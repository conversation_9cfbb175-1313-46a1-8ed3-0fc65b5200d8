/**
 * Echarts自定义组件控制器
 * 参考G2plot自定义组件实现，支持全局变量和动态样式
 */

import {ComponentBaseProps} from "../../common-component/CommonTypes.ts";
import * as echarts from "echarts";
import {ECharts} from "echarts";
import {UpdateOptions} from "../../../framework/core/AbstractController";
import {AntdBaseDesignerController} from "../../antd-common/AntdBaseDesignerController";
import ObjectUtil from "../../../utils/ObjectUtil.ts";
import globalVariableManager from "../../../designer/manager/GlobalVariableManager.ts";
import GlobalVariableParser from "../../../utils/GlobalVariableParser.ts";

export interface EchartsCustomStyle {
    customCode: string;
    // 支持动态样式配置，这些配置会传递给图表实例
    [key: string]: any;
}

export interface EchartsCustomProps extends ComponentBaseProps {
    style?: EchartsCustomStyle;
}

export default class EchartsCustomController extends AntdBaseDesignerController<any, EchartsCustomProps> {

    private globalVariableSubscriptions: Array<{variableId: string, subscriberId: string}> = [];

    async create(container: HTMLElement, config: EchartsCustomProps): Promise<void> {
        this.container = container;
        this.config = config;
        const customCode = config.style?.customCode;
        if (!customCode || customCode === "")
            return;

        // 创建图表实例
        await this.createChartInstance(customCode);

        // 订阅全局变量变化
        this.subscribeToGlobalVariables(customCode);

        //注册蓝图-暂不处理
        // this.registerEvent();
    }

    /**
     * 创建图表实例
     */
    private async createChartInstance(customCode: string): Promise<void> {
        try {
            // 解析自定义代码中的 ${GV::variableName} 引用
            const parsedCode = await GlobalVariableParser.parseAndReplace(
                customCode,
                globalVariableManager,
                undefined,
                'javascript'
            );

            // 创建全局变量访问对象
            const globalVars = {
                get: (name: string) => globalVariableManager.getVariableValueByName(name)
            };

            // 创建动态样式配置对象（排除customCode）
            const dynamicStyleConfig = { ...this.config?.style };
            delete dynamicStyleConfig.customCode;

            // 执行自定义代码
            const func = eval(`(${parsedCode})`);
            if (typeof func !== 'function')
                return;

            // 传入参数：容器、echarts库、数据、全局变量、动态样式配置
            this.instance = func(this.container, echarts, this.config?.data?.staticData, globalVars, dynamicStyleConfig);
        } catch (error) {
            console.error('Echarts自定义组件创建失败:', error);
        }
    }

    /**
     * 订阅全局变量变化
     */
    private subscribeToGlobalVariables(customCode: string): void {
        if (!customCode) return;

        // 解析代码中引用的全局变量
        const referencedVariables = this.extractGlobalVariableReferences(customCode);

        // 订阅这些变量的变化
        referencedVariables.forEach(variableName => {
            const variableId = this.findVariableIdByName(variableName);
            if (variableId) {
                const componentId = this.config?.base?.id || `echarts-custom-${Date.now()}`;
                const subscriberId = `${componentId}-${variableId}`;

                globalVariableManager.subscribe(variableId, subscriberId, () => {
                    // 全局变量变化时重新创建图表
                    this.recreateChart();
                });

                this.globalVariableSubscriptions.push({variableId, subscriberId});
            }
        });
    }

    /**
     * 从代码中提取全局变量引用
     */
    private extractGlobalVariableReferences(code: string): string[] {
        const references = new Set<string>();
        
        // 匹配 globalVars.get('variableName') 或 globalVars.get("variableName")
        const apiMatches = code.match(/globalVars\.get\(['"]([^'"]+)['"]\)/g);
        if (apiMatches) {
            apiMatches.forEach(match => {
                const variableName = match.match(/globalVars\.get\(['"]([^'"]+)['"]\)/)?.[1];
                if (variableName) references.add(variableName);
            });
        }

        // 匹配 ${GV::variableName} 模板语法
        const templateMatches = code.match(/\$\{GV::([^}]+)\}/g);
        if (templateMatches) {
            templateMatches.forEach(match => {
                const variableName = match.match(/\$\{GV::([^}]+)\}/)?.[1];
                if (variableName) references.add(variableName);
            });
        }

        return Array.from(references);
    }

    /**
     * 根据变量名查找变量ID
     */
    private findVariableIdByName(variableName: string): string | null {
        const variables = globalVariableManager.getAllVariableDefinitions();
        const variable = variables.find((v: any) => v.name === variableName);
        return variable ? variable.id : null;
    }

    /**
     * 重新创建图表
     */
    private async recreateChart(): Promise<void> {
        const customCode = this.config?.style?.customCode;
        if (!customCode) return;

        // 销毁现有实例
        try {
            this.instance?.dispose();
        } catch (e) {
            // 清理容器
            while (this.container?.firstChild) {
                this.container.removeChild(this.container.firstChild);
            }
        }

        // 重新创建
        await this.createChartInstance(customCode);
    }

    destroy(): void {
        // 取消全局变量订阅
        this.globalVariableSubscriptions.forEach(({variableId, subscriberId}) => {
            globalVariableManager.unsubscribe(variableId, subscriberId);
        });
        this.globalVariableSubscriptions = [];

        // 销毁图表实例
        this.instance?.dispose();
        this.instance = null;
        this.config = null;
        this.interval && clearInterval(this.interval);
    }

    getConfig(): EchartsCustomProps | null {
        return this.config;
    }

    async update(config: EchartsCustomProps, upOp?: UpdateOptions): Promise<void> {
        // 合并最新的配置
        const oldConfig = { ...this.config };
        this.config = ObjectUtil.merge(this.config, config);
        upOp = upOp || {reRender: true};

        if (upOp.reRender) {
            const customCode = this.config?.style?.customCode;
            const oldCustomCode = oldConfig?.style?.customCode;

            // 检查是否只是样式配置更新（不是customCode更新）
            const isOnlyStyleUpdate = customCode === oldCustomCode &&
                                    customCode &&
                                    config.style &&
                                    !config.style.customCode;

            if (isOnlyStyleUpdate && this.instance) {
                // 只更新样式，不重新创建图表
                await this.recreateChart();
                return;
            }

            if (!customCode || customCode === "") {
                // 取消现有订阅
                this.globalVariableSubscriptions.forEach(({variableId, subscriberId}) => {
                    globalVariableManager.unsubscribe(variableId, subscriberId);
                });
                this.globalVariableSubscriptions = [];
                return;
            }

            // 取消现有的全局变量订阅
            this.globalVariableSubscriptions.forEach(({variableId, subscriberId}) => {
                globalVariableManager.unsubscribe(variableId, subscriberId);
            });
            this.globalVariableSubscriptions = [];

            // 销毁之前的图表实例
            try {
                this.instance?.dispose();
            } catch (e) {
                while (this.container?.firstChild) {
                    this.container.removeChild(this.container.firstChild);
                }
            }

            // 重新创建图表实例和订阅
            await this.createChartInstance(customCode);
            this.subscribeToGlobalVariables(customCode);

            // 触发全局变量引用计数重新计算
            globalVariableManager.recalculateAllReferenceCounts();
        }
    }

    updateStyle(style: EchartsCustomStyle): void {
        if (this.config?.style) {
            this.config.style = ObjectUtil.merge(this.config.style, style);
            this.recreateChart();
        }
    }

    changeData(data: any): void {
        // 更新配置中的静态数据
        if (this.config?.data) {
            this.config.data.staticData = data;
        }

        // 重新创建图表以使用新数据
        this.recreateChart();
    }
}

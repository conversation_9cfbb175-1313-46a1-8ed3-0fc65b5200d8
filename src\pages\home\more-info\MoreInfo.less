/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.more-info-list {
  padding: 15px;
  color: white;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  grid-gap: 25px;
  font-size: 20px;
  font-family: '庞门正道标题体免费版', serif;

  .more-info-item {
    padding: 10px;
    position: relative;
    height: 160px;
    background-color: #00477a;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    transition: transform 0.3s, box-shadow 0.3s;
  }

  .more-info-item:nth-child(1) {
    background-image: url('./image/video.png');
  }

  .more-info-item:nth-child(2) {
    background-image: url('./image/document.png');
  }

  .more-info-item:nth-child(3) {
    background-image: url('./image/github.png');
  }

  .more-info-item:nth-child(4) {
    background-image: url('./image/gitee.png');
  }


  .more-info-item:hover {
    cursor: pointer;
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(9, 91, 110, 0.53);
  }
}
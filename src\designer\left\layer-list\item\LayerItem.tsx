/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {BaseLayer} from "./BaseLayer";
import {Edit, Lock, PreviewClose, PreviewOpen, Unlock} from "@icon-park/react";

class LayerItem extends BaseLayer {
    render() {
        const {name = '', inputMode, lock = false, hide = false, selected = false} = this.state || {};
        const { isOver, canDrop, dropPosition } = this.props;

        // 计算拖拽预览样式
        const getDropIndicatorStyle = () => {
            if (!isOver || !canDrop || !dropPosition) return {};

            const baseStyle = {
                position: 'absolute' as const,
                left: 0,
                right: 0,
                height: '2px',
                backgroundColor: '#1890ff',
                zIndex: 1000,
            };

            if (dropPosition === 'top') {
                return { ...baseStyle, top: '-1px' };
            } else {
                return { ...baseStyle, bottom: '-1px' };
            }
        };

        return (
            <div
                className={`layer-item ${selected ? "layer-selected" :
                    hide ? "layer-hide" : lock ? "layer-lock" : ""} ${this.props.isDragging ? "layer-dragging" : ""}`}
                onClick={(e) => {
                    // 调用onSelected方法，但不阻止事件冒泡
                    this.onSelected(e);
                }}
                onDoubleClick={this.activeComponentConfig}
                ref={this.props.dragRef}
                style={{
                    cursor: lock || hide ? 'default' : 'move',
                    position: 'relative'
                }}
            >
                {/* 拖拽预览指示线 */}
                {isOver && canDrop && dropPosition && (
                    <div style={getDropIndicatorStyle()} />
                )}
                <div className={'layer-name'}>
                    {inputMode ? <input type="text" defaultValue={name} autoFocus={true} onChange={this.changeLayerName}
                                        ref={ref => ref?.select()}
                                        onKeyDown={(e) => {
                                            if (e.code === "Enter")
                                                this.closeInput();
                                        }}
                                        onBlur={this.closeInput}/> : name}
                </div>
                <div className={'layer-operators'}>
                    <div className={'layer-operator'} onClick={this.openInput}>
                        <Edit size={14}/>
                    </div>
                    <div className={'layer-operator'} onClick={this.toggleHide}>
                        {hide ? <PreviewClose size={14}/> : <PreviewOpen size={14}/>}
                    </div>
                    <div className={'layer-operator'} onClick={this.toggleLock}>
                        {lock ? <Lock size={14}/> : <Unlock size={14}/>}
                    </div>
                </div>
            </div>
        );
    }
}

export default LayerItem;
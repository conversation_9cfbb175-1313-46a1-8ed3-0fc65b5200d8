/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import { ComponentBaseProps } from "../../common-component/CommonTypes";
import { ChildTabStyle } from "./CustomTabGroupTypes";

// 子Tab组件的参数
export interface ChildTabComponentProps extends ComponentBaseProps {
    style: ChildTabStyle;
}

// 子Tab组件Ref接口
export interface ChildTabComponentRef {
    updateConfig: (config: ChildTabComponentProps) => void;
} 
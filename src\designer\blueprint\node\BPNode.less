/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.bp-node {
  font-size: 12px;
  width: 160px;
  min-height: 60px;
  background-color: #333333;
  border-radius: 5px;
  box-sizing: border-box;
  padding: 5px;
  color: #fff;

  .bp-node-header {
    padding: 8px 7px;
    border-radius: 2px;
    display: flex;
    align-items: center;

    .bp-node-icon {
      position: relative;
      top: 2px;
    }
  }

  .bp-node-body {
    color: #adadad;

    .bp-node-ap:hover {
      background-color: #4b4b4b;
    }

    .bp-node-ap {
      display: flex;
      padding: 8px 0;
      box-sizing: border-box;
      border-radius: 5px;
      transition: background-color 0.3s ease;

      .bp-node-ap-circle {
        span {
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          border: 2px solid #b4b4b4;
          position: relative;
          background-color: #333333;
        }

        span:hover {
          border: 4px solid #d6d6d6;
          background-color: #7b7b7b;
          cursor: pointer;
        }

        .ap-circle-input {
          flex-direction: row;
          left: -10px;
        }

        .ap-circle-output {
          flex-direction: row-reverse;
          right: -10px;
        }
      }
    }

    .node-ap-input {
      flex-direction: row;
    }

    .node-ap-output {
      flex-direction: row-reverse;
    }
  }
}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractDesignerDefinition from "../../../framework/core/AbstractDesignerDefinition";
import {BaseInfoType, MenuToConfigMappingType} from "../../../framework/core/AbstractDefinition";
import {ClazzTemplate} from "../../common-component/CommonTypes";
import {MenuInfo} from "../../../designer/right/MenuType";
import {ChildTabController} from "./ChildTabController";
import {ChildTabComponentProps} from "./CustomTabGroupTypes";
import BaseInfo from "../../common-component/base-info/BaseInfo";
import ChildTabConfig from "./ChildTabConfig";
import ChildTabConvert from "./ChildTabConvert";
import AbstractConvert from "../../../framework/convert/AbstractConvert";

export default class ChildTabDefinition extends AbstractDesignerDefinition<ChildTabController, ChildTabComponentProps> {

    // 子Tab不应该在蓝图中显示，因为它们是Tab组的内部实现细节
    public isBlueprintVisible: boolean = false;

    getBaseInfo(): BaseInfoType {
        return {
            compName: "子Tab",
            compKey: "ChildTab",
            // 不设置categorize，使其不显示在组件库中
        };
    }

    getChartImg(): string | null {
        return null;
    }

    getController(): ClazzTemplate<ChildTabController> | null {
        return ChildTabController;
    }

    getInitConfig(): ChildTabComponentProps {
        return {
            base: {
                id: "",
                name: '子Tab',
                type: 'ChildTab',
            },
            style: {
                parentId: '',
                value: '',
                isActive: false,
                activeStyle: {
                    background: '#1890ff',
                    color: '#ffffff',
                    borderColor: '#1890ff',
                    borderWidth: 1,
                    borderRadius: 4,
                    fontSize: 14,
                    fontWeight: 'normal',
                    padding: '8px 16px',
                    textAlign: 'center',
                    backgroundImage: {
                        type: 'local',
                        localUrl: '',
                        onLineUrl: '',
                        hash: ''
                    }
                },
                inactiveStyle: {
                    background: '#f5f5f5',
                    color: '#666666',
                    borderColor: '#d9d9d9',
                    borderWidth: 1,
                    borderRadius: 4,
                    fontSize: 14,
                    fontWeight: 'normal',
                    padding: '8px 16px',
                    textAlign: 'center',
                    backgroundImage: {
                        type: 'local',
                        localUrl: '',
                        onLineUrl: '',
                        hash: ''
                    }
                }
            },
        };
    }

    getMenuList(): Array<MenuInfo> {
        return super.getMenuList().filter((menu) => 
            menu.key !== 'data' && 
            menu.key !== 'theme' && 
            menu.key !== 'animation' && 
            menu.key !== 'filter' &&
            menu.key !== 'mapping'
        );
    }

    /**
     * 获取转换器类，用于处理背景图片的保存和加载
     */
    getConvert(): ClazzTemplate<AbstractConvert<ChildTabComponentProps>> | null {
        return ChildTabConvert;
    }

    getMenuToConfigContentMap(): MenuToConfigMappingType {
        return {
            base: BaseInfo,
            style: ChildTabConfig,
        };
    }
}

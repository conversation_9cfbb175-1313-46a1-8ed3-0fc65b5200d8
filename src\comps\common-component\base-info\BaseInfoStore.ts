/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React from "react";
import {ILayerItem} from "../../../designer/DesignerType";
import rightStore from "../../../designer/right/RightStore";

/**
 * 这个store不与mobx结合
 */
class BaseInfoStore {
    /**
     * 基础配置组件的实例引用
     */
    baseConfigRef: React.Component | null = null;

    setBaseConfigRef = (ref: React.Component | null) => this.baseConfigRef = ref;

    updateBaseConfig = (data: ILayerItem) => {
        const {activeElem: {id}} = rightStore;
        if (id !== data?.id) return;
        this.baseConfigRef?.setState(data);
    }

}

const baseInfoStore = new BaseInfoStore();
export default baseInfoStore;
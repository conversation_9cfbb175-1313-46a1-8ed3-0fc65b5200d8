/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

/**
 * 数据类型处理工具类
 * 用于处理静态数据配置、API响应、数据库查询结果等各种数据类型的智能转换和格式化
 */
export default class DataTypeUtil {

    /**
     * 严格解析数据，要求输入符合JSON格式
     * @param data 原始数据
     * @returns 解析后的数据，如果格式不正确返回错误信息
     */
    public static strictParse(data: any): {success: boolean, data?: any, error?: string} {
        // 如果已经是对象、数组、数字、布尔值，直接返回
        if (typeof data !== 'string') {
            return {success: true, data: data};
        }

        const trimmedData = data.trim();

        // 空字符串需要用引号
        if (trimmedData === '') {
            return {success: false, error: '空字符串请使用 "" 表示'};
        }

        // 检查是否是有效的JSON
        try {
            const parsed = JSON.parse(trimmedData);
            return {success: true, data: parsed};
        } catch (error) {
            // 检查是否是未加引号的字符串
            if (!trimmedData.startsWith('"') && !trimmedData.startsWith("'") &&
                !trimmedData.startsWith('{') && !trimmedData.startsWith('[') &&
                trimmedData !== 'null' && trimmedData !== 'true' && trimmedData !== 'false' &&
                !/^-?\d+(\.\d+)?([eE][+-]?\d+)?$/.test(trimmedData)) {
                return {success: false, error: `字符串请使用引号包围，如: "${trimmedData}"`};
            }
            return {success: false, error: `JSON格式错误: ${error instanceof Error ? error.message : '未知错误'}`};
        }
    }

    /**
     * 智能解析数据，支持字符串、数字、布尔值、JSON对象、数组等（向后兼容）
     * @param data 原始数据
     * @returns 解析后的数据
     */
    public static smartParse(data: any): any {
        // 如果已经是对象、数组、数字、布尔值，直接返回
        if (typeof data !== 'string') {
            return data;
        }

        const trimmedData = data.trim();

        // 空字符串
        if (trimmedData === '') {
            return '';
        }

        // null
        if (trimmedData === 'null') {
            return null;
        }

        // undefined
        if (trimmedData === 'undefined') {
            return undefined;
        }

        // 布尔值
        if (trimmedData === 'true') {
            return true;
        }
        if (trimmedData === 'false') {
            return false;
        }

        // 纯数字（整数）
        if (/^-?\d+$/.test(trimmedData)) {
            return parseInt(trimmedData, 10);
        }

        // 纯数字（浮点数）
        if (/^-?\d+\.\d+$/.test(trimmedData)) {
            return parseFloat(trimmedData);
        }

        // 科学计数法
        if (/^-?\d+(\.\d+)?[eE][+-]?\d+$/.test(trimmedData)) {
            return parseFloat(trimmedData);
        }

        // JSON对象或数组
        if ((trimmedData.startsWith('{') && trimmedData.endsWith('}')) ||
            (trimmedData.startsWith('[') && trimmedData.endsWith(']'))) {
            try {
                return JSON.parse(trimmedData);
            } catch (error) {
                // JSON解析失败，当作普通字符串处理
                return trimmedData;
            }
        }

        // 带引号的字符串
        if ((trimmedData.startsWith('"') && trimmedData.endsWith('"')) ||
            (trimmedData.startsWith("'") && trimmedData.endsWith("'"))) {
            try {
                return JSON.parse(trimmedData);
            } catch (error) {
                // 去掉引号返回
                return trimmedData.slice(1, -1);
            }
        }

        // 其他情况当作普通字符串
        return trimmedData;
    }

    /**
     * 格式化数据为JSON字符串，用于显示
     * @param data 要格式化的数据
     * @param indent 缩进空格数，默认2
     * @param alwaysShowJsonFormat 是否总是显示JSON格式（推荐），默认true
     * @returns 格式化后的JSON字符串
     */
    public static formatForDisplay(data: any, indent: number = 2, alwaysShowJsonFormat: boolean = true): string {
        if (alwaysShowJsonFormat) {
            // 总是使用JSON格式显示，确保一致性
            try {
                return JSON.stringify(data, null, indent);
            } catch (error) {
                return `"[Error: ${error instanceof Error ? error.message : '无法序列化'}]"`;
            }
        } else {
            // 向后兼容的显示方式
            if (data === null) {
                return 'null';
            }
            if (data === undefined) {
                return 'undefined';
            }
            if (typeof data === 'string') {
                return data;
            }
            if (typeof data === 'number' || typeof data === 'boolean') {
                return String(data);
            }
            try {
                return JSON.stringify(data, null, indent);
            } catch (error) {
                return String(data);
            }
        }
    }

    /**
     * 格式化数据为代码编辑器显示（总是JSON格式）
     * @param data 要格式化的数据
     * @param indent 缩进空格数，默认2
     * @returns 格式化后的JSON字符串
     */
    public static formatForEditor(data: any, indent: number = 2): string {
        try {
            return JSON.stringify(data, null, indent);
        } catch (error) {
            // 如果无法序列化，返回错误提示
            return `"[Error: 无法序列化数据 - ${error instanceof Error ? error.message : '未知错误'}]"`;
        }
    }

    /**
     * 验证数据是否为有效的JSON格式
     * @param data 要验证的数据
     * @returns 是否为有效JSON
     */
    public static isValidJSON(data: string): boolean {
        if (typeof data !== 'string') {
            return false;
        }
        try {
            JSON.parse(data);
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 安全的JSON解析，解析失败时返回默认值
     * @param data 要解析的JSON字符串
     * @param defaultValue 解析失败时的默认值
     * @returns 解析结果或默认值
     */
    public static safeJSONParse(data: string, defaultValue: any = null): any {
        if (typeof data !== 'string') {
            return data;
        }
        try {
            return JSON.parse(data);
        } catch (error) {
            return defaultValue;
        }
    }

    /**
     * 检测数据类型
     * @param data 要检测的数据
     * @returns 数据类型字符串
     */
    public static getDataType(data: any): string {
        if (data === null) return 'null';
        if (data === undefined) return 'undefined';
        if (Array.isArray(data)) return 'array';
        if (typeof data === 'object') return 'object';
        return typeof data;
    }

    /**
     * 深度克隆数据
     * @param data 要克隆的数据
     * @returns 克隆后的数据
     */
    public static deepClone(data: any): any {
        if (data === null || typeof data !== 'object') {
            return data;
        }
        if (data instanceof Date) {
            return new Date(data.getTime());
        }
        if (Array.isArray(data)) {
            return data.map(item => this.deepClone(item));
        }
        if (typeof data === 'object') {
            const cloned: any = {};
            for (const key in data) {
                if (data.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(data[key]);
                }
            }
            return cloned;
        }
        return data;
    }

    /**
     * 比较两个数据是否相等（深度比较）
     * @param data1 数据1
     * @param data2 数据2
     * @returns 是否相等
     */
    public static isEqual(data1: any, data2: any): boolean {
        if (data1 === data2) return true;
        if (data1 === null || data2 === null) return false;
        if (data1 === undefined || data2 === undefined) return false;
        if (typeof data1 !== typeof data2) return false;
        
        if (Array.isArray(data1) && Array.isArray(data2)) {
            if (data1.length !== data2.length) return false;
            for (let i = 0; i < data1.length; i++) {
                if (!this.isEqual(data1[i], data2[i])) return false;
            }
            return true;
        }
        
        if (typeof data1 === 'object' && typeof data2 === 'object') {
            const keys1 = Object.keys(data1);
            const keys2 = Object.keys(data2);
            if (keys1.length !== keys2.length) return false;
            for (const key of keys1) {
                if (!keys2.includes(key)) return false;
                if (!this.isEqual(data1[key], data2[key])) return false;
            }
            return true;
        }
        
        return false;
    }

    /**
     * 数据验证：检查数据是否符合预期格式
     * @param data 要验证的数据
     * @param expectedType 期望的数据类型 ('string' | 'number' | 'boolean' | 'array' | 'object' | 'null')
     * @returns 验证结果
     */
    public static validateDataType(data: any, expectedType: string): boolean {
        const actualType = this.getDataType(data);
        return actualType === expectedType;
    }

    /**
     * 数据清理：移除对象中的空值、undefined值
     * @param data 要清理的数据
     * @param removeEmpty 是否移除空字符串，默认false
     * @returns 清理后的数据
     */
    public static cleanData(data: any, removeEmpty: boolean = false): any {
        if (data === null || data === undefined) {
            return data;
        }
        
        if (Array.isArray(data)) {
            return data
                .map(item => this.cleanData(item, removeEmpty))
                .filter(item => item !== undefined && item !== null && (!removeEmpty || item !== ''));
        }
        
        if (typeof data === 'object') {
            const cleaned: any = {};
            for (const key in data) {
                if (data.hasOwnProperty(key)) {
                    const value = this.cleanData(data[key], removeEmpty);
                    if (value !== undefined && value !== null && (!removeEmpty || value !== '')) {
                        cleaned[key] = value;
                    }
                }
            }
            return cleaned;
        }
        
        return data;
    }
}

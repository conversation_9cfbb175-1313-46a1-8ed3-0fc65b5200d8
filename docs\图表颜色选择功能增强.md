# 图表颜色选择功能增强

## 需求描述

当前项目在对于图表的编辑时，进行颜色类型选择有单色和多色两种选择，但是对于颜色选择的时候，只能选择单个颜色。条形图和柱状图已经实现了单色、线形渐变、径向渐变三种选择，需要为其他图表类型也实现相同的功能。

## 功能列表

- [√] 条形图 (AntdBarCommonConfig.tsx) 颜色选择增强
- [√] 柱状图 (AntdColumnCommonConfig.tsx) 颜色选择增强
- [√] 饼图 (AntdPieStyleConfig.tsx) 颜色选择增强
- [√] 折线图 (AntdLineCommonConfig.tsx) 颜色选择增强
- [√] 散点图 (AntdScatterCommonConfig.tsx) 颜色选择增强
- [√] 面积图 (AntdAreaCommonConfig.tsx) 颜色选择增强
- [√] 玫瑰图 (AntdRoseCommonConfig.tsx) 颜色选择增强
- [√] 玉玦图 (AntdBaseRadialBarStyleConfig.tsx) 颜色选择增强
- [ ] 词云图 (AntdWordCloudController.ts) 颜色选择增强
- [√] 雷达图 (AntdRadarController.ts) 颜色选择增强
- [√] 进度图 (AntdRingProgressConfig.tsx) 颜色选择增强
- [√] 仪表盘 (AntdGaugeConfig.tsx) 颜色选择增强
- [√] 水波图 (AntdLiquidConfig.tsx) 颜色选择增强

## 技术方案

1. 将现有的 `color-mode` 或 `color-picker` 组件替换为 `enhanced-color-mode` 组件
2. 修改对应的 `onFieldChange` 处理函数，处理渐变色的情况
3. 确保控制器能正确处理渐变色数据

## 实现细节

### 颜色处理逻辑

对于每个图表类型，需要处理以下几种情况：

1. 单色模式 + 单色：直接使用颜色值
2. 单色模式 + 渐变色：使用 `ColorUtil.gradientToCss()` 转换为渐变字符串
3. 多色模式 + 纯色数组：直接使用颜色数组
4. 多色模式 + 包含渐变色的数组：将渐变色对象转换为渐变字符串

### 参考实现

参考条形图和柱状图的实现方式：

```typescript
if (id === 'barColor') {
    if (data && Array.isArray(data)) {
        // 多色模式
        // 检查数组中是否有渐变对象
        const hasGradient = data.some(color =>
            typeof color === 'object' && color !== null && 'type' in color &&
            (color.type === 'linear-gradient' || color.type === 'radial-gradient')
        );

        if (hasGradient) {
            // 如果数组中有渐变对象，我们需要将它们转换为渐变字符串
            const processedColors = data.map(color => {
                if (typeof color === 'object' && color !== null && 'type' in color &&
                    (color.type === 'linear-gradient' || color.type === 'radial-gradient')) {
                    return ColorUtil.gradientToCss(color);
                }
                return color;
            });
            controller.update({style: {color: processedColors as ColorAttr, barStyle: {fill: undefined}}});
        } else {
            // 如果数组中没有渐变对象，直接使用原始数组
            controller.update({style: {color: data as ColorAttr, barStyle: {fill: undefined}}});
        }
    } else if (data && typeof data === 'object' && data !== null && 'type' in data &&
              (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
        // 处理单个渐变对象
        const gradientCss = ColorUtil.gradientToCss(data);
        controller.update({style: {barStyle: {fill: gradientCss}}});
    } else if (data && typeof data === 'string' && data.indexOf('gradient') !== -1) {
        // 处理渐变字符串
        controller.update({style: {barStyle: {fill: data as string}}});
    } else {
        controller.update({style: {barStyle: {fill: data as string}}});
    }
}
```

## 修改记录

### 2025-05-15
- 完成折线图颜色选择增强
  - 修改线条颜色选择器为增强型颜色选择器
  - 修改数据点颜色选择器为增强型颜色选择器
  - 修改轴线、网格线、刻度线颜色选择器为增强型颜色选择器

### 2025-05-15
- 完成图例颜色选择增强
  - 修改图例文本颜色选择器为增强型颜色选择器
  - 处理图例文本颜色的渐变色情况

### 2025-05-15
- 完成饼图颜色选择增强
  - 修改扇区颜色选择器为增强型颜色选择器
  - 修改描边颜色选择器为增强型颜色选择器
  - 修改标签文本颜色选择器为增强型颜色选择器
  - 修改标题颜色选择器为增强型颜色选择器
  - 修改内容颜色选择器为增强型颜色选择器
  - 处理所有颜色配置项的渐变色情况

### 2025-05-15
- 完成散点图颜色选择增强
  - 修改散点颜色选择器为增强型颜色选择器
  - 修改描边颜色选择器为增强型颜色选择器
  - 处理所有颜色配置项的渐变色情况
- 完成面积图颜色选择增强
  - 修改数据面颜色选择器为增强型颜色选择器
  - 修改数据线颜色选择器为增强型颜色选择器
  - 修改数据点颜色选择器为增强型颜色选择器
  - 修改数据点描边颜色选择器为增强型颜色选择器
  - 处理所有颜色配置项的渐变色情况
- 完成基础面积图颜色选择增强
  - 修改数据面颜色选择器为增强型颜色选择器
  - 修改数据线颜色选择器为增强型颜色选择器
  - 修改数据点填充颜色选择器为增强型颜色选择器
  - 修改数据点描边颜色选择器为增强型颜色选择器
  - 处理所有颜色配置项的渐变色情况

### 2025-05-15
- 完成雷达图颜色选择增强
  - 修改辅助点颜色选择器为增强型颜色选择器
  - 修改辅助点边框颜色选择器为增强型颜色选择器
  - 修改面积边框颜色选择器为增强型颜色选择器
  - 修改雷达面颜色选择器为增强型颜色选择器
  - 处理所有颜色配置项的渐变色情况
- 完成进度图颜色选择增强
  - 修改进度条颜色选择器为增强型颜色选择器
  - 修改描边颜色选择器为增强型颜色选择器
  - 修改标题颜色选择器为增强型颜色选择器
  - 修改内容颜色选择器为增强型颜色选择器
  - 处理所有颜色配置项的渐变色情况
- 完成仪表盘颜色选择增强
  - 修改刻度范围颜色选择器为增强型颜色选择器
  - 修改指针颜色选择器为增强型颜色选择器
  - 修改指针原点描边颜色选择器为增强型颜色选择器
  - 修改指针原点填充颜色选择器为增强型颜色选择器
  - 修改主刻度颜色选择器为增强型颜色选择器
  - 修改子刻度颜色选择器为增强型颜色选择器
  - 修改文本颜色选择器为增强型颜色选择器
  - 处理所有颜色配置项的渐变色情况
- 完成水波图颜色选择增强
  - 修改水波颜色选择器为增强型颜色选择器
  - 修改水波描边颜色选择器为增强型颜色选择器
  - 修改图形样式颜色选择器为增强型颜色选择器
  - 修改外边框颜色选择器为增强型颜色选择器
  - 修改标题颜色选择器为增强型颜色选择器
  - 修改内容颜色选择器为增强型颜色选择器
  - 处理所有颜色配置项的渐变色情况

### 2025-05-15
- 完成玫瑰图颜色选择增强
  - 修改扇区颜色选择器为增强型颜色选择器
  - 修改扇区描边颜色选择器为增强型颜色选择器
  - 修改标签颜色选择器为增强型颜色选择器
  - 处理所有颜色配置项的渐变色情况
- 完成玉玦图颜色选择增强
  - 修改柱体颜色选择器为增强型颜色选择器
  - 修改文本颜色选择器为增强型颜色选择器
  - 处理所有颜色配置项的渐变色情况

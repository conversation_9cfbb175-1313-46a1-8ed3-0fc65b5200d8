/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React from 'react';
import {ConfigType} from "../../../designer/right/ConfigContent.tsx";
import {FinanceTableController} from "./FinanceTableController.ts";

export const FinanceTableStyleConfig: React.FC<ConfigType<FinanceTableController>> = () => {
    return (
        <div style={{
            padding: '20px',
            textAlign: 'center',
            color: '#666',
            fontSize: '14px'
        }}>
            <h3>财务数据表格</h3>
            <p>此组件无需配置，样式已固定。</p>
            <p>只需要通过数据源传入符合格式的数据即可。</p>
            <div style={{
                marginTop: '20px',
                padding: '15px',
                background: '#f5f5f5',
                borderRadius: '4px',
                textAlign: 'left'
            }}>
                <h4>数据格式：</h4>
                <pre style={{
                    background: '#fff',
                    padding: '10px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    overflow: 'auto'
                }}>
{`{
  "header": ["项目", "2025预算", "1季度完成", "4月完成", "5月完成", "6月完成", "累计完成", "累计完成率"],
  "body": [
    ["营业收入", 25985, 3456, 43, 665, 12, 8887, "25.6%"],
    ["营业成本", 18500, 2800, 35, 520, 8, 6200, "33.5%"]
  ]
}`}
                </pre>
            </div>
            <div style={{
                marginTop: '15px',
                padding: '15px',
                background: '#e6f7ff',
                borderRadius: '4px',
                textAlign: 'left'
            }}>
                <h4>固定样式：</h4>
                <ul style={{ margin: 0, paddingLeft: '20px' }}>
                    <li>表头：高度80px，字号18px，加粗900</li>
                    <li>表体：每行64px，字号18px，加粗500</li>
                    <li>第一列：宽度250px，表头居中，表体左对齐</li>
                    <li>其他列：均分宽度，表头表体右对齐</li>
                    <li>奇偶行：奇数行#f0f7fa，偶数行透明</li>
                    <li>轮播：超过5行自动启用</li>
                </ul>
            </div>
        </div>
    );
}

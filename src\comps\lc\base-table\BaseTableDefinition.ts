/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {BaseInfoType, EventInfo, MenuToConfigMappingType} from "../../../framework/core/AbstractDefinition";
import {ClazzTemplate} from "../../common-component/CommonTypes.ts";
import {MenuInfo} from "../../../designer/right/MenuType";
import baseTableImg from './base-table.png';
import {BaseTableController} from "./BaseTableController";
import {BaseTableComponentProps} from "./BaseTableComponent";
import {BaseTableStyleConfig} from "./BaseTableConfig";
import AbstractDesignerDefinition from "../../../framework/core/AbstractDesignerDefinition.ts";

export default class BaseTableDefinition extends AbstractDesignerDefinition<BaseTableController, BaseTableComponentProps> {
    getBaseInfo(): BaseInfoType {
        return {
            compName: "基础表格",
            compKey: "LcBaseTable",
            categorize: "web",
        };
    }

    getChartImg(): string | null {
        return baseTableImg;
    }

    getController(): ClazzTemplate<BaseTableController> | null {
        return BaseTableController;
    }

    getInitConfig(): BaseTableComponentProps {
        const data = [
            {name: '张三', age: 18, sex: '男', status: 'active', progress: 85},
            {name: '李四', age: 20, sex: '女', status: 'pending', progress: 60},
            {name: '王五', age: 22, sex: '男', status: 'inactive', progress: 30}
        ];
        return {
            base: {
                id: "",
                name: '基础表格',
                type: 'LcBaseTable',
            },
            style: {
                columns: [
                    {
                        key: 'name',
                        label: '姓名',
                        width: undefined,
                        textAlign: 'center',
                        enableCustomCode: false,
                        customCode: `function(data, rowData, rowIndex) {
    // 可用组件：antd.Button, antd.Tag, antd.Badge, antd.Progress, antd.Tooltip 等
    // 可用工具：globalVars.get(name)

    const name = rowData.name;
    return (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
            {name}
        </span>
    );
}`,
                    },
                    {
                        key: 'age',
                        label: '年龄',
                        width: undefined,
                        textAlign: 'center',
                        enableCustomCode: false,
                        customCode: `function(data, rowData, rowIndex) {
    // 可用组件：antd.Button, antd.Tag, antd.Badge, antd.Progress, antd.Tooltip 等
    // 可用工具：globalVars.get(name)

    const age = rowData.age;
    const color = age >= 30 ? '#f50' : '#87d068';
    return (
        <antd.Tag color={color}>
            {age}岁
        </antd.Tag>
    );
}`,
                    },
                    {
                        key: 'sex',
                        label: '性别',
                        width: undefined,
                        textAlign: 'center',
                        enableCustomCode: false,
                        customCode: `function(data, rowData, rowIndex) {
    // 可用组件：antd.Button, antd.Tag, antd.Badge, antd.Progress, antd.Tooltip 等
    // 可用工具：globalVars.get(name)

    const sex = rowData.sex;
    const color = sex === '男' ? '#1890ff' : '#eb2f96';
    const icon = sex === '男' ? '♂' : '♀';
    return (
        <span style={{ color, fontWeight: 'bold' }}>
            {icon} {sex}
        </span>
    );
}`,
                    },
                    {
                        key: 'status',
                        label: '状态',
                        width: undefined,
                        textAlign: 'center',
                        enableCustomCode: false,
                        customCode: `function(data, rowData, rowIndex) {
    const status = rowData.status;
    if (status === 'active') {
        return <antd.Tag color="green">激活</antd.Tag>;
    } else if (status === 'pending') {
        return <antd.Tag color="orange">待审核</antd.Tag>;
    } else {
        return <antd.Tag color="red">禁用</antd.Tag>;
    }
}`,
                    },
                    {
                        key: 'progress',
                        label: '进度',
                        width: undefined,
                        textAlign: 'center',
                        enableCustomCode: false,
                        customCode: `function(data, rowData, rowIndex) {
    const progress = rowData.progress || 0;
    return (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <antd.Progress
                percent={progress}
                size="small"
                status={progress === 100 ? 'success' : 'active'}
                style={{ width: '80px' }}
            />
            <span>{progress}%</span>
        </div>
    );
}`,
                    }
                ],
                data,
                header: {
                    height: 40,
                    background: '#0080be',
                    color: '#fff',
                    fontSize: 14,
                    fontWeight: 900,
                },
                body: {
                    background: '#141414',
                    color: '#acacac',
                    fontSize: 14,
                    fontWeight: 500,
                    enableCarousel: false,
                    carouselSpeed: 3,
                }
            },
            filter: {
                enable: false,
                blur: 0,
                brightness: 1,
                contrast: 1,
                opacity: 1,
                saturate: 1,
                hueRotate: 0
            },
            data: {
                sourceType: 'static',
                staticData: data
            },
        };
    }

    getMenuList(): Array<MenuInfo> {
        return super.getMenuList().filter((item: MenuInfo) => item.key !== 'theme');
    }

    getMenuToConfigContentMap(): MenuToConfigMappingType {
        const menuMapping = super.getMenuToConfigContentMap();
        menuMapping['style'] = BaseTableStyleConfig;
        return menuMapping;
    }

    getEventList(): Array<EventInfo> {
        return [
            ...super.getEventList(),
            {
                id: "dataChange",
                name: "数据变更时",
            }
        ]
    }
}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.image-source {
  display: flex;
  flex-direction: column;
  padding: 5px;
  height: 100%;

  .image-source-search {
    margin-bottom: 5px;
  }

  .image-source-list {
    overflow-y: scroll;

    .image-source-item {
      width: 100%;
      height: 140px;
      cursor: pointer;
      margin-bottom: 10px;
      background-color: #363636;

      .image-source-item-header {
        height: 25px;
        line-height: 25px;
        padding: 0 6px;
        color: #d4d4d4;
        font-size: 11px;
        display: flex;
        justify-content: space-between;

        .isi-title {
          width: 130px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .isi-operate:hover {
          transition: color 0.3s ease-in-out;
          color: #30c5ff;
        }
      }

      .image-source-item-body {
        height: 115px;
        padding: 5px 10px 10px;

        .item-bg-image {
          width: 100%;
          height: 100%;
          background-size: cover;
          background-position: center;
        }
      }
    }
  }

}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, { useState } from 'react';
import { ConfigType } from "../../../designer/right/ConfigContent";
import { CustomComponentController } from "./CustomComponentController";
import { globalMessage } from "../../../framework/message/GlobalMessage";
import CustomComponentTransformer from './CustomComponentTransformer';
import CodeEditor from '../../../json-schema/ui/code-editor/CodeEditor';
import { Button, Card, Space } from 'antd';

const CustomComponentConfig: React.FC<ConfigType<CustomComponentController>> = ({ controller }) => {
    const [, setCount] = useState(0);
    const config = controller.getConfig();
    const customCode = config?.customCode || {
        code: `function render(data, globalVars) {
  // 可用组件: Antd.Button, Antd.Tag, Antd.Input 等
  // 可用工具: globalVars.get(name)
  
  return (
    <div>
      <Antd.Typography.Title level={4}>自定义组件</Antd.Typography.Title>
      <Antd.Divider />
      <Antd.Button type="primary">点击按钮</Antd.Button>
    </div>
  );
}`,
        isPreview: false,
    };

    // 验证代码
    const validateCode = () => {
        try {
            // 使用CustomComponentTransformer验证代码语法
            const result = CustomComponentTransformer.validateCode(customCode.code);
            if (result.valid) {
                globalMessage.messageApi?.success('代码语法正确');
            } else {
                globalMessage.messageApi?.error(`代码语法错误: ${result.error}`);
            }
        } catch (error) {
            globalMessage.messageApi?.error(`验证失败: ${error instanceof Error ? error.message : String(error)}`);
        }
    };

    // 处理代码保存事件（Ctrl+S）
    const handleCodeSave = (code: string) => {
        // 更新全局变量引用计数
        CustomComponentTransformer.updateGlobalVariableReferencesOnly(code);
        globalMessage.messageApi?.success('代码已保存，全局变量引用计数已更新');
    };

    // 处理代码变化
    const handleCodeChange = (code?: string) => {
        const updatedConfig = {
            customCode: {
                ...customCode,
                code: code || ''
            }
        };
        // 使用 reRender: false 避免实时渲染
        controller.update(updatedConfig, { reRender: false });
        setCount(prev => prev + 1);
    };

    // 预览功能：每次点击都触发渲染
    const handlePreview = () => {
        const updatedConfig = {
            customCode: {
                ...customCode,
                isPreview: true, // 设置为预览状态
                previewTimestamp: Date.now() // 添加时间戳强制重新渲染
            }
        };
        // 使用 reRender: true 确保组件重新渲染
        controller.update(updatedConfig, { reRender: true });
        setCount(prev => prev + 1);

        // 显示提示信息
        globalMessage.messageApi?.success('预览已更新');
    };

    // 获取默认代码示例
    const getDefaultCode = () => {
        return `function render(data, globalVars, theme) {
  // 可用组件: Antd.Button, Antd.Tag, Antd.Input 等
  // 可用工具: globalVars.get(name)
  // 可用主题: theme.colors.mainText, theme.colors.subText 等

  return (
    <div style={{padding: '20px'}}>
      <Antd.Typography.Title
        level={4}
        style={{color: theme?.colors?.mainText || '#000'}}>
        自定义组件
      </Antd.Typography.Title>
      <Antd.Divider />
      <Antd.Button type="primary">点击按钮</Antd.Button>
    </div>
  );
}`;
    };

    // 重置代码
    const resetCode = () => {
        const updatedConfig = {
            customCode: {
                ...customCode,
                code: getDefaultCode()
            }
        };
        controller.update(updatedConfig);
        setCount(prev => prev + 1);
        globalMessage.messageApi?.info('代码已重置');
    };

    return (
        <div style={{ padding: '10px' }}>
            <Card title="自定义组件代码" style={{ marginBottom: '10px' }}>
                <CodeEditor
                    value={customCode.code || getDefaultCode()}
                    onChange={handleCodeChange}
                    onSave={handleCodeSave}
                    height={400}
                    language="javascript"
                    format={true}
                    fullScreen={true}
                />
            </Card>

            <Space>
                <Button onClick={validateCode}>
                    验证代码
                </Button>
                <Button type="primary" onClick={handlePreview}>
                    预览
                </Button>
                <Button onClick={resetCode}>
                    重置代码
                </Button>
            </Space>
        </div>
    );
};

export default CustomComponentConfig; 
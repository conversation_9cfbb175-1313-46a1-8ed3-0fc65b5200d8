/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.dl-component-list {
  width: 250px;
  border-right: 1px solid #404040;

  .dl-cl-header {
    display: flex;
    justify-content: space-between;
    padding: 13px 10px;
    font-size: 12px;
    height: 47px;
    align-items: center;
    border-bottom: 1px solid #404040;
    color: #bababa;
  }

  .dl-cl-body {
    display: flex;
    height: calc(100% - 47px);

    .main-categories {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: #181818;
      width: 35px;
      font-size: 16px;

      .clo-item {
        &:hover {
          color: #0080ff;
        }

        transition: color 0.3s;
        color: #aaaaaa;
        padding: 5px;
        margin-top: 15px;
        cursor: pointer;
      }

      .clo-item-active {
        color: #0080ff;
      }
    }

    .sub-categories {
      width: 60px;
      height: 100%;
      overflow: scroll;

      .clt-item {
        &:hover {
          color: #3b9bff;
        }

        transition: all 0.2s;
        text-align: center;
        color: #aaaaaa;
        font-size: 12px;
        padding: 10px 5px;
        margin-top: 10px;
        cursor: pointer;
      }

      .clt-item-active {
        color: #0080ff;
      }
    }

    .component-list {
      width: 155px;
    }
  }
}
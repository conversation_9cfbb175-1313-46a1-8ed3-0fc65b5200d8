# G2Plot自定义组件蓝图样式更新测试示例

## 问题描述
用户在蓝图中设计了逻辑处理节点返回`{style:{xAxis:{}}}`，但点击文本时G2Plot组件没有更新。

## 解决方案
修改了G2PlotCustomController以支持动态样式更新，无需重新创建整个图表。

## 测试步骤

### 1. 创建G2Plot自定义组件

使用以下代码作为customCode：

```javascript
function renderG2Plot(container, G2Plot, data, globalVars, dynamicStyle) {
    // 基础配置
    const baseConfig = {
        data: data || [
            { category: 'A', value: 10 },
            { category: 'B', value: 20 },
            { category: 'C', value: 15 }
        ],
        xField: 'category',
        yField: 'value',
        color: '#1890ff',
        xAxis: {
            label: {
                style: {
                    fill: '#333',
                    fontSize: 12
                }
            }
        },
        yAxis: {
            label: {
                style: {
                    fill: '#666',
                    fontSize: 10
                }
            }
        }
    };
    
    // 合并动态样式（关键步骤）
    const finalConfig = dynamicStyle ? 
        Object.assign({}, baseConfig, dynamicStyle) : baseConfig;
    
    console.log('最终配置:', finalConfig);
    
    const g2plot = new G2Plot.Column(container, finalConfig);
    g2plot.render();
    return g2plot;
}
```

### 2. 创建蓝图逻辑

#### 蓝图设计：
1. **基础文本组件** → 点击事件输出锚点
2. **逻辑处理节点** → 处理点击事件，返回样式配置
3. **G2Plot自定义组件** → 接收样式更新

#### 逻辑处理节点代码：
```javascript
function processClick(data) {
    // 生成随机颜色
    const colors = ['#ff4757', '#2ed573', '#3742fa', '#ffa502', '#ff6b7a'];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    
    // 返回样式更新配置
    return {
        style: {
            color: randomColor,
            xAxis: {
                label: {
                    style: {
                        fill: randomColor,
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                }
            },
            yAxis: {
                label: {
                    style: {
                        fill: '#333',
                        fontSize: 12
                    }
                }
            }
        }
    };
}
```

### 3. 测试验证

#### 预期结果：
1. 点击文本组件时，G2Plot图表的颜色应该随机改变
2. X轴标签的颜色和字体大小应该更新
3. 图表不应该闪烁（不重新创建）

#### 调试方法：
1. 打开浏览器开发者工具
2. 查看控制台输出的"最终配置"日志
3. 确认样式配置正确传递和合并

### 4. 高级测试

#### 测试复杂样式更新：
```javascript
function processAdvancedClick(data) {
    return {
        style: {
            // 更新图表颜色
            color: ['#ff6b6b', '#4ecdc4', '#45b7d1'],
            
            // 更新柱状图样式
            columnStyle: {
                fillOpacity: 0.8,
                stroke: '#ffffff',
                lineWidth: 2
            },
            
            // 更新标签
            label: {
                visible: true,
                style: {
                    fill: '#333',
                    fontSize: 10,
                    fontWeight: 'bold'
                }
            },
            
            // 更新坐标轴
            xAxis: {
                label: {
                    style: {
                        fill: '#ff6b6b',
                        fontSize: 14
                    }
                },
                line: {
                    style: {
                        stroke: '#ff6b6b',
                        lineWidth: 2
                    }
                }
            },
            
            yAxis: {
                label: {
                    style: {
                        fill: '#4ecdc4',
                        fontSize: 12
                    }
                },
                grid: {
                    line: {
                        style: {
                            stroke: '#f0f0f0',
                            lineWidth: 1,
                            lineDash: [4, 5]
                        }
                    }
                }
            }
        }
    };
}
```

### 5. 性能测试

#### 测试频繁更新：
```javascript
function processFrequentUpdate(data) {
    // 模拟频繁的样式更新
    const timestamp = Date.now();
    const hue = (timestamp / 10) % 360;
    
    return {
        style: {
            color: `hsl(${hue}, 70%, 50%)`,
            xAxis: {
                label: {
                    style: {
                        fill: `hsl(${hue + 180}, 70%, 40%)`,
                        fontSize: 12 + Math.sin(timestamp / 1000) * 2
                    }
                }
            }
        }
    };
}
```

## 故障排除

### 如果样式更新仍然不生效：

1. **检查customCode函数签名**：
   - 确保函数接收5个参数：`(container, G2Plot, data, globalVars, dynamicStyle)`

2. **检查样式合并逻辑**：
   - 确保使用`Object.assign()`或类似方法合并dynamicStyle

3. **检查返回格式**：
   - 逻辑处理节点必须返回`{style: {...}}`格式

4. **检查控制台错误**：
   - 查看是否有JavaScript错误或警告

5. **测试简单配置**：
   - 先测试简单的颜色更新，再逐步增加复杂配置

### 常见错误：

1. **忘记接收dynamicStyle参数**
2. **没有合并动态样式到最终配置**
3. **返回格式错误（缺少style包装）**
4. **样式配置格式不符合G2Plot规范**

## 总结

通过这次修复，G2Plot自定义组件现在支持：
1. 动态样式更新（无需重新创建图表）
2. 全局变量集成
3. 蓝图事件驱动的样式变化
4. 性能优化的增量更新

/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {useState} from 'react';
import {observer} from "mobx-react";
import {Modal, message, Button} from "antd";
import globalVariableManager from "../../manager/GlobalVariableManager.ts";
import {Control} from "../../../json-schema/SchemaTypes.ts";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI.tsx";
import DataTypeUtil from "../../../utils/DataTypeUtil.ts";
import './GlobalVariablePanel.less';

export const GlobalVariablePanel = observer(() => {
    const {editingDefinition, isPanelVisible, setPanelVisible, setEditingDefinition} = globalVariableManager;
    const [testResult, setTestResult] = useState<string>('');
    const [isTestLoading, setIsTestLoading] = useState<boolean>(false);



    // 表单字段变更处理
    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        if (!editingDefinition) return;

        const {id, data, dataFragment} = fieldChangeData;
        const updatedDefinition = {...editingDefinition};

        // 处理字段变化
        if (id) {
            // 使用id处理单个字段变化
            switch (id) {
                case 'name':
                    updatedDefinition.name = data as string;
                    break;
                case 'description':
                    updatedDefinition.description = data as string;
                    break;
                case 'sourceType':
                    updatedDefinition.sourceType = data as 'static' | 'api' | 'database';
                    // 切换数据源类型时重置配置
                    updatedDefinition.sourceConfig = {};
                    if (data === 'static') {
                        updatedDefinition.sourceConfig.staticData = null;
                    } else if (data === 'api') {
                        updatedDefinition.sourceConfig.apiData = {};
                    } else if (data === 'database') {
                        updatedDefinition.sourceConfig.database = {};
                    }
                    break;
                case 'filterFunctionString':
                    updatedDefinition.filterFunctionString = data as string;
                    break;
                case 'isActiveRendering':
                    updatedDefinition.isActiveRendering = data as boolean;
                    break;

                case 'staticData': {
                    const staticDataValue = data as string;
                    // 使用DataTypeUtil智能解析数据
                    const parsedData = DataTypeUtil.smartParse(staticDataValue);
                    updatedDefinition.sourceConfig = {
                        ...updatedDefinition.sourceConfig,
                        staticData: parsedData
                    };
                    break;
                }
                case 'initialOrFallbackValue': {
                    const fallbackValue = data as string;
                    // 使用DataTypeUtil智能解析数据
                    const fallbackParsedData = DataTypeUtil.smartParse(fallbackValue);
                    updatedDefinition.initialOrFallbackValue = fallbackParsedData;
                    break;
                }

                // API配置处理
                case 'apiUrl':
                    if (!updatedDefinition.sourceConfig.apiData) {
                        updatedDefinition.sourceConfig.apiData = {};
                    }
                    updatedDefinition.sourceConfig.apiData.url = data as string;
                    break;
                case 'apiMethod':
                    if (!updatedDefinition.sourceConfig.apiData) {
                        updatedDefinition.sourceConfig.apiData = {};
                    }
                    updatedDefinition.sourceConfig.apiData.method = data as 'get' | 'post' | 'put' | 'delete';
                    break;
                case 'apiHeaders':
                    if (!updatedDefinition.sourceConfig.apiData) {
                        updatedDefinition.sourceConfig.apiData = {};
                    }
                    try {
                        updatedDefinition.sourceConfig.apiData.header = JSON.parse(data as string);
                    } catch {
                        updatedDefinition.sourceConfig.apiData.header = {};
                    }
                    break;
                case 'apiParams':
                    if (!updatedDefinition.sourceConfig.apiData) {
                        updatedDefinition.sourceConfig.apiData = {};
                    }
                    try {
                        updatedDefinition.sourceConfig.apiData.params = JSON.parse(data as string);
                    } catch {
                        updatedDefinition.sourceConfig.apiData.params = {};
                    }
                    break;

                // 数据库配置处理
                case 'dbTargetDb':
                    if (!updatedDefinition.sourceConfig.database) {
                        updatedDefinition.sourceConfig.database = {};
                    }
                    updatedDefinition.sourceConfig.database.targetDb = data as string;
                    break;
                case 'dbSql':
                    if (!updatedDefinition.sourceConfig.database) {
                        updatedDefinition.sourceConfig.database = {};
                    }
                    updatedDefinition.sourceConfig.database.sql = data as string;
                    break;

                // 测试结果是只读的，不处理
                case 'testResult':
                    return;
            }
        } else if (dataFragment) {
            // 处理批量字段变化（如果id是undefined，尝试从dataFragment中获取字段信息）

            // 处理各种字段
            if ('name' in dataFragment) {
                updatedDefinition.name = dataFragment.name as string;
            }
            if ('description' in dataFragment) {
                updatedDefinition.description = dataFragment.description as string;
            }
            if ('sourceType' in dataFragment) {
                updatedDefinition.sourceType = dataFragment.sourceType as 'static' | 'api' | 'database';
                // 切换数据源类型时重置配置
                updatedDefinition.sourceConfig = {};
                if (dataFragment.sourceType === 'static') {
                    updatedDefinition.sourceConfig.staticData = null;
                }
            }
            if ('staticData' in dataFragment) {
                const staticDataValue = dataFragment.staticData as string;
                // 使用严格解析，要求符合JSON格式
                const parseResult = DataTypeUtil.strictParse(staticDataValue);
                if (parseResult.success) {
                    updatedDefinition.sourceConfig = {
                        ...updatedDefinition.sourceConfig,
                        staticData: parseResult.data
                    };
                } else {
                    // 解析失败时显示错误，但不更新数据
                    console.warn('静态数据格式错误:', parseResult.error);
                }
            }
            if ('initialOrFallbackValue' in dataFragment) {
                const fallbackValue = dataFragment.initialOrFallbackValue as string;
                // 使用严格解析，要求符合JSON格式
                const parseResult = DataTypeUtil.strictParse(fallbackValue);
                if (parseResult.success) {
                    updatedDefinition.initialOrFallbackValue = parseResult.data;
                } else {
                    // 解析失败时显示错误，但不更新数据
                    console.warn('初始值/错误默认值格式错误:', parseResult.error);
                }
            }
            if ('filterFunctionString' in dataFragment) {
                updatedDefinition.filterFunctionString = dataFragment.filterFunctionString as string;
            }
            if ('isActiveRendering' in dataFragment) {
                updatedDefinition.isActiveRendering = dataFragment.isActiveRendering as boolean;
            }

            // API配置处理 - 使用不可变更新方式
            if ('apiUrl' in dataFragment) {
                updatedDefinition.sourceConfig = {
                    ...updatedDefinition.sourceConfig,
                    apiData: {
                        ...updatedDefinition.sourceConfig.apiData,
                        url: dataFragment.apiUrl as string
                    }
                };
            }
            if ('apiMethod' in dataFragment) {
                updatedDefinition.sourceConfig = {
                    ...updatedDefinition.sourceConfig,
                    apiData: {
                        ...updatedDefinition.sourceConfig.apiData,
                        method: dataFragment.apiMethod as 'get' | 'post' | 'put' | 'delete'
                    }
                };
            }
            if ('apiHeaders' in dataFragment) {
                let headerValue = {};
                try {
                    headerValue = JSON.parse(dataFragment.apiHeaders as string);
                } catch {
                    headerValue = {};
                }
                updatedDefinition.sourceConfig = {
                    ...updatedDefinition.sourceConfig,
                    apiData: {
                        ...updatedDefinition.sourceConfig.apiData,
                        header: headerValue
                    }
                };
            }
            if ('apiParams' in dataFragment) {
                let paramsValue = {};
                try {
                    paramsValue = JSON.parse(dataFragment.apiParams as string);
                } catch {
                    paramsValue = {};
                }
                updatedDefinition.sourceConfig = {
                    ...updatedDefinition.sourceConfig,
                    apiData: {
                        ...updatedDefinition.sourceConfig.apiData,
                        params: paramsValue
                    }
                };
            }
            // 数据库配置处理 - 使用不可变更新方式
            if ('dbTargetDb' in dataFragment) {
                updatedDefinition.sourceConfig = {
                    ...updatedDefinition.sourceConfig,
                    database: {
                        ...updatedDefinition.sourceConfig.database,
                        targetDb: dataFragment.dbTargetDb as string
                    }
                };
            }
            if ('dbSql' in dataFragment) {
                updatedDefinition.sourceConfig = {
                    ...updatedDefinition.sourceConfig,
                    database: {
                        ...updatedDefinition.sourceConfig.database,
                        sql: dataFragment.dbSql as string
                    }
                };
            }
        }

        setEditingDefinition(updatedDefinition);
    };



    // 测试按钮处理
    const handleTest = async () => {
        if (!editingDefinition) return;

        setIsTestLoading(true);

        try {
            // 1. 先进行基本配置验证
            const basicValidation = validateDefinitionBasic(editingDefinition);
            if (!basicValidation.isValid) {
                setTestResult(DataTypeUtil.formatForDisplay({
                    success: false,
                    error: '配置验证失败',
                    errors: basicValidation.errors,
                    timestamp: new Date().toISOString()
                }, 2, true));
                return;
            }

            // 2. 进行完整的数据处理和验证
            const processedValidation = await validateProcessedData(editingDefinition);

            if (processedValidation.isValid) {
                // 验证成功，显示详细结果
                const result = {
                    success: true,
                    message: '测试成功',
                    finalData: processedValidation.finalData,
                    dataType: DataTypeUtil.getDataType(processedValidation.finalData),
                    hasFilter: !!editingDefinition.filterFunctionString?.trim(),
                    timestamp: new Date().toISOString()
                };
                setTestResult(DataTypeUtil.formatForDisplay(result, 2, true));
            } else {
                // 验证失败，显示错误信息
                const errorResult = {
                    success: false,
                    error: '数据处理验证失败',
                    errors: processedValidation.errors,
                    timestamp: new Date().toISOString()
                };
                setTestResult(DataTypeUtil.formatForDisplay(errorResult, 2, true));
            }

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            console.error('测试失败:', errorMessage);

            const errorResult = {
                success: false,
                error: errorMessage,
                timestamp: new Date().toISOString(),
                definition: {
                    name: editingDefinition.name,
                    sourceType: editingDefinition.sourceType,
                    hasFilter: !!editingDefinition.filterFunctionString?.trim(),
                    hasInitialValue: editingDefinition.initialOrFallbackValue !== undefined
                }
            };

            setTestResult(DataTypeUtil.formatForDisplay(errorResult, 2, true));
        } finally {
            setIsTestLoading(false);
        }
    };

    // 验证定义的基本完整性（不包括数据验证）
    const validateDefinitionBasic = (definition: any): {isValid: boolean, errors: string[]} => {
        const errors: string[] = [];

        if (!definition.name || definition.name.trim() === '') {
            errors.push('变量名称不能为空');
        }

        switch (definition.sourceType) {
            case 'static':
                // 静态数据基本验证：确保有数据配置
                if (definition.sourceConfig?.staticData === undefined) {
                    errors.push('静态数据不能为空');
                }
                break;
            case 'api':
                if (!definition.sourceConfig?.apiData?.url || definition.sourceConfig.apiData.url.trim() === '') {
                    errors.push('API地址不能为空');
                }
                break;
            case 'database':
                if (!definition.sourceConfig?.database?.targetDb || definition.sourceConfig.database.targetDb.trim() === '') {
                    errors.push('数据库ID不能为空');
                }
                if (!definition.sourceConfig?.database?.sql || definition.sourceConfig.database.sql.trim() === '') {
                    errors.push('SQL语句不能为空');
                }
                break;
            default:
                errors.push(`不支持的数据源类型: ${definition.sourceType}`);
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    };

    // 验证经过过滤器处理后的最终数据
    const validateProcessedData = async (definition: any): Promise<{isValid: boolean, errors: string[], finalData?: any}> => {
        const errors: string[] = [];

        try {
            // 1. 获取原始数据
            let rawData: any;
            try {
                rawData = await globalVariableManager._fetchDataForVariable(definition);
            } catch (dataError) {
                // 如果获取失败，尝试使用初始值/错误默认值
                if (definition.initialOrFallbackValue !== undefined) {
                    rawData = definition.initialOrFallbackValue;
                } else {
                    errors.push(`数据获取失败且未配置默认值: ${dataError instanceof Error ? dataError.message : dataError}`);
                    return {isValid: false, errors};
                }
            }

            // 2. 应用过滤器
            let finalData: any;
            try {
                finalData = await globalVariableManager._parseAndExecuteFilter(
                    definition.filterFunctionString,
                    rawData,
                    definition.id,
                    new Set() // 使用空的调用栈进行验证
                );
            } catch (filterError) {
                errors.push(`过滤器执行失败: ${filterError instanceof Error ? filterError.message : filterError}`);
                return {isValid: false, errors};
            }

            // 3. 验证最终数据是否有效
            if (finalData === undefined) {
                errors.push('过滤器返回了 undefined，这可能导致组件显示异常');
            }

            return {
                isValid: errors.length === 0,
                errors,
                finalData
            };
        } catch (error) {
            errors.push(`验证过程中发生错误: ${error instanceof Error ? error.message : error}`);
            return {isValid: false, errors};
        }
    };

    // 保存处理
    const handleSave = async () => {
        if (!editingDefinition) return;

        try {
            // 验证配置的基本完整性
            const validationResult = validateDefinitionBasic(editingDefinition);
            if (!validationResult.isValid) {
                message.error(`配置验证失败: ${validationResult.errors.join(', ')}`);
                return;
            }

            // 检查名称唯一性
            const isUpdate = globalVariableManager.getVariableDefinition(editingDefinition.id);
            const existingDefinitions = globalVariableManager.getAllVariableDefinitions();

            // 如果是新建或者名称发生了变化，需要检查名称唯一性
            if (!isUpdate || (isUpdate && isUpdate.name !== editingDefinition.name)) {
                const nameExists = existingDefinitions.some(def =>
                    def.id !== editingDefinition.id && def.name === editingDefinition.name
                );

                if (nameExists) {
                    message.error('变量名称已存在，请使用其他名称');
                    return;
                }
            }

            let success = false;

            if (isUpdate) {
                success = await globalVariableManager.updateDefinition(editingDefinition);
            } else {
                success = await globalVariableManager.addDefinition(editingDefinition);
            }

            if (success) {
                message.success(isUpdate ? '更新成功' : '创建成功');
                handleCancel();

                // 无论是新建还是更新，都需要重新计算引用计数
                // 因为全局变量的配置（API URL、过滤器等）可能包含对其他全局变量的引用
                globalVariableManager.recalculateAllReferenceCounts();
            } else {
                message.error('保存失败，请检查配置后重试');
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            console.error('保存全局变量失败:', errorMessage);
            message.error(`保存失败: ${errorMessage}`);
        }
    };

    // 取消处理
    const handleCancel = () => {
        setPanelVisible(false);
        setEditingDefinition(null);
        setTestResult('');
    };

    // 构建表单配置
    const buildSchema = (): Control => {
        if (!editingDefinition) return {type: 'text-only', value: ''};

        return {
            type: 'grid',
            config: {gridGap: '12px'},
            children: [
                {
                    key: 'name',
                    type: 'input',
                    label: '变量名称',
                    reRender: true, // 启用响应式更新
                    value: editingDefinition.name || '',
                    config: {
                        placeholder: '请输入变量名称（项目内唯一）'
                    }
                },
                {
                    key: 'description',
                    type: 'input',
                    label: '描述',
                    reRender: true, // 启用响应式更新
                    value: editingDefinition.description || '',
                    config: {
                        placeholder: '请输入变量描述（可选）'
                    }
                },
                {
                    key: 'sourceType',
                    type: 'select',
                    label: '数据来源',
                    reRender: true, // 启用响应式更新
                    value: editingDefinition.sourceType,
                    config: {
                        options: [
                            {value: 'static', label: '静态数据'},
                            {value: 'api', label: 'API接口'},
                            {value: 'database', label: '数据库'}
                        ]
                    }
                },
                // 主动渲染
                {
                    key: 'isActiveRendering',
                    type: 'switch',
                    label: '主动渲染',
                    tip: '开启后，变量值变化时会自动触发相关组件刷新',
                    reRender: true, // 启用响应式更新
                    value: !!editingDefinition.isActiveRendering,
                    config: {}
                },
                // 静态数据配置（仅静态数据显示）
                ...(editingDefinition.sourceType === 'static' ? [{
                    key: 'staticData',
                    type: 'code-editor' as const,
                    label: '静态数据配置',
                    reRender: false, // 禁用响应式更新，避免输入时数据转换导致的错乱
                    config: {
                        height: 200,
                        language: 'json' as const
                    },
                    value: editingDefinition.sourceConfig.staticData !== undefined ? DataTypeUtil.formatForEditor(editingDefinition.sourceConfig.staticData, 2) : '',
                }] : []),
                // API配置（仅API显示）
                ...(editingDefinition.sourceType === 'api' ? [
                    {
                        key: 'apiUrl',
                        type: 'input' as const,
                        label: 'API地址',
                        reRender: true,
                        value: editingDefinition.sourceConfig.apiData?.url || '',
                        config: {
                            placeholder: '请输入API地址，支持全局变量引用 ${GV::variableName}'
                        }
                    },
                    {
                        key: 'apiMethod',
                        type: 'select' as const,
                        label: '请求方式',
                        reRender: true,
                        value: editingDefinition.sourceConfig.apiData?.method || 'get',
                        config: {
                            options: [
                                {value: 'get', label: 'GET'},
                                {value: 'post', label: 'POST'},
                                {value: 'put', label: 'PUT'},
                                {value: 'delete', label: 'DELETE'}
                            ]
                        }
                    },
                    {
                        key: 'apiHeaders',
                        type: 'code-editor' as const,
                        label: '请求头',
                        reRender: false, // 禁用响应式更新，避免输入时JSON格式化导致的错乱
                        config: {
                            height: 100,
                            language: 'json' as const
                        },
                        value: editingDefinition.sourceConfig.apiData?.header ? JSON.stringify(editingDefinition.sourceConfig.apiData.header, null, 2) : '{}',
                    },
                    {
                        key: 'apiParams',
                        type: 'code-editor' as const,
                        label: '请求参数',
                        reRender: false, // 禁用响应式更新，避免输入时JSON格式化导致的错乱
                        config: {
                            height: 100,
                            language: 'json' as const
                        },
                        value: editingDefinition.sourceConfig.apiData?.params ? JSON.stringify(editingDefinition.sourceConfig.apiData.params, null, 2) : '{}',
                    }
                ] : []),
                // 数据库配置（仅数据库显示）
                ...(editingDefinition.sourceType === 'database' ? [
                    {
                        key: 'dbTargetDb',
                        type: 'input' as const,
                        label: '数据库ID',
                        reRender: true,
                        value: editingDefinition.sourceConfig.database?.targetDb || '',
                        config: {
                            placeholder: '请输入数据库ID'
                        }
                    },
                    {
                        key: 'dbSql',
                        type: 'code-editor' as const,
                        label: 'SQL语句',
                        reRender: true,
                        config: {
                            height: 150,
                            language: 'sql' as const
                        },
                        value: editingDefinition.sourceConfig.database?.sql || '',
                    }
                ] : []),
                // 初始值/错误默认值（仅API和数据库显示）
                ...(editingDefinition.sourceType === 'api' || editingDefinition.sourceType === 'database' ? [{
                    key: 'initialOrFallbackValue',
                    type: 'code-editor' as const,
                    label: '初始值/错误默认值',
                    reRender: false, // 禁用响应式更新，避免输入时数据转换导致的错乱
                    config: {
                        height: 120,
                        language: 'json' as const
                    },
                    value: editingDefinition.initialOrFallbackValue !== undefined ? DataTypeUtil.formatForEditor(editingDefinition.initialOrFallbackValue, 2) : '',
                }] : []),

                // 过滤器
                {
                    key: 'filterFunctionString',
                    type: 'code-editor',
                    label: '过滤器',
                    reRender: true, // 启用响应式更新
                    config: {
                        height: 150,
                        language: 'javascript' as const,
                        format: true
                    },
                    value: editingDefinition.filterFunctionString || "// 过滤器函数体，可以直接编写JavaScript代码\n// 参数: data - 原始数据, globalVars - 全局变量访问对象\n// 示例: return data.filter(item => item.value > 100);\n// 示例: return data.map(item => ({...item, processed: true}));\n// 示例: const threshold = globalVars.get('threshold'); return data.filter(item => item.value > threshold);\n\nreturn data;",
                },
                // 测试结果
                {
                    key: 'testResult',
                    type: 'code-editor',
                    label: '测试结果',
                    reRender: true, // 启用响应式更新
                    config: {
                        readonly: true,
                        height: 120,
                        language: 'json' as const
                    },
                    value: testResult || '// 点击测试按钮查看结果',
                }
            ]
        };
    };

    return (
        <Modal
            title={editingDefinition?.name ? `编辑全局变量: ${editingDefinition.name}` : '新建全局变量'}
            open={isPanelVisible}
            onOk={handleSave}
            onCancel={handleCancel}
            width={800}
            okText="保存"
            cancelText="取消"
            className="global-variable-panel"
            destroyOnClose
            footer={[
                <Button
                    key="test"
                    onClick={handleTest}
                    loading={isTestLoading}
                    style={{ float: 'left' }}
                >
                    测试
                </Button>,
                <Button key="cancel" onClick={handleCancel}>
                    取消
                </Button>,
                <Button key="save" type="primary" onClick={handleSave}>
                    保存
                </Button>
            ]}
        >
            <div className="gv-panel-content">
                <LCGUI schema={buildSchema()} onFieldChange={onFieldChange}/>
            </div>
        </Modal>
    );
});

export default GlobalVariablePanel;

/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.monaco-editor-container {
  border: 2px solid #2d2d2d;
}

.monaco-editor-button-bar {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  position: relative;
  top: -30px;
  right: 12px;
  z-index: 10;
  pointer-events: none; // 让容器不阻挡鼠标事件

  .editor-full-btn, .editor-format-btn {
    font-size: 16px;
    color: #939393;
    transition: all 0.3s;
    cursor: pointer;
    padding: 2px;
    pointer-events: auto; // 但按钮本身可以接收鼠标事件

    &:hover {
      color: #47a4ff;
    }
  }
}


/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import JSXTransformer from './JSXTransformer';

/**
 * JSXTransformer测试
 * 这个文件用于测试JSX转换器的功能
 */

// 测试基本的JSX转换
export function testBasicJSXTransform() {
    const code = `function(data, rowData, rowIndex) {
        return <antd.Tag color="green">测试</antd.Tag>;
    }`;
    
    try {
        const transformedCode = JSXTransformer.transformJSXCode(code);
        console.log('转换成功:', transformedCode);
        return true;
    } catch (error) {
        console.error('转换失败:', error);
        return false;
    }
}

// 测试代码执行
export function testCodeExecution() {
    const code = `function(data, rowData, rowIndex) {
        const status = rowData.status;
        if (status === 'active') {
            return React.createElement(antd.Tag, { color: 'green' }, '激活');
        } else {
            return React.createElement(antd.Tag, { color: 'red' }, '禁用');
        }
    }`;
    
    const testData = [
        { id: 1, name: '测试1', status: 'active' },
        { id: 2, name: '测试2', status: 'inactive' }
    ];
    
    try {
        const result1 = JSXTransformer.executeCustomCode(code, testData, testData[0], 0);
        const result2 = JSXTransformer.executeCustomCode(code, testData, testData[1], 1);
        
        console.log('执行结果1:', result1);
        console.log('执行结果2:', result2);
        return true;
    } catch (error) {
        console.error('执行失败:', error);
        return false;
    }
}

// 测试代码验证
export function testCodeValidation() {
    const validCode = `function(data, rowData, rowIndex) {
        return <antd.Tag>测试</antd.Tag>;
    }`;
    
    const invalidCode = `function(data, rowData, rowIndex) {
        return <antd.Tag>测试</antd.Tag;  // 缺少闭合标签
    }`;
    
    const validResult = JSXTransformer.validateCode(validCode);
    const invalidResult = JSXTransformer.validateCode(invalidCode);
    
    console.log('有效代码验证:', validResult);
    console.log('无效代码验证:', invalidResult);
    
    return validResult.valid && !invalidResult.valid;
}

// 运行所有测试
export function runAllTests() {
    console.log('开始测试JSXTransformer...');
    
    const test1 = testBasicJSXTransform();
    const test2 = testCodeExecution();
    const test3 = testCodeValidation();
    
    console.log('测试结果:');
    console.log('- 基本JSX转换:', test1 ? '通过' : '失败');
    console.log('- 代码执行:', test2 ? '通过' : '失败');
    console.log('- 代码验证:', test3 ? '通过' : '失败');
    
    const allPassed = test1 && test2 && test3;
    console.log('所有测试:', allPassed ? '通过' : '失败');
    
    return allPassed;
}

// 在浏览器控制台中可以调用这些函数进行测试
if (typeof window !== 'undefined') {
    (window as any).JSXTransformerTest = {
        testBasicJSXTransform,
        testCodeExecution,
        testCodeValidation,
        runAllTests
    };
}

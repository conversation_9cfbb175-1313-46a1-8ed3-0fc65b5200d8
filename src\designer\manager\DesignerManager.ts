/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractManager from "./core/AbstractManager.ts";
import {action, makeObservable, observable} from "mobx";
import {DesignerMode, ProjectDataType} from "../DesignerType.ts";
import canvasManager from "../header/items/canvas/CanvasManager.ts";
import layerManager from "./LayerManager.ts";
import themeManager from "../header/items/theme/ThemeManager.ts";
import bluePrintManager from "../blueprint/manager/BluePrintManager.ts";
import FilterManager from "./FilterManager.ts";
import globalVariableManager from "./GlobalVariableManager.ts";

/**
 * 整个设计器的所有数据初始化和数据聚合，统一通过该管理器进行分发和处理
 */
class DesignerManager extends AbstractManager<ProjectDataType> {
    constructor() {
        super();
        makeObservable(this, {
            loaded: observable,
            setLoaded: action,
        })
    }

    loaded: boolean = false;

    setLoaded = (loaded: boolean) => this.loaded = loaded;

    destroy(): void {
        canvasManager.destroy();
        layerManager.destroy();
        themeManager.destroy();
        bluePrintManager.destroy();
        globalVariableManager.destroy();
    }

    getData(): ProjectDataType {
        return {
            canvasManager: canvasManager.getData(),
            themeManager: themeManager.getData()!,
            layerManager: layerManager.getData(),
            bluePrintManager: bluePrintManager.getData(),
            filterManager: FilterManager.getData(),
            globalVariableManager: globalVariableManager.getData(),
        };
    }

    async init(data: ProjectDataType, mode: DesignerMode): Promise<void> {
        data.canvasManager && canvasManager.init(data.canvasManager!);
        data.themeManager && themeManager.init(data.themeManager!);
        data.layerManager && layerManager.init(data.layerManager!);
        data.bluePrintManager && bluePrintManager.init(data.bluePrintManager!, mode)
        data.filterManager && FilterManager.init(data.filterManager!);

        // 异步初始化全局变量管理器
        if (data.globalVariableManager) {
            await globalVariableManager.init(data.globalVariableManager!);

            // 在所有管理器初始化完成后，延迟计算引用计数
            // 确保组件控制器已经创建完成
            setTimeout(() => {
                globalVariableManager.recalculateAllReferenceCounts();
            }, 500); // 增加延迟时间，确保组件控制器创建完成
        }
    }

}

const designerManager = new DesignerManager();
export default designerManager;
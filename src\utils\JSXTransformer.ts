/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import * as Babel from '@babel/standalone';
import * as React from 'react';
import * as antd from 'antd';
import globalVariableManager from '../designer/manager/GlobalVariableManager';

/**
 * JSX转换器
 * 负责将用户编写的JSX代码转换为可执行的JavaScript代码
 */
export default class JSXTransformer {
    
    /**
     * 转换JSX代码为JavaScript代码
     * @param code 用户编写的JSX代码
     * @returns 转换后的JavaScript代码
     */
    public static transformJSXCode(code: string): string {
        try {
            // 将函数表达式包装成可解析的形式
            const wrappedCode = `const userFunction = ${code}`;

            const result = Babel.transform(wrappedCode, {
                presets: ['react'],
                plugins: []
            });

            if (!result.code) {
                throw new Error('转换结果为空');
            }

            // 提取转换后的函数部分，移除末尾的分号
            let transformedCode = result.code.replace('const userFunction = ', '');
            // 移除末尾的分号，因为我们要将其作为函数表达式使用
            transformedCode = transformedCode.replace(/;$/, '');
            return transformedCode;
        } catch (error) {
            console.error('JSX转换错误:', error);
            throw new Error('JSX语法错误，请检查代码');
        }
    }

    /**
     * 创建自定义代码执行上下文
     * @returns 执行上下文对象
     */
    public static createExecutionContext() {
        return {
            React,
            antd: {
                Button: antd.Button,
                Tag: antd.Tag,
                Badge: antd.Badge,
                Progress: antd.Progress,
                Tooltip: antd.Tooltip,
                Popover: antd.Popover,
                Switch: antd.Switch,
                Rate: antd.Rate,
                Avatar: antd.Avatar,
                Image: antd.Image,
                Space: antd.Space,
                Divider: antd.Divider,
                Typography: antd.Typography,
                Alert: antd.Alert,
                Spin: antd.Spin,
                Result: antd.Result,
                Empty: antd.Empty,
                Statistic: antd.Statistic,
                Card: antd.Card,
                List: antd.List,
                Descriptions: antd.Descriptions,
                Timeline: antd.Timeline,
                Steps: antd.Steps,
                Breadcrumb: antd.Breadcrumb,
                Dropdown: antd.Dropdown,
                Menu: antd.Menu,
                Pagination: antd.Pagination,
                Input: antd.Input,
                Select: antd.Select,
                Checkbox: antd.Checkbox,
                Radio: antd.Radio,
                DatePicker: antd.DatePicker,
                TimePicker: antd.TimePicker,
                Upload: antd.Upload,
                Form: antd.Form,
                Slider: antd.Slider,
                InputNumber: antd.InputNumber,
                Cascader: antd.Cascader,
                TreeSelect: antd.TreeSelect,
                Transfer: antd.Transfer,
                AutoComplete: antd.AutoComplete,
                ColorPicker: antd.ColorPicker,
            },
            globalVars: {
                get: (name: string) => globalVariableManager.getVariableValueByName(name)
            }
        };
    }

    /**
     * 执行自定义代码
     * @param code 用户编写的代码
     * @param data 所有表格数据
     * @param rowData 当前行数据
     * @param rowIndex 当前行索引
     * @returns 执行结果
     */
    public static executeCustomCode(
        code: string,
        data: any[],
        rowData: Record<string, any>,
        rowIndex: number
    ): any {
        try {
            // 转换JSX为JavaScript
            const transformedCode = this.transformJSXCode(code);

            // 创建执行上下文
            const context = this.createExecutionContext();

            // 创建函数并执行
            const func = new Function(
                'data', 'rowData', 'rowIndex',
                'React', 'antd', 'globalVars',
                `return (${transformedCode})(data, rowData, rowIndex)`
            );

            const result = func(
                data, rowData, rowIndex,
                context.React, context.antd, context.globalVars
            );

            return result;
        } catch (error) {
            console.error('自定义代码执行错误:', error);
            throw error;
        }
    }

    /**
     * 验证代码语法
     * @param code 要验证的代码
     * @returns 验证结果
     */
    public static validateCode(code: string): { valid: boolean; error?: string } {
        try {
            this.transformJSXCode(code);
            return { valid: true };
        } catch (error) {
            return { 
                valid: false, 
                error: error instanceof Error ? error.message : '未知错误' 
            };
        }
    }

    /**
     * 获取默认的自定义代码示例
     * @returns 默认代码示例
     */
    public static getDefaultCustomCode(): string {
        return `function(data, rowData, rowIndex) {
    // 可用组件：antd.Button, antd.Tag, antd.Badge, antd.Progress, antd.Tooltip 等
    // 可用工具：globalVars.get(name)
    
    const status = rowData.status;
    
    if (status === 'active') {
        return <antd.Tag color="green">激活</antd.Tag>;
    } else if (status === 'pending') {
        return <antd.Tag color="orange">待审核</antd.Tag>;
    } else {
        return <antd.Tag color="red">禁用</antd.Tag>;
    }
}`;
    }
}

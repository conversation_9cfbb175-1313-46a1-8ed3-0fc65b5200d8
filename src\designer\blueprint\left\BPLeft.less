/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.bp-left {
  display: flex;
  height: calc(100vh - 50px);
  background-color: #1a1a1a;
  border-right: 1px solid #2e2e2e;

  .bp-node-sort-list {
    height: 100%;
    width: 70px;
    border-right: 1px solid #2e2e2e;

    .bp-left-item:hover {
      transition: all 0.3s ease-in-out;
      background-color: #3d3d3d;
      color: #0095db;
      cursor: pointer;
    }

    .bp-left-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #999999;
      padding: 10px 2px;
      transition: background-color, color, 0.3s ease-in-out;

      .bp-item-label {
        margin-top: 5px;
        font-size: 12px;
      }

      .bp-item-icon {
        font-size: 20px;
      }
    }

    .bp-left-item-active {
      color: #0095db;
    }
  }

  .bp-node-list {
    width: 250px;
    height: 100%;
    background-color: #1a1a1a;
    z-index: 1;
    font-size: 12px;

    .bp-node-list-header {
      width: 100%;
      height: 50px;
      border-bottom: 1px solid #2e2e2e;
      display: flex;
      padding: 5px 10px;
      color: #999999;
      align-items: center;

      .bp-node-list-search {
        width: 100%;
      }
    }


    .bp-node-list-body {
      padding: 10px;
      color: #999999;
      height: calc(100vh - 100px);
      overflow-y: scroll;

      .bp-node-list-container {

        .bp-node-list-item-used {
          background-color: #181818;
          color: #656565;
        }

        .bp-node-list-item:hover {
          background-color: #3d3d3d;
          color: #9ed4ff;
          cursor: pointer;
        }

        .bp-node-list-item {
          margin-bottom: 5px;
          background-color: #262626;
          padding: 10px 5px;
          border-radius: 5px;
          transition: background-color, color, 0.2s ease-in-out;
          display: flex;
          align-items: center;

          .bpn-li-icon {
            width: 18px;
            height: 18px;
            margin-right: 5px;
            border-radius: 3px;
            padding: 3px;
            color: white;
            background-color: rgba(0, 152, 255, 0.25);

            span {
              position: relative;
            }
          }
        }
      }
    }
  }

}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { ComponentBaseProps } from "../../common-component/CommonTypes.ts";
import './FinanceTableComponent.less';
import { Tooltip } from "antd";
import { CustomCarousel } from "../../common-component/custom-carousel/CustomCarousel.tsx";

// 财务表格数据结构
export interface FinanceTableData {
    header: string[];  // 表头数组
    body: (string | number)[][];  // 表体数据数组
}

export interface FinanceTableComponentProps extends ComponentBaseProps {
    // 继承 ComponentBaseProps 的 data 字段（DataConfigType）
    // 不需要重新定义 data 字段
}

export interface FinanceTableComponentRef {
    updateConfig: (newConfig: FinanceTableComponentProps) => void;
    setEventHandler: (eventMap: Record<string, (...args: any[]) => void>) => void;
    destroy: () => void;
}

const FinanceTableComponent = forwardRef((props: FinanceTableComponentProps, ref: ForwardedRef<FinanceTableComponentRef>) => {
    const [config, setConfig] = useState<FinanceTableComponentProps>({ ...props });

    const tableContainerRef = useRef<HTMLDivElement>(null);
    const eventHandlerMap = useRef<Record<string, (...args: any[]) => void>>({});

    useImperativeHandle(ref, () => ({
        updateConfig: (newConfig) => setConfig({ ...newConfig }),
        setEventHandler: (eventMap) => (eventHandlerMap.current = eventMap),
        destroy: () => {}
    }));

    const onClick = () => {
        if ('click' in eventHandlerMap.current) {
            eventHandlerMap.current['click']();
        }
    }

    // 固定的样式配置
    const FIXED_STYLES = {
        header: {
            height: 80,
            fontSize: 18,
            fontWeight: 900,
            color: '#333',
            background: 'rgba(221, 237, 254, 0.3)', // #ddedfe 30%透明度
        },
        body: {
            rowHeight: 64,
            fontSize: 18,
            fontWeight: 500,
            color: '#333',
            oddRowColor: '#f0f7fa',
            evenRowColor: 'transparent',
            pageSize: 5,
            enableCarousel: false, // 默认关闭，根据数据量自动判断
            carouselSpeed: 3,
            pauseOnHover: true,
            carouselMode: 'step' as 'step' | 'continuous',
            slidesToScroll: 1,
        },
        columns: {
            firstColumnWidth: 250, // 第一列固定宽度
            firstColumnMaxLength: 10, // 第一列内容最大长度
        }
    };

    // 生成grid-template-columns用于Grid布局
    const generateGridTemplateColumns = (headers: string[]) => {
        if (headers.length === 0) return 'none';

        // 第一列固定宽度250px，其他列均分剩余空间
        const template = headers.map((_, index) => {
            if (index === 0) {
                return `${FIXED_STYLES.columns.firstColumnWidth}px`;
            }
            return '1fr';
        }).join(' ');

        return template;
    };

    // 合并props更新逻辑
    useEffect(() => {
        setConfig({ ...props });
    }, [props]);


    const getFinanceData = (): FinanceTableData => {
        // config.data 是 DataConfigType，包含 staticData 字段
        const dataConfig = config.data as any; // ComponentBaseProps 中的 data 字段
        if (dataConfig?.staticData) {
            return dataConfig.staticData as FinanceTableData;
        }
        // 默认空数据
        return { header: [], body: [] };
    };

    const financeData = getFinanceData();
    const bodyData = financeData.body || [];

    // 判断是否应该启用轮播：数据量足够时才启用
    const shouldEnableCarousel = bodyData.length >= FIXED_STYLES.body.pageSize;

    // 生成Grid布局的列定义
    const gridTemplateColumns = generateGridTemplateColumns(financeData.header || []);

    /**
     * 获取文本对齐的类名
     * @param columnIndex 列索引
     * @param isHeader 是否为表头
     * @returns 对应的CSS类名
     */
    const getAlignClassName = (columnIndex: number, isHeader: boolean = false) => {
        if (columnIndex === 0) {
            // 第一列：表头居中，表体居左
            return isHeader ? 'text-align-center' : 'text-align-left';
        } else {
            // 其他列：表头和表体都右对齐
            return 'text-align-right';
        }
    };

    /**
     * 渲染第一列内容（带文本截断和 Tooltip）
     * @param content 内容
     * @returns 渲染的内容
     */
    const renderFirstColumnContent = (content: string | number) => {
        const text = String(content);
        const maxLength = FIXED_STYLES.columns.firstColumnMaxLength;

        if (text.length <= maxLength) {
            // 内容长度不超过限制，正常显示
            return <span style={{ padding: '0 15px' }}>{text}</span>;
        } else {
            // 内容超过限制，显示省略号并添加 Tooltip
            const truncatedText = text.substring(0, maxLength) + '...';
            return (
                <Tooltip title={text} placement="topLeft">
                    <span style={{ cursor: 'pointer',padding: '0 15px' }}>{truncatedText}</span>
                </Tooltip>
            );
        }
    };

    // 渲染每一行
    const tableRows = useMemo(() => bodyData.map((rowData, index) => (
        <div
            key={index}
            className="finance-table-row-div"
            style={{
                backgroundColor: index % 2 === 0 ? FIXED_STYLES.body.oddRowColor : FIXED_STYLES.body.evenRowColor,
                gridTemplateColumns,
                height: `${FIXED_STYLES.body.rowHeight}px`,
            }}
        >
            {rowData.map((cellData: string | number, cellIndex: number) => (
                <div
                    key={cellIndex}
                    className={`finance-table-cell-div ${getAlignClassName(cellIndex)}`}
                >
                    {cellIndex === 0 ? renderFirstColumnContent(cellData) : cellData}
                </div>
            ))}
        </div>
    )), [bodyData, gridTemplateColumns]);

    return (
        <div className="finance-table-container" ref={tableContainerRef} onClick={onClick}>
            {/* 表头 */}
            <div
                className="finance-table-header-div"
                style={{
                    height: FIXED_STYLES.header.height,
                    fontSize: FIXED_STYLES.header.fontSize,
                    fontWeight: FIXED_STYLES.header.fontWeight,
                    color: FIXED_STYLES.header.color,
                    background: FIXED_STYLES.header.background,
                }}
            >
                <div className="finance-table-row-div" style={{ gridTemplateColumns }}>
                    {financeData.header?.map((headerText: string, index: number) => (
                        <div
                            key={index}
                            className={`finance-table-cell-div finance-table-header-cell ${getAlignClassName(index, true)}`}
                        >
                            {headerText}
                        </div>
                    ))}
                </div>
            </div>

            {/* 表体 */}
            <div
                className="finance-table-body-div"
                style={{
                    height: 'auto',
                    fontSize: FIXED_STYLES.body.fontSize,
                    fontWeight: FIXED_STYLES.body.fontWeight,
                    color: FIXED_STYLES.body.color,
                }}
            >
                {shouldEnableCarousel ? (
                    <CustomCarousel
                        mode={FIXED_STYLES.body.carouselMode}
                        speed={FIXED_STYLES.body.carouselSpeed}
                        slidesToScroll={FIXED_STYLES.body.slidesToScroll}
                        pauseOnHover={FIXED_STYLES.body.pauseOnHover}
                        rowHeight={FIXED_STYLES.body.rowHeight}
                    >
                        {tableRows}
                    </CustomCarousel>
                ) : (
                    <div className="finance-table-body-content">
                        {tableRows.slice(0, FIXED_STYLES.body.pageSize)}
                    </div>
                )}
            </div>
        </div>
    );
});

export default FinanceTableComponent;

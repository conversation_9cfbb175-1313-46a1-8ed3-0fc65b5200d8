/**
 * Echarts自定义组件定义
 */

import React from "react";
import {BaseInfoType, MenuToConfigMappingType} from "../../../framework/core/AbstractDefinition";
import {MenuInfo} from "../../../designer/right/MenuType";
import {ClazzTemplate} from "../../common-component/CommonTypes.ts";
import echartsImg from './echarts-custom.png';
import EchartsCustomController, {EchartsCustomProps} from "./EchartsCustomController";
import {AntdCommonDefinition} from "../../antd-common/AntdCommonDefinition";
import {Data, Optimize, SettingOne} from "@icon-park/react";

const EchartsCustomConfig = React.lazy(() => import("./EchartsCustomConfig"));
const BaseInfo = React.lazy(() => import("../../common-component/base-info/BaseInfo"));
const DataConfig = React.lazy(() => import("../../common-component/data-config/DataConfig"));

class EchartsCustomDefinition extends AntdCommonDefinition<EchartsCustomController, EchartsCustomProps> {

    getController(): ClazzTemplate<EchartsCustomController> | null {
        return EchartsCustomController;
    }

    getMenuList(): Array<MenuInfo> {
        return [
            {
                icon: SettingOne,
                name: '基础',
                key: 'base',
            },
            {
                icon: Optimize,
                name: '自定义',
                key: 'custom',
            },
            {
                icon: Data,
                name: '数据',
                key: 'data',
            }
        ];
    }

    getMenuToConfigContentMap(): MenuToConfigMappingType {
        return {
            base: BaseInfo,
            data: DataConfig,
            custom: EchartsCustomConfig,
        };
    }

    getBaseInfo(): BaseInfoType {
        return {
            compName: "Echarts自定义图表",
            compKey: "EchartsCustom",
            categorize: "other",
        };
    }

    getChartImg(): string | null {
        return echartsImg;
    }

    getInitConfig(): EchartsCustomProps {

        return {
            base: {
                id: "",
                name: 'Echarts自定义图表',
                type: 'EchartsCustom',
            },
            style: {
                customCode: "function renderEcharts(container, echarts, data, globalVars, dynamicStyle) {\n" +
                    "    // Echarts自定义图表示例 - 支持全局变量和动态样式\n" +
                    "    // 参数说明:\n" +
                    "    // - container: 图表容器\n" +
                    "    // - echarts: Echarts库对象\n" +
                    "    // - data: 静态数据或组件数据\n" +
                    "    // - globalVars: 全局变量访问对象\n" +
                    "    // - dynamicStyle: 蓝图传入的动态样式配置\n" +
                    "    \n" +
                    "    // 全局变量使用示例:\n" +
                    "    // 方式1: 使用globalVars.get()方法\n" +
                    "    // const threshold = globalVars.get('threshold') || 50;\n" +
                    "    // const chartTitle = globalVars.get('chartTitle') || '默认标题';\n" +
                    "    // \n" +
                    "    // 方式2: 使用${GV::variableName}模板语法(在字符串中)\n" +
                    "    // 注意: 模板语法会在代码执行前被替换\n" +
                    "    \n" +
                    "    // 动态样式使用示例:\n" +
                    "    // 蓝图可以通过'更新组件样式'传入样式配置\n" +
                    "    // const finalConfig = Object.assign({}, baseConfig, dynamicStyle);\n" +
                    "    \n" +
                    "    // 初始化echarts实例\n" +
                    "    const chart = echarts.init(container);\n" +
                    "    \n" +
                    "    // 默认配置\n" +
                    "    const option = {\n" +
                    "        title: {\n" +
                    "            text: 'Echarts示例图表',\n" +
                    "            left: 'center'\n" +
                    "        },\n" +
                    "        tooltip: {\n" +
                    "            trigger: 'item'\n" +
                    "        },\n" +
                    "        xAxis: {\n" +
                    "            type: 'category',\n" +
                    "            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n" +
                    "        },\n" +
                    "        yAxis: {\n" +
                    "            type: 'value'\n" +
                    "        },\n" +
                    "        series: [{\n" +
                    "            data: data || [120, 200, 150, 80, 70, 110, 130],\n" +
                    "            type: 'bar',\n" +
                    "            itemStyle: {\n" +
                    "                color: '#5470c6'\n" +
                    "            }\n" +
                    "        }]\n" +
                    "    };\n" +
                    "    \n" +
                    "    // 合并动态样式配置\n" +
                    "    const finalOption = dynamicStyle ? \n" +
                    "        Object.assign({}, option, dynamicStyle) : option;\n" +
                    "    \n" +
                    "    // 设置配置项并渲染图表\n" +
                    "    chart.setOption(finalOption);\n" +
                    "    \n" +
                    "    // 返回图表实例\n" +
                    "    return chart;\n" +
                    "}"
            },
            data: {
                sourceType: 'static',
                staticData: [120, 200, 150, 80, 70, 110, 130]
            }
        };
    }

}

export default EchartsCustomDefinition;

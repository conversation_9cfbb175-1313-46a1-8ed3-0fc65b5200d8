/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {forwardRef, useImperativeHandle, useState, useEffect} from 'react';
import {ChildTabComponentProps, ChildTabStateStyle} from './CustomTabGroupTypes';
import layerManager from '../../../designer/manager/LayerManager';

export interface ChildTabComponentRef {
    updateConfig: (newConfig: ChildTabComponentProps) => void;
}

const ChildTabComponent = forwardRef<ChildTabComponentRef, ChildTabComponentProps>((props, ref) => {
    const [config, setConfig] = useState<ChildTabComponentProps>({...props});
    const {style} = config;
    const {parentId, value, isActive, activeStyle, inactiveStyle} = style;

    // 当props变化时，更新内部状态
    useEffect(() => {
        setConfig({...props});
    }, [props]);

    useImperativeHandle(ref, () => ({
        updateConfig: (newConfig: ChildTabComponentProps) => {
            // 强制创建新的对象引用，确保React能检测到变化
            setConfig(prevConfig => {
                const updatedConfig = {
                    ...newConfig,
                    // 确保每次都是新的对象引用
                    _updateKey: Date.now()
                } as any;
                return updatedConfig;
            });
        }
    }));

    const handleClick = () => {
        // 通过parentId获取父级控制器并调用handleChildClick方法
        const {compController} = layerManager;
        const parentController = compController[parentId];
        if (parentController && typeof parentController.handleChildClick === 'function') {
            parentController.handleChildClick(value);
        }
    };

    // 根据isActive状态选择样式
    const currentStyle = isActive ? activeStyle : inactiveStyle;

    // 构建内联样式
    const textAlign = currentStyle.textAlign || 'center';
    let justifyContent: React.CSSProperties['justifyContent'] = 'center';

    // 根据textAlign设置justifyContent
    if (textAlign === 'left') {
        justifyContent = 'flex-start';
    } else if (textAlign === 'right') {
        justifyContent = 'flex-end';
    } else {
        justifyContent = 'center';
    }

    // 处理边框宽度，确保0值能正确生效
    const borderWidth = currentStyle.borderWidth !== undefined ? currentStyle.borderWidth : 1;
    const borderStyle = borderWidth > 0 ? `${borderWidth}px solid ${currentStyle.borderColor || '#d9d9d9'}` : 'none';

    // 检查是否有有效的背景图片
    const hasValidBackgroundImage = currentStyle.backgroundImage && (
        (currentStyle.backgroundImage.type === 'local' && currentStyle.backgroundImage.localUrl && currentStyle.backgroundImage.localUrl.trim() !== '') ||
        (currentStyle.backgroundImage.type === 'online' && currentStyle.backgroundImage.onLineUrl && currentStyle.backgroundImage.onLineUrl.trim() !== '')
    );

    const inlineStyle: React.CSSProperties = {
        width: '100%',
        height: '100%',
        // 如果有背景图片，则不设置背景色，避免样式冲突
        ...(hasValidBackgroundImage ? {} : { background: currentStyle.background || '#f5f5f5' }),
        color: currentStyle.color || '#666666',
        border: borderStyle,
        borderRadius: currentStyle.borderRadius !== undefined ? currentStyle.borderRadius : 4,
        fontSize: currentStyle.fontSize !== undefined ? currentStyle.fontSize : 14,
        fontWeight: currentStyle.fontWeight || 'normal',
        padding: currentStyle.padding || '8px 16px',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: justifyContent,
        userSelect: 'none',
        transition: 'all 0.2s ease',
        boxSizing: 'border-box',
    };

    // 处理背景图片
    if (hasValidBackgroundImage) {
        const imageUrl = currentStyle.backgroundImage.type === 'local'
            ? currentStyle.backgroundImage.localUrl
            : currentStyle.backgroundImage.onLineUrl;

        if (imageUrl) {
            inlineStyle.backgroundImage = `url(${imageUrl})`;
            inlineStyle.backgroundSize = 'cover';
            inlineStyle.backgroundPosition = 'center';
            inlineStyle.backgroundRepeat = 'no-repeat';
        }
    }

    return (
        <div 
            style={inlineStyle}
            onClick={handleClick}
            onMouseEnter={(e) => {
                // 添加悬停效果
                if (!isActive) {
                    e.currentTarget.style.opacity = '0.8';
                }
            }}
            onMouseLeave={(e) => {
                // 移除悬停效果
                if (!isActive) {
                    e.currentTarget.style.opacity = '1';
                }
            }}
        >
            {config.base?.name || 'Tab'}
        </div>
    );
});

ChildTabComponent.displayName = 'ChildTabComponent';

export default ChildTabComponent;

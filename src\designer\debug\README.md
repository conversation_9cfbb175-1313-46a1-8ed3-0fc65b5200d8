# 全局变量调试器使用说明

## 概述

全局变量调试器是一个专门用于测试和调试全局变量自动渲染功能的浮动工具组件。它以浮动按钮的形式出现在页面右下角，允许您手动修改全局变量的值，并观察是否会触发相关组件的自动更新。

## 可用环境

调试器**仅在开发模式下可见**，确保生产环境的安全性：

### 自动检测开发环境
- `NODE_ENV=development`
- `REACT_APP_ENV=development`
- 本地主机地址（localhost、127.0.0.1）
- 开发端口（3000、3001、8080、5173等）
- 存在 React DevTools

### 手动启用调试模式
即使在生产环境中，也可以通过以下方式手动启用：
- URL 参数：`?debug=true`
- localStorage：`localStorage.setItem('light-chaser-debug', 'true')`

### 生产环境安全性
- 调试器在生产环境中默认不可见
- 只有开发人员可以通过特定方式手动启用
- 不会影响最终用户的使用体验
- 不会暴露敏感的调试信息

## 可用页面

调试器在以下页面中可用：
- **设计页面** (`/design`): 用于在设计时调试全局变量
- **预览页面** (`/view`): 用于在预览时调试全局变量

## 如何使用

### 1. 打开调试器
1. 在设计页面或预览页面中，查找页面右下角的蓝色浮动按钮（🐛 图标）
2. 点击浮动按钮，调试面板将在页面右上角弹出

### 2. 关闭调试器
1. 点击调试面板标题栏右侧的"关闭"按钮
2. 调试面板将关闭，恢复为浮动按钮状态

### 3. 浮动按钮特性
- **位置**: 固定在页面右下角，不会遮挡主要内容
- **样式**: 蓝色圆形按钮，带有调试器图标（🐛）
- **交互**: 鼠标悬停时有阴影和位移动画效果
- **层级**: 高 z-index 确保始终可见和可点击

## 功能特性

### 1. 变量选择和信息显示
- 选择要调试的全局变量
- 显示变量的详细信息：
  - 变量名称
  - 数据源类型（静态、API、数据库）
  - 主动渲染开关状态
  - 引用次数
  - 加载状态
  - 错误信息（如果有）

### 2. 当前值显示
- 以JSON格式显示变量的当前值
- 实时更新显示

### 3. 手动设置变量值
- 支持输入新的变量值（支持JSON格式）
- 点击"设置值"按钮手动触发变量值更新
- 点击"刷新值"按钮从数据源重新获取值

### 4. 调试工具
- 重新计算引用计数
- 清空调试日志

### 5. 实时日志
- 记录所有调试操作和变量变化
- 显示时间戳和操作类型
- 支持不同类型的日志（信息、成功、警告、错误）

## 使用方法

### 1. 打开调试器
在设计器编辑模式下，您会在右上角看到一个"全局变量调试器"按钮，点击即可打开调试面板。

### 2. 选择要调试的变量
在"选择变量"下拉框中选择您要调试的全局变量。

### 3. 查看变量信息
选择变量后，您可以看到：
- 变量的基本信息
- 当前值
- 是否开启了主动渲染
- 被引用的次数

### 4. 测试自动渲染功能

#### 前提条件
要测试自动渲染功能，需要满足以下条件：
1. 全局变量必须开启"主动渲染"选项
2. 必须有组件引用了该全局变量（引用次数 > 0）
3. 引用该变量的组件必须支持自动渲染

#### 测试步骤
1. 选择一个开启了主动渲染的全局变量
2. 在"设置新值"文本框中输入新的值
3. 点击"设置值"按钮
4. 观察调试日志中是否有变量更新的记录
5. 检查引用该变量的组件是否自动更新了显示

### 5. 测试场景示例

#### 场景1：测试静态数据变量
```json
{
  "name": "张三",
  "age": 25,
  "city": "北京"
}
```

#### 场景2：测试数字变量
```
100
```

#### 场景3：测试字符串变量
```
"Hello World"
```

#### 场景4：测试数组变量
```json
[1, 2, 3, 4, 5]
```

## 调试技巧

### 1. 检查主动渲染设置
如果设置变量值后没有看到组件更新，首先检查：
- 变量是否开启了"主动渲染"
- 变量的引用次数是否大于0

### 2. 查看调试日志
调试日志会记录：
- 变量值设置操作
- 订阅/取消订阅操作
- 变量值变化通知
- 错误信息

### 3. 使用刷新功能
如果变量值显示异常，可以点击"刷新值"按钮从数据源重新获取值。

### 4. 重新计算引用计数
如果引用计数显示不正确，可以点击"重新计算引用"按钮。

## 注意事项

1. **仅在编辑模式下可用**：调试器只在设计器编辑模式下显示，预览模式下不会出现。

2. **数据格式**：输入新值时，如果是JSON格式会自动解析，否则当作字符串处理。

3. **性能影响**：调试器会订阅变量变化，在生产环境中应该移除或禁用。

4. **日志限制**：调试日志最多保留50条记录，超出会自动删除旧记录。

5. **权限控制**：确保只有开发人员能够访问调试功能。

## 故障排除

### 问题1：设置值后组件没有更新
**可能原因**：
- 变量未开启主动渲染
- 组件没有正确引用该变量
- 组件没有实现自动渲染逻辑

**解决方法**：
- 检查变量配置
- 检查组件数据源配置
- 查看调试日志中的错误信息

### 问题2：调试器无法显示
**可能原因**：
- 不在编辑模式
- 组件导入错误

**解决方法**：
- 确认在设计器编辑模式下
- 检查控制台错误信息

### 问题3：变量值显示为空
**可能原因**：
- 变量尚未初始化
- 数据源配置错误

**解决方法**：
- 点击"刷新值"按钮
- 检查变量的数据源配置

## 开发说明

如果需要扩展调试器功能，可以修改 `GlobalVariableDebugger.tsx` 文件。主要的扩展点包括：

1. 添加新的调试工具
2. 增强日志功能
3. 添加性能监控
4. 集成更多的变量操作

调试器使用了 MobX 的 observer 模式，能够自动响应全局变量状态的变化。

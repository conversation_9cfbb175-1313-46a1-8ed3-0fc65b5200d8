/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {APIConfig, IDatabase, IFilterConfigType, ThemeItemType} from "../../designer/DesignerType";
import AbstractController from "./AbstractController";
import {ComponentBaseProps} from "../../comps/common-component/CommonTypes.ts";
import FetchUtil from "../../utils/FetchUtil.ts";
import Base64Util from "../../utils/Base64Util.ts";
import globalVariableManager from "../../designer/manager/GlobalVariableManager.ts";

/**
 * AbstractDesignerController继承自AbstractController，在泛型的定义和约束上和AbstractController完全保持一致。
 * 此外，AbstractDesignerController扩展了一些自定义组件所需的特有方法，如：修改组件数据、注册蓝图事件等
 */
abstract class AbstractDesignerController<I = any, C = any> extends AbstractController<I, C> {
    //轮询请求定时器
    protected interval: NodeJS.Timeout | null = null;
    //上一次数据连接状态 true：成功 false：失败
    protected lastReqState: boolean = true;
    //异常提示信息dom元素
    private errMsgDom: HTMLElement | null = null;

    /**
     * 更新组件数据,且必须触发组件的重新渲染
     * @param data
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    public changeData(data: any): void {
    }

    /**
     * 用于注册组件事件，在组件接入蓝图事件系统时使用
     */
    public registerEvent(): void {
    }


    private doApi = async (config: APIConfig) => {
        const {url, method, params, header, frequency = 5, filter, autoFlush} = config;
        const request = async () => {
            try {
                // 解析URL中的全局变量引用
                const GlobalVariableParser = (await import('../../utils/GlobalVariableParser')).default;
                const parsedUrl = await GlobalVariableParser.parseAndReplace(url!, globalVariableManager, undefined, 'text');

                // 解析请求头和参数中的全局变量引用
                const parsedHeaders = await GlobalVariableParser.parseObjectAndReplace(header || {}, globalVariableManager, undefined, 'text');
                const parsedParams = await GlobalVariableParser.parseObjectAndReplace(params || {}, globalVariableManager, undefined, 'text');

                // 如果 URL 已经包含查询参数且 params 为空，则不传递 params 避免重复添加问号
                const shouldPassParams = parsedParams && Object.keys(parsedParams).length > 0;
                const res = await FetchUtil.doRequestNativeResult(
                    parsedUrl,
                    method!,
                    parsedHeaders,
                    shouldPassParams ? parsedParams : undefined
                );

                if (res) {
                    if (!this.lastReqState) {
                        this.lastReqState = true;
                        this.errMsgDom?.remove();
                        this.errMsgDom = null;
                    }
                    let finalRes = res;
                    if (filter && filter !== '') {
                        // 使用GlobalVariableManager的过滤器执行方法，确保支持 ${GV::variableName} 格式
                        finalRes = await globalVariableManager._parseAndExecuteFilter(
                            filter,
                            res,
                            'api-component-filter' // API组件过滤器的虚拟ID
                        );
                    }
                    this.changeData(finalRes);
                } else {
                    this.lastReqState = false;
                    //请求失败，在原有容器的基础上添加异常提示信息的dom元素（此处直接操作dom元素，不适用react的api进行组件的反复挂载和卸载）
                    if (!this.errMsgDom) {
                        this.errMsgDom = document.createElement("div");
                        this.errMsgDom.classList.add("view-error-message");
                        this.errMsgDom.innerText = "数据加载失败...";
                        this.container!.appendChild(this.errMsgDom);
                    }
                }
            } catch (error) {
                console.error('API请求失败:', error);
                this.lastReqState = false;
                if (!this.errMsgDom) {
                    this.errMsgDom = document.createElement("div");
                    this.errMsgDom.classList.add("view-error-message");
                    this.errMsgDom.innerText = "数据加载失败...";
                    this.container!.appendChild(this.errMsgDom);
                }
            }
        }
        request();
        if (autoFlush)
            this.interval = setInterval(() => request(), frequency * 1000);
    }

    private doDatabase = (config: IDatabase) => {
        const {sql, targetDb, filter, frequency, autoFlush} = config;
        const request = () => {
            if (!sql || sql === '')
                return;
            FetchUtil.post(`/api/db/executor/execute`, {id: targetDb, sql: Base64Util.toBase64(sql)}).then(res => {
                let {data} = res;
                if (res.code === 200) {
                    if (!this.lastReqState) {
                        this.lastReqState = true;
                        this.errMsgDom?.remove();
                        this.errMsgDom = null;
                    }
                    if (filter && filter !== '') {
                        const func = eval(`(${filter})`);
                        data = typeof func === 'function' ? func(data) : data;
                    }
                    this.changeData(data);
                } else {
                    this.lastReqState = false;
                    //请求失败，在原有容器的基础上添加异常提示信息的dom元素（此处直接操作dom元素，不适用react的api进行组件的反复挂载和卸载）
                    if (!this.errMsgDom) {
                        this.errMsgDom = document.createElement("div");
                        this.errMsgDom.classList.add("view-error-message");
                        this.errMsgDom.innerText = "数据加载失败...";
                        this.container!.appendChild(this.errMsgDom);
                    }
                }
            });
        }
        request();
        if (autoFlush)
            this.interval = setInterval(() => request(), (frequency || 5) * 1000);
    }

    /**
     * 加载组件数据，用于在预览（展示）模式下渲染完组件后根据当前组件的数据配置自动加载并更新组件数组。
     * 注：若自定义组件有自己的数据加载方式，则需要覆写此方法
     */
    public async loadComponentData(): Promise<void> {
        //预览模式
        const {data} = this.config! as ComponentBaseProps;
        if (!data) return;
        const {sourceType} = data!;
        switch (sourceType) {
            case "static":
                //静态数据不做处理，组件首次渲染时默认读取静态数据
                break;
            case "api":
                this.doApi(data?.apiData ?? {});
                break;
            case 'database':
                this.doDatabase(data?.database ?? {});
                break;
            case 'globalVariable':
                await this.doGlobalVariable(data?.selectedGlobalVariableId);
                break;
        }
    }

    /**
     * 刷新静态数据（当全局变量变化时调用）
     */
    public refreshStaticData = async () => {
        const {data} = this.config! as ComponentBaseProps;
        if (!data || data.sourceType !== 'static') return;

        const {rawStaticData, staticDataFilter} = data;
        if (!staticDataFilter || staticDataFilter.trim() === '') return;

        // 使用原始数据，如果不存在则使用 staticData（向后兼容）
        const sourceData = rawStaticData !== undefined ? rawStaticData : data.staticData;
        if (sourceData === undefined) return;

        try {
            // 重新执行过滤器
            const finalData = await globalVariableManager._parseAndExecuteFilter(
                staticDataFilter,
                sourceData,
                'static-data-refresh' // 静态数据刷新的虚拟ID
            );

            // 更新 staticData 和组件显示
            this.config!.data!.staticData = finalData;
            this.changeData(finalData);
        } catch (error) {
            console.error('静态数据过滤器刷新执行错误:', error);
        }
    };

    /**
     * 处理全局变量数据源
     */
    public doGlobalVariable = async (variableId?: string) => {
        if (!variableId) return;

        const {data} = this.config! as ComponentBaseProps;
        const globalVariableFilter = data?.globalVariableFilter;

        // 获取全局变量定义和状态
        const definition = globalVariableManager.getVariableDefinition(variableId);
        if (!definition) {
            console.error(`全局变量 ${variableId} 不存在`);
            return;
        }

        let variableValue = globalVariableManager.getVariableValue(variableId);

        // 检查是否正在加载
        const isLoading = globalVariableManager.isLoading.get(variableId);
        const errorState = globalVariableManager.errorStates.get(variableId);

        // 如果值为undefined，检查是否有错误状态或初始值
        if (variableValue === undefined || variableValue === null) {
            if (isLoading) {
                // 正在加载中，暂不处理
                console.log(`全局变量 ${definition.name} 正在加载中...`);
                return;
            } else if (errorState) {
                // 有错误，但可能有初始值/默认值
                if (definition.initialOrFallbackValue !== undefined) {
                    variableValue = definition.initialOrFallbackValue;
                    console.warn(`全局变量 ${definition.name} 数据获取失败，使用默认值:`, errorState);
                } else {
                    console.error(`全局变量 ${definition.name} 获取失败且无默认值:`, errorState);
                    return;
                }
            } else {
                console.warn(`全局变量 ${definition.name} 值未定义`);
                return;
            }
        }

        // 如果组件配置了全局变量过滤器，应用过滤器
        if (globalVariableFilter && globalVariableFilter.trim() !== '') {
            try {
                // 使用GlobalVariableManager的过滤器执行方法，确保一致性
                variableValue = await globalVariableManager._parseAndExecuteFilter(
                    globalVariableFilter,
                    variableValue,
                    variableId
                );
            } catch (error) {
                console.error('全局变量过滤器执行错误:', error);
                // 过滤器执行失败时使用原始值
            }
        }

        this.changeData(variableValue);

        // 如果全局变量开启了主动渲染，订阅变量变化
        if (definition?.isActiveRendering) {
            const componentId = (this.config as ComponentBaseProps)?.base?.id;
            if (componentId) {
                globalVariableManager.subscribe(variableId, componentId, async (newValue: any) => {
                    // 订阅回调中也需要应用过滤器
                    let finalValue = newValue;
                    if (globalVariableFilter && globalVariableFilter.trim() !== '') {
                        try {
                            // 使用GlobalVariableManager的过滤器执行方法，确保一致性
                            finalValue = await globalVariableManager._parseAndExecuteFilter(
                                globalVariableFilter,
                                newValue,
                                variableId
                            );
                        } catch (error) {
                            console.error('全局变量过滤器执行错误:', error);
                            // 过滤器执行失败时使用原始值
                        }
                    }
                    this.changeData(finalValue);
                });
            }
        }
    };

    /**
     * 仅建立全局变量订阅关系，不加载数据（用于编辑模式）
     */
    public setupGlobalVariableSubscription = async (variableId?: string) => {
        if (!variableId) return;

        const {data} = this.config! as ComponentBaseProps;
        const globalVariableFilter = data?.globalVariableFilter;

        // 获取全局变量定义
        const definition = globalVariableManager.getVariableDefinition(variableId);
        if (!definition) {
            console.error(`全局变量 ${variableId} 不存在`);
            return;
        }

        // 如果全局变量开启了主动渲染，订阅变量变化
        if (definition?.isActiveRendering) {
            const componentId = (this.config as ComponentBaseProps)?.base?.id;
            if (componentId) {
                globalVariableManager.subscribe(variableId, componentId, async (newValue: any) => {
                    // 订阅回调中也需要应用过滤器
                    let finalValue = newValue;
                    if (globalVariableFilter && globalVariableFilter.trim() !== '') {
                        try {
                            // 使用GlobalVariableManager的过滤器执行方法，确保一致性
                            finalValue = await globalVariableManager._parseAndExecuteFilter(
                                globalVariableFilter,
                                newValue,
                                variableId
                            );
                        } catch (error) {
                            console.error('全局变量过滤器执行错误:', error);
                            // 过滤器执行失败时使用原始值
                        }
                    }
                    this.changeData(finalValue);
                });
            }
        }
    };

    /**
     * 销毁组件，清理资源
     */
    public destroy(): void {
        // 清理定时器
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }

        // 清理错误提示DOM
        if (this.errMsgDom) {
            this.errMsgDom.remove();
            this.errMsgDom = null;
        }

        // 取消全局变量订阅并更新引用计数
        const {data} = this.config as ComponentBaseProps || {};
        if (data?.sourceType === 'globalVariable' && data.selectedGlobalVariableId) {
            const componentId = (this.config as ComponentBaseProps)?.base?.id;
            if (componentId) {
                globalVariableManager.unsubscribe(data.selectedGlobalVariableId, componentId);
                // 减少引用计数
                globalVariableManager.decrementReferenceCount(data.selectedGlobalVariableId);
            }
        }

        // 重新计算所有引用计数（确保准确性）
        globalVariableManager.recalculateAllReferenceCounts();

        // 调用父类销毁方法
        super.destroy();
    }

    /**
     * 更新本组件的主题样式方法，用于在全局切换主题时使用
     * @param newTheme 新主题
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    public updateTheme(newTheme: ThemeItemType): void {
    }

    public updateFilter(filter: IFilterConfigType): void {
        if (this.config && (this.config as ComponentBaseProps).filter)
            (this.config as ComponentBaseProps)!.filter = filter;
        if (!this.container)
            return;
        if (filter?.enable) {
            this.container.style.filter = `blur(${filter.blur}px) brightness(${filter.brightness}) contrast(${filter.contrast}) opacity(${filter.opacity}) saturate(${filter.saturate}) hue-rotate(${filter.hueRotate}deg)`
        } else {
            this.container.style.filter = 'none';
        }
    }

}

export default AbstractDesignerController;

.gv-debugger-panel {
  .gv-debugger-section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .gv-debugger-logs {
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 8px;

    .gv-debugger-log {
      padding: 4px 0;
      border-bottom: 1px solid #e8e8e8;
      font-size: 12px;

      &:last-child {
        border-bottom: none;
      }

      &.gv-debugger-log-info {
        .ant-typography {
          color: #1890ff;
        }
      }

      &.gv-debugger-log-success {
        .ant-typography {
          color: #52c41a;
        }
      }

      &.gv-debugger-log-warning {
        .ant-typography {
          color: #faad14;
        }
      }

      &.gv-debugger-log-error {
        .ant-typography {
          color: #ff4d4f;
        }
      }
    }
  }
}

// 浮动按钮样式
.ant-float-btn {
  &.gv-debugger-float-btn {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

    &:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }
  }
}

// 调试面板样式优化
.gv-debugger-panel {
  .ant-card {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border-radius: 8px;

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-weight: 600;
      }
    }
  }
}

/* 深色主题适配 */
.dark-theme {
  .gv-debugger-panel {
    .gv-debugger-logs {
      background: #1f1f1f;
      border-color: #434343;

      .gv-debugger-log {
        border-bottom-color: #434343;
      }
    }
  }
}

/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import './Legend.less';
import {FieldChangeData, LCGUI} from "../../../../json-schema/LCGUI";
import {Control} from "../../../../json-schema/SchemaTypes";
import {ConfigType} from "../../../../designer/right/ConfigContent";
import cloneDeep from "lodash/cloneDeep";
import {useState} from "react";
import ColorUtil from "../../../../utils/ColorUtil";

const defaultLegendConfig = {
    legend: {
        position: "top",
        layout: "horizontal",
        itemName: {
            style: {
                fill: "#989898ff",
                fontSize: 10,
            },
        },
    }
}

export const AntdLegend = (props: ConfigType) => {
    const {controller} = props;
    const {legend} = controller.getConfig().style;
    const [count, setCount] = useState(0);

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataFragment, reRender} = fieldChangeData;
        if (id === 'legendSwitch') {
            if (data) controller.update({style: cloneDeep(defaultLegendConfig)});
            else controller.update({style: {legend: false}});
        } else if (id === 'legendFill') {
            // 处理图例文本颜色
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                const config = controller.getConfig().style;
                controller.update({
                    style: {
                        legend: {
                            ...config.legend,
                            itemName: {
                                ...config.legend?.itemName,
                                style: {
                                    ...config.legend?.itemName?.style,
                                    fill: gradientCss
                                }
                            }
                        }
                    }
                });
            } else {
                // 处理普通颜色
                const config = controller.getConfig().style;
                controller.update({
                    style: {
                        legend: {
                            ...config.legend,
                            itemName: {
                                ...config.legend?.itemName,
                                style: {
                                    ...config.legend?.itemName?.style,
                                    fill: data as string
                                }
                            }
                        }
                    }
                });
            }
        } else {
            controller.update(dataFragment);
        }
        if (reRender)
            setCount(count + 1);
    }

    const schema: Control = {
        key: 'style',
        id: 'legendSwitch',
        type: 'accordion',
        label: '图例',
        config: {showSwitch: true},
        value: !!legend,
        children: [
            {
                key: 'legend',
                type: 'grid',
                children: [
                    {
                        id: 'legendSwitch',
                        type: 'switch',
                        label: '显示',
                        reRender: true,
                        value: !!legend
                    },
                    {
                        rules: `"${!!legend}" === "true"`,
                        children: [
                            {
                                key: 'position',
                                type: 'select',
                                label: '位置',
                                value: 'left-top',
                                config: {
                                    options: [
                                        {value: 'left-top', label: '左上'},
                                        {value: 'left', label: '正左'},
                                        {value: 'left-bottom', label: '左下'},
                                        {value: 'top-left', label: '上左'},
                                        {value: 'top', label: '正上'},
                                        {value: 'top-right', label: '上右'},
                                        {value: 'right-top', label: '右上'},
                                        {value: 'right', label: '正右'},
                                        {value: 'right-bottom', label: '右下'},
                                        {value: 'bottom-left', label: '下左'},
                                        {value: 'bottom', label: '正下'},
                                        {value: 'bottom-right', label: '下右'},
                                    ]
                                }
                            },
                            {
                                key: 'layout',
                                type: 'select',
                                label: '方向',
                                value: 'horizontal',
                                config: {
                                    options: [
                                        {value: 'horizontal', label: '水平'},
                                        {value: 'vertical', label: '垂直'},
                                    ]
                                }
                            },
                            {
                                key: 'itemName',
                                children: [
                                    {
                                        key: 'style',
                                        children: [
                                            {
                                                key: 'fontSize',
                                                type: 'number-input',
                                                label: '字号',
                                                value: 12,
                                                config: {
                                                    min: 0,
                                                    max: 100,
                                                }
                                            },
                                            {
                                                id: 'legendFill',
                                                key: 'fill',
                                                type: 'enhanced-color-mode',
                                                label: '颜色',
                                                value: '#1c1c1c',
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }

    return (
        <LCGUI schema={schema} onFieldChange={onFieldChange}/>
    );
};

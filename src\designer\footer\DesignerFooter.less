/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

@import "../../designer/style/DesignerStyle.less";

.lc-designer-footer {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #404040;

  .footer-left {
    display: flex;
    padding-left: 10px;

    .footer-item {
      cursor: pointer;
      margin-right: 10px;
      color: #bababa;
      font-size: 14px;

      .i-icon {
        position: relative;
        top: 3px;
      }

      &:hover {
        transition: all 0.2s;
        color: #0095db;
      }

      span {
        padding: 2px;
      }
    }
  }

  .footer-center {
    display: flex;

    .footer-center-item {
      cursor: pointer;
      margin: 0 5px;
      padding: 5px;
      border-radius: 5px;
      transition: all 0.2s;
      font-size: 18px;

      .i-icon {
        position: relative;
        top: 3px;
      }

    }

    .footer-center-item-active {
      color: #00b2ff;
    }

    .footer-center-item:hover {
      color: #00b2ff;
    }
  }

  .footer-right {
    display: flex;

    .right-info-item {
      color: @footer-item-color;
      margin-right: 20px;
    }
  }
}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.control-group {
  width: 100%;
  padding: 10px;

  .control-group-header {
    color: #e4f4ff;
    width: 100%;
    display: flex;
    justify-content: space-between;

    .cgh-operate {

      .operate-icon {
        cursor: pointer;
        padding: 3px;
        position: relative;
        top: 1px;
      }

      .toggle-icon {
        top: 2px;
      }
    }
  }

  .control-group-body {
    padding: 5px;

    .lc-accordion {

      .accordion-header {
        color: #c6c6c6;
        font-size: 12px;
      }
    }
  }
}
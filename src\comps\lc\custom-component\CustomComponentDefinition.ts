/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import { BaseInfoType, EventInfo, MenuToConfigMappingType } from "../../../framework/core/AbstractDefinition";
import { MenuInfo } from "../../../designer/right/MenuType";
import { ClazzTemplate } from "../../common-component/CommonTypes";
import AbstractDesignerDefinition from "../../../framework/core/AbstractDesignerDefinition";
import React from "react";
import customComponentImg from './custom-component.png';
import { CustomComponentController } from "./CustomComponentController";
import { CustomComponentProps } from "./CustomComponent";
import { Code } from "@icon-park/react";

const BaseInfo = React.lazy(() => import("../../common-component/base-info/BaseInfo"));
const ThemeConfig = React.lazy(() => import("../../common-component/theme-config/ThemeConfig"));
const AnimationConfig = React.lazy(() => import("../../common-component/animation-config/AnimationConfig"));
const FilterConfig = React.lazy(() => import("../../common-component/filter-config/FilterConfig"));
const DataConfig = React.lazy(() => import("../../common-component/data-config/DataConfig"));
const CustomComponentConfig = React.lazy(() => import("./CustomComponentConfig"));

/**
 * 自定义组件定义类
 * 继承自AbstractDesignerDefinition，实现自定义组件的定义
 */
export default class CustomComponentDefinition extends AbstractDesignerDefinition<CustomComponentController, CustomComponentProps> {
    /**
     * 获取组件基本信息
     */
    getBaseInfo(): BaseInfoType {
        return {
            compName: "自定义组件",
            compKey: "CustomComponent",
            categorize: "other",  // 放在"其他"分类下
            width: 300,
            height: 200,
        };
    }

    /**
     * 获取组件初始配置
     */
    getInitConfig(): CustomComponentProps {
        return {
            base: {
                id: "",
                name: "自定义组件",
                type: "CustomComponent",
            },
            style: {
                background: "#ffffff",
                borderRadius: 4,
                padding: 16,
            },
            filter: {
                enable: false,
                blur: 0,
                brightness: 1,
                contrast: 1,
                opacity: 1,
                saturate: 1,
                hueRotate: 0
            },
            data: {
                sourceType: 'static',
                staticData: "[]"
            },
            customCode: {
                code: `function render(data, globalVars, theme) {
  // 可用组件: Antd.Button, Antd.Tag, Antd.Input 等
  // 可用工具: globalVars.get(name)
  // 可用主题: theme.colors.mainText, theme.colors.subText 等

  return (
    <div style={{padding: '20px'}}>
      <Antd.Typography.Title
        level={4}
        style={{color: theme?.colors?.mainText || '#000'}}>
        自定义组件
      </Antd.Typography.Title>
      <Antd.Divider />
      <Antd.Button type="primary">点击按钮</Antd.Button>
    </div>
  );
}`,
                isPreview: false, // 默认不预览
            }
        };
    }

    /**
     * 获取组件控制器
     */
    getController(): ClazzTemplate<CustomComponentController> | null {
        return CustomComponentController;
    }

    /**
     * 获取组件图片
     */
    getChartImg(): string | null {
        return customComponentImg;
    }

    /**
     * 获取组件配置菜单列表
     */
    getMenuList(): Array<MenuInfo> {
        const menuList = super.getMenuList();
        // 添加自定义配置菜单
        menuList.splice(2, 0, {
            icon: Code, 
            name: '自定义代码',
            key: 'customCode',
        });
        return menuList;
    }

    /**
     * 获取菜单到配置组件的映射
     */
    getMenuToConfigContentMap(): MenuToConfigMappingType {
        const configMap = super.getMenuToConfigContentMap();
        configMap['customCode'] = CustomComponentConfig;
        return configMap;
    }

    /**
     * 获取组件支持的事件列表
     */
    getEventList(): EventInfo[] {
        return [
            ...super.getEventList(),
            {
                id: "click",
                name: "点击时",
            }
        ];
    }
} 
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.search-layer-dialog {
  .search-layer-body {
    max-height: 300px;
    margin-top: 5px;
    overflow: auto;

    .search-item {
      width: 100%;
      display: flex;
      height: 35px;
      padding: 10px;
      align-items: center;
      color: #a1a1a1;
      cursor: pointer;
      transition: background-color, color 0.3s ease;
    }

    .search-item:hover {
      background-color: #2c2c2c;
      color: white;
    }
  }
}
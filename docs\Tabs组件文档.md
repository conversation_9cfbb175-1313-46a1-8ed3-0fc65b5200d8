# 自定义Tab组组件 - 需求与技术方案

## 需求文档

### 1. 背景与目标

#### 1.1 背景
当前设计器缺少一种原生的、可配置的Tab或按钮组交互组件。用户需要一种方式来创建一组可点击的元素（Tabs），通过点击不同的Tab来切换看板上的内容或状态，并通过蓝图系统与其他组件进行联动。

#### 1.2 目标
开发一个“自定义Tab组”组件，它作为一种**非可视化的控制器组件**，用于在画布上动态生成和管理一组可视化的、可独立配置的“子Tab”组件。实现以下核心能力：

1.  **配置驱动生成**：在Tab组的配置面板中添加/删除Tab，能实时在画布上创建/删除对应的子Tab组件。
2.  **独立样式**：每个子Tab组件都拥有独立的、可自由编辑的样式，包括激活（Active）和未激活（Inactive）两种状态。
3.  **自由布局**：生成的子Tab组件是标准画布元素，支持自由拖拽、缩放、对齐等所有常规操作。
4.  **蓝图交互**：
    *   **输出**：点击不同的子Tab，Tab组能通过蓝图输出锚点传递出该子Tab的预设值。
    *   **输入**：Tab组能接收一个值，并根据该值自动激活对应的子Tab。
5.  **资源复用**：子Tab的背景图片功能应复用现有的“资源库”体系，实现图片的上传和复用。

### 2. 核心概念

#### 2.1 自定义Tab组 (CustomTabGroup)
*   **角色**：控制器/容器组件。
*   **分类**：位于组件库新增的“**交互**”分类下。
*   **设计器中表现**：拖拽到画布上时，显示为一个虚线框的占位符，仅用于标识位置和提供配置入口。
*   **预览时表现**：**完全不可见**。它本身不参与最终的视觉呈现。
*   **核心职责**：管理其下所有子Tab的配置、状态（哪个是激活的）和蓝图交互逻辑。

#### 2.2 子Tab (ChildTab)
*   **角色**：可视化的、可交互的组件。
*   **创建方式**：由“自定义Tab组”的配置面板动态生成，**不直接出现在组件库中**。
*   **设计器中表现**：一个标准的画布组件，可以被选中、拖拽、缩放、设置图层顺序等。
*   **核心职责**：展示自身样式（激活/未激活），并响应用户的点击事件，将事件通知给父级的Tab组。

### 3. 功能详述

#### 3.1 自定义Tab组 (CustomTabGroup)
##### 3.1.1 组件库
- 在组件库中新增“交互”分类。
- 在该分类下添加“自定义Tab组”组件。

##### 3.1.2 配置面板（双击Tab组占位符打开）
- **Tab列表管理**：
  - 一个可编辑的列表，每一项代表一个子Tab。
  - 列表项包含两个可配置字段：“**显示名称**”和“**值**”。“值”是蓝图交互时传递的数据。
  - 提供“新增Tab”按钮，点击后在列表末尾添加一项，并在画布上生成一个新的子Tab。
  - 列表项提供“删除”按钮，点击后从列表和画布中同时移除对应的子Tab。
- **初始激活项设置**：
  - 提供一个下拉框，可以选择一个已配置的Tab的“值”作为默认的激活项。

##### 3.1.3 蓝图交互
- **输出锚点**：
  - `onValueChanged` (值变化时)：当用户点击任何一个子Tab导致激活项变化时，从此锚点输出新激活Tab的“值”。
- **输入锚点**：
  - `setActiveTab` (设置激活项)：接收一个“值”，并激活与之匹配的子Tab。

#### 3.2 子Tab (ChildTab)
##### 3.2.1 独立性
- 每个子Tab在图层列表中都是一个独立的图层。
- 支持所有标准图层操作（重命名、锁定、隐藏、调整层级、编组等）。

##### 3.2.2 配置面板（双击子Tab组件打开）
- 提供两个主要的可折叠配置区：“**激活样式**”和“**未激活样式**”。
- 每个样式区内包含以下可配置项：
  - **基础样式**：尺寸、位置、字体、颜色、背景色、边框、圆角等常规样式。
  - **背景图片**：
    - 一个图片上传控件。
    - 上传逻辑应与现有的“基础图片”组件保持一致。
    - 上传的图片应自动加入到“资源库-图片”中，支持后续复用。

---

## 技术实现方案

### 1. 核心数据结构设计

#### 1.1 CustomTabGroup 组件配置 (`CustomTabGroupComponentProps`)
```typescript
// Tab组中每个Tab的配置项
interface TabConfigItem {
  id: string; // 唯一标识，用于关联
  name: string; // 显示名称
  value: any;   // 蓝图交互的值
  childTabId: string; // 关联的画布上子Tab组件的ID
}

// Tab组的样式/核心配置
interface CustomTabGroupStyle {
  tabs: TabConfigItem[];
  activeTabValue: any; // 当前激活的Tab的值
}

// Tab组组件的完整Props
interface CustomTabGroupComponentProps extends ComponentBaseProps {
  style: CustomTabGroupStyle;
}
```

#### 1.2 ChildTab 组件配置 (`ChildTabComponentProps`)
```typescript
// 背景图片样式，可复用或参考BaseImageComponentStyle
interface BackgroundImageStyle {
  type?: 'online' | 'local';
  onLineUrl?: string;
  localUrl?: string;
  hash?: string;
}

// 子Tab单个状态的样式
interface ChildTabStateStyle {
  background?: string;
  backgroundImage?: BackgroundImageStyle;
  color?: string;
  borderColor?: string;
  // ... 其他所有可配置的CSS属性
}

// 子Tab的核心配置
interface ChildTabStyle {
  parentId: string; // 指向父级TabGroup的ID
  value: any; // 自己的值，用于匹配激活状态
  isActive: boolean; // 自身是否激活的状态（由父级控制）
  activeStyle: ChildTabStateStyle;
  inactiveStyle: ChildTabStateStyle;
}

// 子Tab组件的完整Props
interface ChildTabComponentProps extends ComponentBaseProps {
  style: ChildTabStyle;
}
```

### 2. 组件实现

#### 2.1 `CustomTabGroupDefinition.ts` & `CustomTabGroupController.ts`
- **Definition**:
  - `getBaseInfo`: 定义组件名、key，并设置`categorize: '交互'`。
  - `getInitConfig`: 初始化`style.tabs`为空数组。
  - `getMenuToConfigContentMap`: 指向配置面板组件`CustomTabGroupConfig`。
  - `getEventList` / `getActionList`: 定义蓝图的`onValueChanged`输出和`setActiveTab`输入锚点。
- **Controller**:
  - **`create()`**: 在画布上渲染一个虚线框占位符。
  - **`update(config)`**: **核心逻辑**。对比新旧`config.style.tabs`数组，通过`historyRecordOperateProxy.doAdd()`和`historyRecordOperateProxy.doDelete()`动态增删`ChildTab`组件，并同步更新`TabConfigItem`中的`childTabId`。
  - **`execute()`**: 实现蓝图`setActiveTab`输入逻辑，调用`this.setActiveTab(params)`。
  - **`setActiveTab(value)`**: 更新自身的`activeTabValue`，并遍历所有子Tab，调用其`controller.update()`来更新它们的`isActive`状态。
  - **`handleChildClick(childValue)`**: 暴露给子Tab调用。它会先调用`setActiveTab(childValue)`，然后触发蓝图`onValueChanged`输出锚点。

#### 2.2 `ChildTabDefinition.ts` & `ChildTabController.ts`
- **Definition**:
  - 定义`ChildTab`组件，但**不提供`categorize`属性**，使其不显示在组件库中。
  - `getInitConfig`: 提供默认的`activeStyle`和`inactiveStyle`。
  - `getMenuToConfigContentMap`: 指向配置面板组件`ChildTabConfig`。
- **Controller**:
  - **`create()`**: 正常创建组件。
  - **`update()`**: 当`style.isActive`属性变化时，切换应用`activeStyle`或`inactiveStyle`到组件的实际渲染样式上。
- **Component (`ChildTab.tsx`)**:
  - 注册`onClick`事件，事件触发时通过`layerManager.compController[props.style.parentId]`获取父级`controller`，并调用其`handleChildClick`方法。
  - 根据`props.style.isActive`来决定渲染哪个样式对象。

#### 2.3 `ChildTabConfig.tsx`
- 使用两个`Accordion`组件来分别包裹“激活样式”和“未激活样式”的配置区。
- **背景图片**：直接使用现有的`ImageUpload`组件。其`onChange`回调会返回包含`url`和`hash`的对象，将其存入对应的`style.backgroundImage`中。

---

## 分阶段实施路径 (Phased Implementation Plan)

### 阶段一：组件骨架和基础配置
- **任务**:
  1.  创建`CustomTabGroup`的所有相关文件（Definition, Controller, Component, Config）。
  2.  在`CustomTabGroupDefinition`中定义好`getBaseInfo`，使其出现在新的“交互”分类下。
  3.  `CustomTabGroupComponent`仅渲染一个简单的虚线框占位符。
  4.  创建`ChildTab`的所有相关文件。`ChildTabDefinition`不设置`categorize`，`ChildTabComponent`渲染一个简单的div。
  5.  实现`CustomTabGroupConfig.tsx`的基础UI，只包含一个可增删的列表，列表项仅有`name`和`value`输入框。
- **测试目标**:
  - `CustomTabGroup`能从组件库拖到画布上。
  - 双击`CustomTabGroup`占位符能打开其配置面板。
  - 在配置面板中可以增加、删除Tab项的配置数据（此时还不会影响画布）。

### 阶段二：动态创建与管理子Tab
- **任务**:
  1.  实现`CustomTabGroupController`的`update`核心逻辑。
  2.  当在配置面板中点击“新增Tab”时，调用`historyRecordOperateProxy.doAdd()`创建一个新的`ChildTab`组件，并将其ID存回`CustomTabGroup`的配置中。
  3.  当在配置面板中点击“删除Tab”时，调用`historyRecordOperateProxy.doDelete()`删除对应的`ChildTab`组件。
  4.  确保项目保存和加载时，`CustomTabGroup`和其动态创建的`ChildTab`都能被正确序列化和恢复。
- **测试目标**:
  - 在配置面板中新增一项，画布上会多出一个`ChildTab`组件。
  - 在配置面板中删除一项，画布上对应的`ChildTab`组件会消失。
  - 保存并重新加载项目后，Tab组和所有子Tab都恢复正常。

### 阶段三：子Tab样式与状态切换
- **任务**:
  1.  完善`ChildTabConfig.tsx`，实现“激活样式”和“未激活样式”两个区域，并放入基础样式控件（如背景色、字体颜色）。
  2.  在`CustomTabGroup`的配置中添加“默认激活项”的设置。
  3.  实现`CustomTabGroupController.setActiveTab(value)`方法，该方法能遍历所有子Tab并更新它们的`isActive`状态。
  4.  实现`ChildTabController.update()`方法，根据`isActive`状态，将`activeStyle`或`inactiveStyle`应用到`ChildTabComponent`上。
- **测试目标**:
  - 可以为子Tab分别配置激活和未激活的样式。
  - 项目加载时，默认的子Tab显示为激活样式。
  - 在`CustomTabGroup`的配置中切换默认激活项，画布上子Tab的样式会相应变化。

### 阶段四：点击交互与蓝图集成
- **任务**:
  1.  在`ChildTabComponent`中实现`onClick`事件，事件触发时调用父级`CustomTabGroupController`的`handleChildClick`方法。
  2.  在`CustomTabGroupController`中实现`handleChildClick`，使其能调用`setActiveTab`并触发蓝图`onValueChanged`输出锚点。
  3.  在`CustomTabGroupController`中实现`execute`方法，处理蓝图`setActiveTab`输入锚点。
- **测试目标**:
  - 在预览模式下，点击不同的子Tab，其样式会正确切换。
  - 在蓝图编辑器中，能看到`CustomTabGroup`的输入和输出锚点。
  - 连接`onValueChanged`输出到其他节点，点击子Tab时能看到对应的值被传递出去。
  - 通过其他节点（如按钮）向`setActiveTab`输入一个值，对应的子Tab能被正确激活。

### 阶段五：背景图片功能实现
- **任务**:
  1.  在`ChildTabConfig.tsx`的激活/未激活样式区域中，集成`ImageUpload`组件。
  2.  确保`ImageUpload`组件的上传逻辑能正确调用`LocalOperator`或`ServerOperator`，并将图片信息存入资源库。
  3.  `ChildTabComponent`能正确读取`backgroundImage`的`url`并作为背景图渲染。
  4.  参考`BaseImageConvert.ts`，为`ChildTab`组件实现一个数据转换器，确保在项目导出/导入时，本地图片的`blob`链接能正确处理。
- **测试目标**:
  - 可以为子Tab的激活/未激活状态分别上传背景图。
  - 上传的图片出现在资源库中，并可以被其他组件复用。
  - 项目导出再导入后，背景图片能正常显示。

### 阶段六：最终优化与测试
- **任务**:
  1.  代码审查和重构。
  2.  完善错误处理和边界情况（例如，删除一个正在被蓝图引用的Tab组）。
  3.  添加详细的组件使用说明和注释。
  4.  进行全面的功能和交互测试。
- **测试目标**:
  - 组件在各种操作下表现稳定、可靠，无明显Bug。
  - 代码清晰，易于维护和二次开发。
# 蓝图系统"更新组件样式"使用指南

## 概述

在蓝图系统中，每个组件节点都有一个"更新组件样式"输入锚点，允许您通过蓝图逻辑动态修改组件的样式配置。

## 工作原理

### 1. 锚点定义
- **锚点名称**: "更新组件样式"
- **锚点ID**: `updateConfig`
- **锚点类型**: 输入锚点 (INPUT)
- **处理函数**: `controller.update(params)`

### 2. 实现机制
```typescript
{
    name: "更新组件样式",
    id: "updateConfig", 
    handler: (controller: AbstractDesignerController, params?: object) => {
        controller.update(params);
    }
}
```

## 参数格式

### 基本结构
输入参数应该是一个对象，包含您想要更新的组件配置部分：

```javascript
{
    style: {
        // 样式配置
    },
    data: {
        // 数据配置
    },
    base: {
        // 基础配置
    }
}
```

### 常用样式配置示例

#### 1. 基础文本组件
```javascript
{
    style: {
        fontSize: 16,
        color: "#ff0000",
        fontWeight: "bold",
        textAlign: "center"
    }
}
```

#### 2. 颜色块组件
```javascript
{
    style: {
        background: "#ff6b6b",
        borderRadius: 10,
        borderWidth: 2,
        borderColor: "#333",
        borderStyle: "solid"
    }
}
```

#### 3. G2Plot图表组件
```javascript
{
    style: {
        color: ["#ff6b6b", "#4ecdc4", "#45b7d1"],
        xAxis: {
            label: {
                style: {
                    fill: "#333",
                    fontSize: 12
                }
            }
        },
        yAxis: {
            label: {
                style: {
                    fill: "#666",
                    fontSize: 10
                }
            }
        }
    }
}
```

#### 4. G2Plot自定义组件
```javascript
// 方式1: 更新customCode（会重新创建图表）
{
    style: {
        customCode: `function renderG2Plot(container, G2Plot, data, globalVars, dynamicStyle) {
            const color = globalVars.get('primaryColor') || '#1890ff';
            const baseConfig = {
                data: data,
                xField: 'category',
                yField: 'value',
                color: color
            };
            // 合并动态样式
            const finalConfig = dynamicStyle ?
                Object.assign({}, baseConfig, dynamicStyle) : baseConfig;
            const g2plot = new G2Plot.Column(container, finalConfig);
            g2plot.render();
            return g2plot;
        }`
    }
}

// 方式2: 只更新样式配置（性能更好，不重新创建图表）
{
    style: {
        xAxis: {
            label: {
                style: {
                    fill: "#ff0000",
                    fontSize: 14
                }
            }
        },
        yAxis: {
            label: {
                style: {
                    fill: "#00ff00",
                    fontSize: 12
                }
            }
        },
        color: ["#ff6b6b", "#4ecdc4", "#45b7d1"]
    }
}
```

## 实际使用场景

### 场景1: 根据数据动态改变颜色
```javascript
// 在逻辑处理节点中
function processData(data) {
    const value = data.value;
    let color;
    
    if (value > 100) {
        color = "#ff4757"; // 红色
    } else if (value > 50) {
        color = "#ffa502"; // 橙色  
    } else {
        color = "#2ed573"; // 绿色
    }
    
    return {
        style: {
            background: color
        }
    };
}
```

### 场景2: 主题切换
```javascript
// 切换到暗色主题
{
    style: {
        background: "#2c3e50",
        color: "#ecf0f1",
        borderColor: "#34495e"
    }
}

// 切换到亮色主题  
{
    style: {
        background: "#ffffff",
        color: "#2c3e50", 
        borderColor: "#bdc3c7"
    }
}
```

### 场景3: 动画效果
```javascript
// 高亮效果
{
    style: {
        background: "#f39c12",
        borderWidth: 3,
        borderColor: "#e67e22"
    }
}

// 恢复正常
{
    style: {
        background: "#3498db",
        borderWidth: 1,
        borderColor: "#2980b9"
    }
}
```

## 注意事项

### 1. 参数合并
- 传入的参数会与现有配置进行**深度合并**
- 只需要传入您想要修改的部分，不需要完整的配置对象
- 未指定的属性会保持原有值

### 2. 重新渲染
- 默认情况下，更新样式会触发组件重新渲染
- 某些组件可能支持增量更新以提高性能

### 3. 类型安全
- 确保传入的样式属性符合组件的预期格式
- 错误的属性类型可能导致组件渲染异常

### 4. 全局变量支持
- 在G2Plot自定义组件中，可以使用全局变量
- 支持`globalVars.get()`和`${GV::variableName}`两种语法

## 调试技巧

### 1. 使用控制台输出
```javascript
// 在逻辑处理节点中
function processData(data) {
    const styleConfig = {
        style: {
            background: data.color
        }
    };

    console.log('更新样式配置:', styleConfig);
    return styleConfig;
}
```

### 2. 分步测试
- 先测试简单的样式更新（如颜色）
- 逐步增加复杂的配置
- 使用浏览器开发者工具检查元素样式

### 3. 错误处理
```javascript
function processData(data) {
    try {
        return {
            style: {
                background: data.color || '#default'
            }
        };
    } catch (error) {
        console.error('样式更新错误:', error);
        return {}; // 返回空对象避免错误
    }
}
```

## 常见问题排查

### 问题1: G2Plot自定义组件样式更新不生效

**症状**: 在蓝图中返回`{style: {xAxis: {}}}`等样式配置，但图表没有更新

**原因**: G2Plot自定义组件需要特殊处理动态样式更新

**解决方案**:
1. **确保自定义函数接收dynamicStyle参数**:
```javascript
function renderG2Plot(container, G2Plot, data, globalVars, dynamicStyle) {
    // 注意第5个参数dynamicStyle
}
```

2. **在函数中合并动态样式**:
```javascript
function renderG2Plot(container, G2Plot, data, globalVars, dynamicStyle) {
    const baseConfig = {
        data: data,
        xField: 'category',
        yField: 'value',
        // 基础配置...
    };

    // 关键：合并动态样式
    const finalConfig = dynamicStyle ?
        Object.assign({}, baseConfig, dynamicStyle) : baseConfig;

    const g2plot = new G2Plot.Column(container, finalConfig);
    g2plot.render();
    return g2plot;
}
```

3. **蓝图逻辑处理节点返回格式**:
```javascript
// 正确的返回格式
return {
    style: {
        xAxis: {
            label: {
                style: {
                    fill: "#ff0000",
                    fontSize: 14
                }
            }
        }
        // 其他样式配置...
    }
};
```

### 问题2: 样式更新后图表闪烁

**原因**: 每次样式更新都重新创建了整个图表

**解决方案**: 使用动态样式更新而不是更新customCode

### 问题3: 复杂样式配置不生效

**原因**: 样式配置格式不正确或层级错误

**解决方案**:
1. 参考G2Plot官方文档确认配置格式
2. 使用浏览器控制台检查配置对象结构
3. 分步测试，从简单配置开始

## 完整示例：动态仪表盘

### 场景描述
创建一个动态仪表盘，根据数据值的变化自动调整颜色和样式。

### 蓝图设计
1. **定时器节点** → 触发数据更新
2. **逻辑处理节点** → 处理数据并生成样式配置
3. **组件节点** → 接收样式更新

### 逻辑处理代码
```javascript
function processData(inputData) {
    // 模拟获取实时数据
    const currentValue = Math.random() * 100;

    // 根据数值范围确定颜色和样式
    let backgroundColor, borderColor, textColor;

    if (currentValue >= 80) {
        // 危险状态 - 红色
        backgroundColor = "#ff4757";
        borderColor = "#ff3838";
        textColor = "#ffffff";
    } else if (currentValue >= 60) {
        // 警告状态 - 橙色
        backgroundColor = "#ffa502";
        borderColor = "#ff9500";
        textColor = "#ffffff";
    } else if (currentValue >= 40) {
        // 正常状态 - 蓝色
        backgroundColor = "#3742fa";
        borderColor = "#2f3542";
        textColor = "#ffffff";
    } else {
        // 良好状态 - 绿色
        backgroundColor = "#2ed573";
        borderColor = "#20bf6b";
        textColor = "#ffffff";
    }

    // 返回样式配置对象
    return {
        style: {
            background: backgroundColor,
            borderColor: borderColor,
            borderWidth: 3,
            borderStyle: "solid",
            borderRadius: 10,
            color: textColor,
            fontSize: 18,
            fontWeight: "bold",
            textAlign: "center",
            padding: "20px"
        },
        data: {
            staticData: `当前值: ${currentValue.toFixed(1)}`
        }
    };
}
```

### G2Plot图表样式更新示例
```javascript
function updateChartStyle(data) {
    const maxValue = Math.max(...data.map(item => item.value));

    // 根据最大值动态调整图表样式
    const colorScheme = maxValue > 100
        ? ["#ff4757", "#ff6b7a", "#ff8a9b"] // 红色系
        : ["#3742fa", "#5352ed", "#40407a"]; // 蓝色系

    return {
        style: {
            color: colorScheme,
            columnStyle: {
                fillOpacity: 0.8,
                stroke: "#ffffff",
                lineWidth: 2
            },
            label: {
                visible: true,
                style: {
                    fill: "#333333",
                    fontSize: 12,
                    fontWeight: "bold"
                }
            },
            xAxis: {
                label: {
                    style: {
                        fill: maxValue > 100 ? "#ff4757" : "#3742fa",
                        fontSize: 10
                    }
                }
            }
        }
    };
}
```

## 最佳实践

1. **渐进式更新**: 一次只更新必要的样式属性
2. **默认值**: 始终为可能为空的值提供默认值
3. **性能考虑**: 避免频繁的样式更新，考虑使用防抖
4. **可维护性**: 将复杂的样式逻辑封装到函数中
5. **测试**: 在不同场景下测试样式更新的效果
6. **全局变量**: 利用全局变量实现主题统一管理
7. **错误处理**: 添加适当的错误处理和回退机制

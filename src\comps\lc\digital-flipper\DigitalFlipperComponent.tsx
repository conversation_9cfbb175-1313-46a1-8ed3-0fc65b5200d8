/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {ForwardedRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {ComponentBaseProps} from "../../common-component/CommonTypes.ts";
import {CountUp} from "countup.js";
import {Odometer} from "odometer_countup";

export interface DigitalFlipperComponentStyle {
    fontSize?: number;
    fontWeight?: number;
    fontFamily?: string;
    color?: string;
    type?: 'slide' | 'caper';
    alignItems?: string;
    justifyContent?: string;
    decimalPlaces?: number;
    prefix?: string;
    suffix?: string;
    separator?: string;
}

export interface DigitalFlipperComponentProps extends ComponentBaseProps {
    style?: DigitalFlipperComponentStyle;
}

export interface DigitalFlipperComponentRef {
    updateConfig: (newConfig: DigitalFlipperComponentProps) => void;
    changeData: (newData: number) => void;
    setEventHandler: (eventMap: Record<string, Function>) => void;
}

const DigitalFlipperComponent = React.forwardRef((props: DigitalFlipperComponentProps,
                                                  ref: ForwardedRef<DigitalFlipperComponentRef>) => {
    const [config, setConfig] = useState<DigitalFlipperComponentProps>({...props});

    const eventHandlerMap = useRef<Record<string, Function>>({});
    const countUpRef = useRef(null);
    const countUpAnimRef = useRef<CountUp | null>(null);

    useEffect(() => {
        const { style } = config;

        countUpAnimRef.current = new CountUp(countUpRef.current!, config.data?.staticData, {
            plugin: style?.type === 'slide' ? new Odometer({duration: 1, lastDigitDelay: 0}) : undefined,
            duration: 1,
            decimalPlaces: style?.decimalPlaces !== undefined ? style.decimalPlaces : 2,
            prefix: style?.prefix || '',
            suffix: style?.suffix || '',
            separator: style?.separator || ',',
        });

        if (!countUpAnimRef.current.error) {
            countUpAnimRef.current.start();
        } else {
            console.error(countUpAnimRef.current.error);
        }
    }, [config]);

    useImperativeHandle(ref, () => ({
        updateConfig: (newConfig) => setConfig({...newConfig}),
        changeData: (newData) => {
            if (countUpAnimRef.current) {
                countUpAnimRef.current.update(newData);
            }
        },
        setEventHandler: (eventMap) => eventHandlerMap.current = eventMap,
    }));

    const onClick = () => {
        if ('click' in eventHandlerMap.current) {
            eventHandlerMap.current['click']();
        }
    }

    const {style} = config;
    return (
        <div ref={countUpRef}
             style={{display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%', ...style}}
             onClick={onClick}/>
    );
});

export default DigitalFlipperComponent;
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, { ForwardedRef, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { ComponentBaseProps } from "../../common-component/CommonTypes.ts";
import './BaseTableComponent.less';
import debounce from "lodash/debounce";
import JSXTransformer from "../../../utils/JSXTransformer.ts";
import { CustomCarousel } from "../../common-component/custom-carousel/CustomCarousel.tsx";


export interface ITableColumn {
    key: string;
    label: string;
    width?: number | null;
    textAlign?: 'left' | 'center' | 'right';
    enableCustomCode?: boolean;  // 是否启用自定义代码
    customCode?: string;         // 自定义代码内容
}

export interface ITableHeaderStyle {
    height?: number;
    background?: string;
    color?: string;
    fontSize?: number;
    fontWeight?: number;
    fontFamily?: string;
}

export interface ITableBodyStyle {
    background?: string;
    color?: string;
    fontSize?: number;
    fontWeight?: number;
    fontFamily?: string;
    enableZebra?: boolean;
    zebraColor?: string;
    oddRowColor?: string;  // 奇数行颜色
    evenRowColor?: string; // 偶数行颜色
    enableCarousel?: boolean;
    carouselSpeed?: number;
    pageSize?: number;
    pauseOnHover?: boolean;    // 鼠标悬停时是否暂停
    carouselMode?: 'continuous' | 'step'; // 轮播模式：连续滚动 | 分步滚动
    slidesToScroll?: number;   // 每次滚动行数（仅在step模式下生效）
}

export interface BaseTableComponentStyle {
    columns?: ITableColumn[];
    data?: object[];
    header?: ITableHeaderStyle;
    body?: ITableBodyStyle;
}

export interface BaseTableComponentProps extends ComponentBaseProps {
    style?: BaseTableComponentStyle;
}

export interface BaseTableComponentRef {
    updateConfig: (newConfig: BaseTableComponentProps) => void;
    setEventHandler: (eventMap: Record<string, (...args: any[]) => void>) => void;
    destroy: () => void;
}

const BaseTableComponent = forwardRef((props: BaseTableComponentProps, ref: ForwardedRef<BaseTableComponentRef>) => {
    const [config, setConfig] = useState<BaseTableComponentProps>({ ...props });

    const tableContainerRef = useRef<HTMLDivElement>(null);
    const eventHandlerMap = useRef<Record<string, (...args: any[]) => void>>({});
    const [bodyHeight, setBodyHeight] = useState(0);

    useImperativeHandle(ref, () => ({
        updateConfig: (newConfig) => setConfig({ ...newConfig }),
        setEventHandler: (eventMap) => (eventHandlerMap.current = eventMap),
        destroy: () => {}
    }));

    const onClick = () => {
        if ('click' in eventHandlerMap.current) {
            eventHandlerMap.current['click']();
        }
    }

    // 合并props更新逻辑
    useEffect(() => {
        setConfig({ ...props });
    }, [props]);

    // 当当配置变化时，更新表体的高度
    useEffect(() => {
        const container = tableContainerRef.current;
        if (!container) return;

        const updateHeight = debounce(() => {
            const headerEl = container.querySelector('.base-table-header-div') as HTMLElement;
            const headerHeight = headerEl ? headerEl.offsetHeight : 0;
            const containerHeight = container.clientHeight;
            setBodyHeight(containerHeight - headerHeight);
        }, 50); // 减少防抖时间，让响应更快

        const resizeObserver = new ResizeObserver(updateHeight);
        resizeObserver.observe(container);
        updateHeight();

        return () => resizeObserver.disconnect();
    }, [config]);

    // 计算Grid布局的列宽
    const gridTemplateColumns = useMemo(() => {
        const { columns = [] } = config.style || {};
        if (columns.length === 0) return 'none';
        const template = columns.map(column => {
            // 如果width是有效的正数，则使用它
            if (column.width && column.width > 0) {
                return `${column.width}px`;
            }
            // 对于0, null, undefined, ''等情况，都使用自动宽度
            return '1fr';
        }).join(' ');

        return template;
    }, [config]);

    // 渲染单元格内容
    const renderCellContent = useCallback((column: ITableColumn, item: Record<string, any>, index: number) => {
        const { data = [] } = config.style || {};
        if (column.enableCustomCode && column.customCode) {
            try {
                const result = JSXTransformer.executeCustomCode(column.customCode, data, item, index);
                return React.isValidElement(result) ? result : String(result);
            } catch (error) {
                console.error('自定义代码执行错误:', error);
                return <span style={{ color: 'red', fontSize: '12px' }}>渲染错误</span>;
            }
        }
        return item[column.key];
    }, [config]);

    // 从配置中提取参数
    const { columns = [], data = [], header = {}, body = {} } = config.style || {};
    const { pageSize = 1, enableCarousel = false, carouselMode = 'step' } = body;
    const shouldEnableCarousel = enableCarousel && data.length > pageSize;

    // 计算行高
    const calculatedRowHeight = useMemo(() => {
        if (bodyHeight > 0 && pageSize > 0) {
            return bodyHeight / pageSize;
        }
        return 40; // 默认行高
    }, [bodyHeight, pageSize]);

    // 渲染每一行
    const tableRows = useMemo(() => data.map((item, index) => (
        <div
            key={index}
            className="base-table-row-div"
            style={{
                height: `${calculatedRowHeight}px`,
                gridTemplateColumns,
                backgroundColor: body.enableZebra
                    ? (index % 2 === 0 ? body.evenRowColor : body.oddRowColor)
                    : body.background,
            }}
        >
            {columns.map((column, i) => (
                <div
                    key={i}
                    className={`base-table-cell-div ${column.enableCustomCode ? 'custom-mode' : `text-align-${column.textAlign || 'left'}`}`}
                    style={{ color: body.color, fontSize: body.fontSize, fontWeight: body.fontWeight }}
                >
                    {renderCellContent(column, item, index)}
                </div>
            ))}
        </div>
    )), [data, columns, body, calculatedRowHeight, gridTemplateColumns, renderCellContent]);



    return (
        <div className="base-table-container" ref={tableContainerRef} onClick={onClick}>
            <div className="base-table-header-div" style={{ ...header, height: header?.height || 40 }}>
                <div className="base-table-row-div" style={{ gridTemplateColumns }}>
                    {columns.map((column, index) => (
                        <div key={index} className={`base-table-cell-div text-align-${column.textAlign || 'left'}`}>
                            {column.label}
                        </div>
                    ))}
                </div>
            </div>
            <div className="base-table-body-div" style={{
                height: bodyHeight > 0 ? `${bodyHeight}px` : 'auto',
                background: body.background,
                color: body.color,
                fontSize: body.fontSize,
                fontWeight: body.fontWeight,
                fontFamily: body.fontFamily
            }}>
                {shouldEnableCarousel ? (
                    <CustomCarousel
                        mode={carouselMode}
                        speed={body.carouselSpeed || 3}
                        slidesToScroll={body.slidesToScroll || 1}
                        pauseOnHover={body.pauseOnHover !== false}
                        rowHeight={calculatedRowHeight}
                    >
                        {tableRows}
                    </CustomCarousel>
                ) : (
                    <div className="base-table-body-content">
                        {tableRows.slice(0, pageSize)}
                    </div>
                )}
            </div>
        </div>
    );
});

export default BaseTableComponent;

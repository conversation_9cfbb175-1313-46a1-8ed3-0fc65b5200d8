/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, { useState, useEffect } from 'react';
import { Tabs, Slider, Button, InputNumber, Popover } from 'antd';
import { ColorPicker as AntdColorPicker } from 'antd';
import { Plus, Delete } from '@icon-park/react';
import './EnhancedColorPicker.less';
import { UIContainer, UIContainerProps } from "../ui-container/UIContainer";

// 颜色类型
export type ColorType = 'single' | 'linear-gradient' | 'radial-gradient';

// 渐变色停止点
export interface ColorStop {
  color: string;
  offset: number; // 0-1之间的值
  opacity?: number; // 透明度，0-1之间的值
}

// 线性渐变
export interface LinearGradient {
  type: 'linear-gradient';
  angle: number; // 角度，0-360
  colorStops: ColorStop[];
}

// 径向渐变
export interface RadialGradient {
  type: 'radial-gradient';
  position: { x: number, y: number }; // 中心点位置，0-1之间的值
  colorStops: ColorStop[];
}

// 统一的颜色值类型
export type ColorValue = string | LinearGradient | RadialGradient;

export interface EnhancedColorPickerProps extends UIContainerProps {
  value?: ColorValue;
  defaultValue?: ColorValue;
  showText?: boolean;
  disabled?: boolean;
  onChange?: (color: ColorValue) => void;
}

// 判断值是否为线性渐变
const isLinearGradient = (value: any): value is LinearGradient => {
  return value && typeof value === 'object' && value.type === 'linear-gradient';
};

// 判断值是否为径向渐变
const isRadialGradient = (value: any): value is RadialGradient => {
  return value && typeof value === 'object' && value.type === 'radial-gradient';
};

// 获取颜色类型
const getColorType = (value: ColorValue): ColorType => {
  if (isLinearGradient(value)) {
    return 'linear-gradient';
  } else if (isRadialGradient(value)) {
    return 'radial-gradient';
  } else {
    return 'single';
  }
};

// 默认线性渐变
const defaultLinearGradient: LinearGradient = {
  type: 'linear-gradient',
  angle: 90,
  colorStops: [
    { color: '#ffffff', offset: 0, opacity: 1 },
    { color: '#1677ff', offset: 1, opacity: 1 }
  ]
};

// 默认径向渐变
const defaultRadialGradient: RadialGradient = {
  type: 'radial-gradient',
  position: { x: 0.5, y: 0.5 },
  colorStops: [
    { color: '#ffffff', offset: 0, opacity: 1 },
    { color: '#1677ff', offset: 1, opacity: 1 }
  ]
};

// 将颜色和透明度合并
const applyOpacityToColor = (color: string, opacity: number = 1): string => {
  if (opacity >= 1) return color;

  if (color.startsWith('#')) {
    // hex转rgba
    const r = parseInt(color.substring(1, 3), 16);
    const g = parseInt(color.substring(3, 5), 16);
    const b = parseInt(color.substring(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  } else if (color.startsWith('rgb(')) {
    // rgb转rgba
    return color.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`);
  } else if (color.startsWith('rgba(')) {
    // 如果已经是rgba，需要合并透明度
    const rgbaMatch = color.match(/rgba\(([^)]+)\)/);
    if (rgbaMatch) {
      const parts = rgbaMatch[1].split(',');
      if (parts.length >= 4) {
        const existingAlpha = parseFloat(parts[3].trim());
        const newAlpha = existingAlpha * opacity;
        parts[3] = ` ${newAlpha}`;
        return `rgba(${parts.join(',')})`;
      }
    }
  }
  return color;
};

// 将渐变对象转换为CSS字符串
const gradientToCss = (gradient: LinearGradient | RadialGradient): string => {
  if (isLinearGradient(gradient)) {
    return `linear-gradient(${gradient.angle}deg, ${gradient.colorStops
      .map(stop => {
        const color = applyOpacityToColor(stop.color, stop.opacity);
        return `${color} ${stop.offset * 100}%`;
      })
      .join(', ')})`;
  } else if (isRadialGradient(gradient)) {
    return `radial-gradient(circle at ${gradient.position.x * 100}% ${gradient.position.y * 100}%, ${gradient.colorStops
      .map(stop => {
        const color = applyOpacityToColor(stop.color, stop.opacity);
        return `${color} ${stop.offset * 100}%`;
      })
      .join(', ')})`;
  }
  return '';
};

export default function EnhancedColorPicker(props: EnhancedColorPickerProps) {
  const { value, defaultValue, showText, disabled, onChange, ...containerProps } = props;

  // 初始化颜色值
  const initialValue = value || defaultValue || '#1677ff';
  const initialType = getColorType(initialValue);

  const [colorType, setColorType] = useState<ColorType>(initialType);
  const [singleColor, setSingleColor] = useState<string>(
    initialType === 'single' ? initialValue as string : '#1677ff'
  );
  const [linearGradient, setLinearGradient] = useState<LinearGradient>(
    initialType === 'linear-gradient' ? initialValue as LinearGradient : defaultLinearGradient
  );
  const [radialGradient, setRadialGradient] = useState<RadialGradient>(
    initialType === 'radial-gradient' ? initialValue as RadialGradient : defaultRadialGradient
  );
  const [popoverVisible, setPopoverVisible] = useState(false);

  // 当外部value变化时更新内部状态
  useEffect(() => {
    if (value) {
      const type = getColorType(value);
      setColorType(type);

      if (type === 'single') {
        setSingleColor(value as string);
      } else if (type === 'linear-gradient') {
        setLinearGradient(value as LinearGradient);
      } else if (type === 'radial-gradient') {
        setRadialGradient(value as RadialGradient);
      }
    }
  }, [value]);

  // 处理单色变化
  const handleSingleColorChange = (color: any) => {
    const hexColor = color.toHexString();
    setSingleColor(hexColor);
    onChange && onChange(hexColor);
  };

  // 处理线性渐变变化
  const handleLinearGradientChange = (newGradient: Partial<LinearGradient>) => {
    const updatedGradient = { ...linearGradient, ...newGradient };
    setLinearGradient(updatedGradient);
    onChange && onChange(updatedGradient);
  };

  // 处理径向渐变变化
  const handleRadialGradientChange = (newGradient: Partial<RadialGradient>) => {
    const updatedGradient = { ...radialGradient, ...newGradient };
    setRadialGradient(updatedGradient);
    onChange && onChange(updatedGradient);
  };

  // 处理颜色停止点变化
  const handleColorStopChange = (index: number, newStop: Partial<ColorStop>, gradientType: 'linear' | 'radial') => {
    if (gradientType === 'linear') {
      const newStops = [...linearGradient.colorStops];
      newStops[index] = { ...newStops[index], ...newStop };
      handleLinearGradientChange({ colorStops: newStops });
    } else {
      const newStops = [...radialGradient.colorStops];
      newStops[index] = { ...newStops[index], ...newStop };
      handleRadialGradientChange({ colorStops: newStops });
    }
  };

  // 添加颜色停止点
  const addColorStop = (gradientType: 'linear' | 'radial') => {
    const stops = gradientType === 'linear' ? linearGradient.colorStops : radialGradient.colorStops;
    const newStop: ColorStop = {
      color: '#1677ff',
      offset: stops.length > 0 ? Math.min(stops[stops.length - 1].offset + 0.1, 1) : 0.5,
      opacity: 1
    };

    if (gradientType === 'linear') {
      handleLinearGradientChange({ colorStops: [...stops, newStop] });
    } else {
      handleRadialGradientChange({ colorStops: [...stops, newStop] });
    }
  };

  // 删除颜色停止点
  const removeColorStop = (index: number, gradientType: 'linear' | 'radial') => {
    const stops = gradientType === 'linear' ? linearGradient.colorStops : radialGradient.colorStops;
    if (stops.length <= 2) return; // 至少保留两个停止点

    const newStops = stops.filter((_, i) => i !== index);
    if (gradientType === 'linear') {
      handleLinearGradientChange({ colorStops: newStops });
    } else {
      handleRadialGradientChange({ colorStops: newStops });
    }
  };

  // 标签页切换
  const handleTabChange = (key: string) => {
    setColorType(key as ColorType);

    // 根据选择的类型返回相应的值
    if (key === 'single') {
      onChange && onChange(singleColor);
    } else if (key === 'linear-gradient') {
      onChange && onChange(linearGradient);
    } else if (key === 'radial-gradient') {
      onChange && onChange(radialGradient);
    }
  };

  // 渲染颜色停止点
  const renderColorStops = (stops: ColorStop[], gradientType: 'linear' | 'radial') => {
    return stops.map((stop, index) => (
      <div key={index} className="color-stop-item">
        <div className="color-stop-controls">
          <AntdColorPicker
            size="small"
            value={stop.color}
            onChange={(color) => handleColorStopChange(index, { color: color.toHexString() }, gradientType)}
          />
          <div className="slider-group">
            <div className="slider-item">
              <span>位置:</span>
              <Slider
                className="offset-slider"
                min={0}
                max={1}
                step={0.01}
                value={stop.offset}
                onChange={(value) => handleColorStopChange(index, { offset: value }, gradientType)}
              />
            </div>
            <div className="slider-item">
              <span>透明度:</span>
              <Slider
                className="opacity-slider"
                min={0}
                max={1}
                step={0.01}
                value={stop.opacity !== undefined ? stop.opacity : 1}
                onChange={(value) => handleColorStopChange(index, { opacity: value }, gradientType)}
              />
            </div>
          </div>
          <Button
            type="text"
            size="small"
            icon={<Delete />}
            onClick={() => removeColorStop(index, gradientType)}
            disabled={stops.length <= 2}
          />
        </div>
      </div>
    ));
  };

  // 定义 Tabs 的 items
  const tabItems = [
    {
      key: 'single',
      label: '单色',
      children: (
        <AntdColorPicker
          size="small"
          format="hex"
          value={singleColor}
          disabled={disabled}
          showText={showText}
          onChange={handleSingleColorChange}
        />
      )
    },
    {
      key: 'linear-gradient',
      label: '线性渐变',
      children: (
        <>
          <div className="gradient-preview" style={{ background: gradientToCss(linearGradient) }} />

          <div className="gradient-controls">
            <div className="angle-control">
              <span>角度:</span>
              <InputNumber
                min={0}
                max={360}
                value={linearGradient.angle}
                onChange={(value) => handleLinearGradientChange({ angle: value || 0 })}
              />
            </div>

            <div className="color-stops">
              {renderColorStops(linearGradient.colorStops, 'linear')}

              <Button
                type="dashed"
                icon={<Plus />}
                onClick={() => addColorStop('linear')}
                disabled={linearGradient.colorStops.length >= 5}
              >
                添加颜色
              </Button>
            </div>
          </div>
        </>
      )
    },
    {
      key: 'radial-gradient',
      label: '径向渐变',
      children: (
        <>
          <div className="gradient-preview" style={{ background: gradientToCss(radialGradient) }} />

          <div className="gradient-controls">
            <div className="position-control">
              <div>
                <span>X位置:</span>
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  value={radialGradient.position.x}
                  onChange={(value) => handleRadialGradientChange({ position: { ...radialGradient.position, x: value } })}
                />
              </div>
              <div>
                <span>Y位置:</span>
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  value={radialGradient.position.y}
                  onChange={(value) => handleRadialGradientChange({ position: { ...radialGradient.position, y: value } })}
                />
              </div>
            </div>

            <div className="color-stops">
              {renderColorStops(radialGradient.colorStops, 'radial')}

              <Button
                type="dashed"
                icon={<Plus />}
                onClick={() => addColorStop('radial')}
                disabled={radialGradient.colorStops.length >= 5}
              >
                添加颜色
              </Button>
            </div>
          </div>
        </>
      )
    }
  ];

  // 渲染颜色预览
  const renderColorPreview = () => {
    const style: React.CSSProperties = {};

    if (colorType === 'single') {
      style.backgroundColor = singleColor;
    } else if (colorType === 'linear-gradient') {
      style.background = gradientToCss(linearGradient);
    } else if (colorType === 'radial-gradient') {
      style.background = gradientToCss(radialGradient);
    }

    return (
      <div
        className="color-preview-box"
        style={style}
        onClick={() => !disabled && setPopoverVisible(true)}
      />
    );
  };

  // 颜色选择面板内容
  const colorPickerContent = (
    <div className="color-picker-panel">
      <Tabs activeKey={colorType} onChange={handleTabChange} items={tabItems} />
    </div>
  );

  return (
    <UIContainer {...containerProps} className="enhanced-color-picker">
      <Popover
        content={colorPickerContent}
        trigger="click"
        open={popoverVisible}
        onOpenChange={setPopoverVisible}
        placement="bottomLeft"
        overlayClassName="enhanced-color-picker-popover"
      >
        {renderColorPreview()}
      </Popover>
      {showText && (
        <div className="color-text">
          {colorType === 'single' ? singleColor : colorType === 'linear-gradient' ? '线性渐变' : '径向渐变'}
        </div>
      )}
    </UIContainer>
  );
}

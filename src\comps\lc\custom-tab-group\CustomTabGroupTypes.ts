/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {ComponentBaseProps} from "../../common-component/CommonTypes";

// Tab组中每个Tab的配置项
export interface TabConfigItem {
    id: string; // 唯一标识，用于关联
    name: string; // 显示名称
    value: any;   // 蓝图交互的值
    childTabId: string; // 关联的画布上子Tab组件的ID
}

// Tab组的样式/核心配置
export interface CustomTabGroupStyle {
    tabs: TabConfigItem[];
    activeTabValue: any; // 当前激活的Tab的值
}

// Tab组组件的完整Props
export interface CustomTabGroupComponentProps extends ComponentBaseProps {
    style: CustomTabGroupStyle;
}

// 背景图片样式，可复用或参考BaseImageComponentStyle
export interface BackgroundImageStyle {
    type?: 'online' | 'local';
    onLineUrl?: string;
    localUrl?: string;
    hash?: string;
}

// 子Tab单个状态的样式
export interface ChildTabStateStyle {
    background?: string;
    backgroundImage?: BackgroundImageStyle;
    color?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    fontSize?: number;
    fontWeight?: string | number;
    padding?: string;
    textAlign?: 'left' | 'center' | 'right';
}

// 子Tab的核心配置
export interface ChildTabStyle {
    parentId: string; // 指向父级TabGroup的ID
    value: any; // 自己的值，用于匹配激活状态
    isActive: boolean; // 自身是否激活的状态（由父级控制）
    activeStyle: ChildTabStateStyle;
    inactiveStyle: ChildTabStateStyle;
}

// 子Tab组件的完整Props
export interface ChildTabComponentProps extends ComponentBaseProps {
    style: ChildTabStyle;
}

/*
 * @Author: <EMAIL>
 * @Date: 2024-05-26 10:00:00
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-05-26 10:00:00
 * @Description: 全局变量蓝图节点控制器
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {AbstractBPNodeController, AnchorPointType, ExecuteInfoType, NodeInfoType} from "../../AbstractBPNodeController";
import {UpdateOptions} from "../../../../../../framework/core/AbstractController";
import ComponentUtil from "../../../../../../utils/ComponentUtil";
import BPNode, {NodeProps} from "../../../BPNode";
import React from "react";
import ObjectUtil from "../../../../../../utils/ObjectUtil";
import BPExecutor from "../../../../core/BPExecutor";
import globalVariableManager from "../../../../../manager/GlobalVariableManager.ts";
import {GlobalVariableNodeConfig} from "./GlobalVariableNodeConfig";

export interface GlobalVariableNodeConfigType extends NodeProps {
    globalVariableId?: string;
}

export default class BPGlobalVariableNodeController extends AbstractBPNodeController<GlobalVariableNodeConfigType> {

    async create(container: HTMLElement, config: GlobalVariableNodeConfigType): Promise<void> {
        this.config = config;
        this.container = container;
        this.instance = await ComponentUtil.createAndRender(container, BPNode, config);
    }

    execute(executeInfo: ExecuteInfoType, executor: BPExecutor, params: any): void {
        const {nodeId, apId, anchorType} = executeInfo;

        // 处理输出锚点的请求
        if (anchorType === AnchorPointType.OUTPUT) {
            if (apId === 'currentValue') {
                // 处理"当前值"锚点请求，返回变量的当前值
                const currentValue = globalVariableManager.getVariableValue(nodeId);
                // 继续传递当前值到连接的节点
                const nextAnchorId = `${nodeId}:currentValue:${AnchorPointType.OUTPUT}`;
                executor.execute(nextAnchorId, executor, currentValue);
            }
            return;
        }

        // 处理输入锚点 - 直接设置值，不需要执行流控制
        if (apId === 'setValue') {
            // 调用全局变量管理器设置变量值
            globalVariableManager.setVariableValue(nodeId, params, true);
        }
    }

    getConfig(): GlobalVariableNodeConfigType | null {
        return this.config;
    }

    update(config: GlobalVariableNodeConfigType, upOp?: UpdateOptions | undefined): void {
        this.config = ObjectUtil.merge(this.config, config);
    }

    getNodeInfo(nodeId: string): NodeInfoType | null {
        // 获取全局变量定义
        const variableDefinition = globalVariableManager.getVariableDefinition(nodeId);
        if (!variableDefinition) {
            return null;
        }

        return {
            id: nodeId,
            name: variableDefinition.name,
            titleBgColor: "#6b46c1", // 紫色主题
            type: "global-variable-node",
            icon: "FunctionOutlined",
            input: [
                {
                    id: nodeId + ':setValue:' + AnchorPointType.INPUT,
                    name: "设置值",
                    type: AnchorPointType.INPUT
                }
            ],
            output: [
                {
                    id: nodeId + ":onValueChange:" + AnchorPointType.OUTPUT,
                    name: "值变化时",
                    type: AnchorPointType.OUTPUT
                },
                {
                    id: nodeId + ':currentValue:' + AnchorPointType.OUTPUT,
                    name: "当前值",
                    type: AnchorPointType.OUTPUT
                }
            ]
        };
    }

    public getConfigComponent(): React.ComponentType | null {
        return GlobalVariableNodeConfig;
    }
}

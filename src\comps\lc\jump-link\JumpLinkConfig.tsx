/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React from 'react';
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI";
import {Control} from "../../../json-schema/SchemaTypes";
import {JumpLinkController} from "./JumpLinkController";
import {ConfigType} from "../../../designer/right/ConfigContent";
import {
    AlignBottomTwo,
    AlignHorizontalCenterTwo,
    AlignLeftTwo,
    AlignRightTwo,
    AlignTopTwo,
    AlignVerticalCenterTwo
} from "@icon-park/react";

export const JumpLinkStyleConfig: React.FC<ConfigType<JumpLinkController>> = ({controller}) => {

    const {data, style} = controller.getConfig()!;

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {dataFragment, id, data: fieldData} = fieldChangeData;
        
        if (id === 'linkText') {
            controller.update({
                data: {
                    ...data,
                    staticData: {
                        ...data?.staticData,
                        text: fieldData as string
                    }
                }
            });
        } else if (id === 'linkUrl') {
            controller.update({
                data: {
                    ...data,
                    staticData: {
                        ...data?.staticData,
                        url: fieldData as string
                    }
                }
            });
        } else if (id === 'linkTarget') {
            controller.update({
                data: {
                    ...data,
                    staticData: {
                        ...data?.staticData,
                        target: fieldData as '_blank' | '_self'
                    }
                }
            });
        } else {
            controller.update(dataFragment!);
        }
    }

    const schema: Control = {
        type: 'grid',
        children: [
            {
                key: 'data',
                children: [
                    {
                        id: 'linkText',
                        key: 'text',
                        type: 'input',
                        label: '链接文字',
                        value: data?.staticData?.text,
                    },
                    {
                        id: 'linkUrl',
                        key: 'url',
                        type: 'text-area',
                        label: 'URL地址',
                        value: data?.staticData?.url,
                    },
                    {
                        id: 'linkTarget',
                        key: 'target',
                        type: 'select',
                        label: '打开方式',
                        value: data?.staticData?.target || '_blank',
                        config: {
                            options: [
                                {label: '新窗口', value: '_blank'},
                                {label: '当前窗口', value: '_self'}
                            ]
                        }
                    },
                ]
            },
            {
                key: 'style',
                children: [
                    {
                        key: 'fontSize',
                        type: 'number-input',
                        label: '字号',
                        value: style?.fontSize,
                        config: {min: 1}
                    },
                    {
                        key: 'fontWeight',
                        type: 'number-input',
                        label: '加粗',
                        value: style?.fontWeight || 400,
                        config: {
                            min: 100,
                            max: 900,
                            step: 100
                        }
                    },
                    {
                        key: 'color',
                        type: 'color-picker',
                        label: '颜色',
                        value: style?.color,
                        config: {
                            showText: true,
                        }
                    },
                    {
                        key: 'fontFamily',
                        type: 'select',
                        label: '字体',
                        value: style?.fontFamily,
                        config: {
                            options: [
                                {label: '钉钉进步体', value: 'DingTalk JinBuTi'},
                                {label: '抖音美好体', value: 'DouyinSansBold'},
                                {label: '优设标题黑', value: '优设标题黑'},
                                {label: '庞门正道标题', value: '庞门正道标题体免费版'},
                            ],
                        }
                    },
                    {
                        key: 'textDecoration',
                        type: 'select',
                        label: '文字装饰',
                        value: style?.textDecoration || 'underline',
                        config: {
                            options: [
                                {label: '下划线', value: 'underline'},
                                {label: '无装饰', value: 'none'},
                                {label: '删除线', value: 'line-through'},
                            ]
                        }
                    },
                    {
                        key: 'letterSpacing',
                        type: 'number-input',
                        label: '字距',
                        value: style?.letterSpacing,
                        config: {
                            min: 0,
                            max: 10,
                            step: 1
                        }
                    },
                    {
                        key: 'lineHeight',
                        type: 'number-input',
                        label: '行距',
                        value: style?.lineHeight,
                        config: {
                            min: 0,
                            max: 10,
                            step: 0.1
                        }
                    },
                    {
                        label: "水平对齐",
                        type: 'group-button',
                        key: 'justifyContent',
                        value: style?.justifyContent,
                        config: {
                            items: [
                                {
                                    value: 'flex-start',
                                    content: <AlignLeftTwo theme="filled" size="16"
                                                           strokeWidth={2}
                                                           strokeLinecap="square"/>
                                },
                                {
                                    value: 'center',
                                    content: <AlignHorizontalCenterTwo theme="filled" size="16"
                                                                       strokeWidth={2} strokeLinecap="square"/>
                                },
                                {
                                    value: 'flex-end',
                                    content: <AlignRightTwo theme="filled" size="16"
                                                            strokeWidth={2}
                                                            strokeLinecap="square"/>
                                }
                            ]
                        }
                    },
                    {
                        label: '垂直对齐',
                        type: 'group-button',
                        key: 'alignItems',
                        value: style?.alignItems,
                        config: {
                            items: [
                                {
                                    value: 'flex-start',
                                    content: <AlignTopTwo theme="filled" size="16"
                                                          strokeWidth={2}
                                                          strokeLinecap="square"/>
                                },
                                {
                                    value: 'center',
                                    content: <AlignVerticalCenterTwo theme="filled" size="16"
                                                                     strokeWidth={2} strokeLinecap="square"/>
                                },
                                {
                                    value: 'flex-end',
                                    content: <AlignBottomTwo theme="filled" size="16"
                                                             strokeWidth={2}
                                                             strokeLinecap="square"/>
                                }
                            ]
                        }
                    },
                ]
            },
        ]
    }

    return (
        <div style={{padding: 10}}><LCGUI schema={schema} onFieldChange={onFieldChange}/></div>
    )
}

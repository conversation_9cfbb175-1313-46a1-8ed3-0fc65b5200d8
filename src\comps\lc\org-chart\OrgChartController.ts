/**
 * 组织架构图组件控制器
 */

import {ThemeItemType, DesignerMode} from "../../../designer/DesignerType";
import {UpdateOptions} from "../../../framework/core/AbstractController";
import AbstractDesignerController from "../../../framework/core/AbstractDesignerController";
import ComponentUtil from "../../../utils/ComponentUtil";
import ObjectUtil from "../../../utils/ObjectUtil";
import BPExecutor from "../../../designer/blueprint/core/BPExecutor";
import URLUtil from "../../../utils/URLUtil";
import OrgChartComponent, {
    OrgChartComponentProps,
    OrgChartComponentRef
} from "./OrgChartComponent";

export class OrgChartController extends AbstractDesignerController<OrgChartComponentRef, OrgChartComponentProps> {

    async create(container: HTMLElement, config: OrgChartComponentProps): Promise<void> {
        this.config = config;
        this.container = container;
        this.instance = await ComponentUtil.createAndRender<OrgChartComponentRef>(container, OrgChartComponent, config);

        // 应用容器样式
        this.updateContainerStyle();
    }

    destroy(): void {
        this.instance = null;
        this.config = null;
    }

    getConfig(): OrgChartComponentProps | null {
        // 如果组件实例存在且有getCurrentConfig方法，优先使用组件的当前配置
        // 这样可以获取到包含viewport和nodePositions的最新配置
        if (this.instance && typeof this.instance.getCurrentConfig === 'function') {
            const currentConfig = this.instance.getCurrentConfig();
            // 同步到控制器的config中
            this.config = currentConfig;
            return currentConfig;
        }
        return this.config;
    }

    changeData(data: any[]) {
        this.config!.data!.staticData = data;
        this.instance?.updateData(data);
    }

    update(config: OrgChartComponentProps, upOp?: UpdateOptions | undefined): void {
        this.config = ObjectUtil.merge(this.config, config);
        upOp = upOp || {reRender: true};
        if (upOp.reRender) {
            this.instance?.updateConfig(this.config!);

            // 如果更新了panOnDrag配置，需要重新应用容器样式
            if (config.style?.panOnDrag !== undefined) {
                this.updateContainerStyle();
            }
        }
    }

    private updateContainerStyle(): void {
        const {mode} = URLUtil.parseUrlParams();
        if (mode === DesignerMode.EDIT && this.container) {
            if (this.config?.style?.panOnDrag !== false) {
                // 启用拖拽平移：需要特殊处理，既要支持ReactFlow拖拽，又要支持双击配置
                this.container.style.pointerEvents = "auto";

                // 添加事件监听器来处理双击事件
                this.container.addEventListener('dblclick', this.handleDoubleClick.bind(this));

                const reactFlowContainer = this.container.querySelector('.react-flow');
                if (reactFlowContainer) {
                    (reactFlowContainer as HTMLElement).style.pointerEvents = "auto";
                    // 阻止ReactFlow容器的双击事件冒泡，让组件的双击事件能够触发
                    reactFlowContainer.addEventListener('dblclick', (e) => {
                        e.stopPropagation();
                    });
                }
            } else {
                // 禁用拖拽平移：恢复组件容器拖拽，移除双击监听器
                this.container.style.pointerEvents = "auto";
                this.container.removeEventListener('dblclick', this.handleDoubleClick.bind(this));
            }
        }
    }

    private handleDoubleClick(event: MouseEvent): void {
        // 检查双击是否发生在ReactFlow容器外部
        const reactFlowContainer = this.container?.querySelector('.react-flow');
        if (reactFlowContainer && !reactFlowContainer.contains(event.target as Node)) {
            // 双击发生在组件边框区域，触发配置面板
            event.stopPropagation();
            // 这里可以触发配置面板打开的逻辑
            // 由于我们无法直接访问设计器的配置面板，这个事件会自然冒泡到设计器
        }
    }

    updateTheme(newTheme: ThemeItemType): void {
        // 主题更新逻辑
    }

    registerEvent() {
        const nodeId = this.config?.base?.id!;
        this.instance?.setEventHandler({
            nodeClick: (nodeData: any) => BPExecutor.triggerComponentEvent(nodeId!, "nodeClick", nodeData),
            nodeDoubleClick: (nodeData: any) => BPExecutor.triggerComponentEvent(nodeId!, "nodeDoubleClick", nodeData),
            nodeHover: (nodeData: any) => BPExecutor.triggerComponentEvent(nodeId!, "nodeHover", nodeData),
        })
    }
}

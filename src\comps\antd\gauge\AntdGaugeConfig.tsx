/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {ConfigType} from "../../../designer/right/ConfigContent";
import {Control} from "../../../json-schema/SchemaTypes.ts";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI.tsx";
import AntdGaugeController from "./AntdGaugeController.ts";
import LCGUIUtil from "../../../json-schema/LCGUIUtil.ts";
import {Indicator} from "@antv/g2plot/lib/plots/gauge/types";
import {CSSProperties} from "react";
import {ColorAttr, StatisticText} from "@antv/g2plot";
import ColorUtil from "../../../utils/ColorUtil.ts";

export default function AntdGaugeConfig(props: ConfigType<AntdGaugeController>) {
    const {controller} = props;
    const config = controller.getConfig()?.style;

    const schema: Control =
        {
            key: 'style',
            children: [
                {
                    type: 'accordion',
                    label: '图形',
                    children: [
                        {
                            type: 'sub-accordion',
                            label: '基础',
                            children: [
                                {
                                    type: 'grid',
                                    children: [
                                        {
                                            key: 'radius',
                                            type: 'number-input',
                                            label: '外半径',
                                            value: config?.radius,
                                            config: {
                                                min: 0,
                                                max: 1,
                                                step: 0.01
                                            },
                                        },
                                        {
                                            id: 'startAngle',
                                            key: 'startAngle',
                                            type: 'number-input',
                                            label: '起始角度',
                                            value: config!.startAngle! / Math.PI * 180,
                                        },
                                        {
                                            key: 'innerRadius',
                                            type: 'number-input',
                                            label: '内半径',
                                            value: config?.innerRadius,
                                            config: {
                                                min: 0,
                                                max: 1,
                                                step: 0.01
                                            },
                                        },
                                        {
                                            id: 'endAngle',
                                            key: 'endAngle',
                                            type: 'number-input',
                                            label: '结束角度',
                                            value: config!.endAngle! / Math.PI * 180,
                                        },
                                        {
                                            key: 'range',
                                            children: [
                                                {
                                                    id: 'rangeColor',
                                                    key: 'color',
                                                    type: 'enhanced-color-mode',
                                                    label: '颜色',
                                                    value: config?.range?.color,
                                                },
                                            ]
                                        },
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'accordion',
                    label: '指针',
                    key: 'indicator',
                    children: [
                        {
                            label: '基础',
                            key: 'pointer',
                            type: 'sub-accordion',
                            children: [
                                {
                                    type: 'grid',
                                    key: 'style',
                                    children: [
                                        {
                                            id: 'pointerStroke',
                                            key: 'stroke',
                                            type: 'enhanced-color-picker',
                                            label: '颜色',
                                            value: (config?.indicator as Indicator)?.pointer!.style?.stroke,
                                            config: {
                                                showText: true,
                                            }
                                        },
                                        {
                                            key: 'lineWidth',
                                            type: 'number-input',
                                            label: '线宽',
                                            value: (config?.indicator as Indicator)?.pointer!.style?.lineWidth,
                                            config: {
                                                min: 0,
                                                max: 10,
                                                step: 1
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            type: 'sub-accordion',
                            label: '指针原点',
                            children: [
                                {
                                    key: 'pin',
                                    type: 'grid',
                                    children: [
                                        {
                                            key: 'style',
                                            children: [
                                                {
                                                    id: 'pinStroke',
                                                    key: 'stroke',
                                                    type: 'enhanced-color-picker',
                                                    label: '描边',
                                                    value: (config?.indicator as Indicator)?.pin!.style?.stroke,
                                                    config: {
                                                        showText: true,
                                                    }
                                                },
                                                {
                                                    id: 'pinFill',
                                                    key: 'fill',
                                                    type: 'enhanced-color-picker',
                                                    label: '填充',
                                                    value: (config?.indicator as Indicator)?.pin!.style?.fill,
                                                    config: {
                                                        showText: true,
                                                    }
                                                },
                                                {
                                                    key: 'r',
                                                    type: 'number-input',
                                                    label: '半径',
                                                    value: (config?.indicator as Indicator)?.pin!.style?.r,
                                                    config: {
                                                        min: 0,
                                                    }
                                                },
                                                {
                                                    key: 'lineWidth',
                                                    type: 'number-input',
                                                    label: '描边宽',
                                                    value: (config?.indicator as Indicator)?.pin!.style?.lineWidth,
                                                    config: {
                                                        min: 0,
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                },
                            ]
                        },
                    ]
                },
                {
                    type: 'accordion',
                    label: '刻度',
                    children: [
                        {
                            type: 'sub-accordion',
                            label: '主刻度',
                            key: 'axis',
                            children: [
                                {
                                    key: 'tickLine',
                                    type: 'grid',
                                    children: [
                                        {
                                            key: 'style',
                                            children: [
                                                {
                                                    id: 'tickLineStroke',
                                                    key: 'stroke',
                                                    type: 'enhanced-color-picker',
                                                    label: '颜色',
                                                    value: (config?.axis as any)?.tickLine?.style?.stroke,
                                                    config: {
                                                        showText: true,
                                                    }
                                                },
                                                {
                                                    key: 'lineWidth',
                                                    type: 'number-input',
                                                    label: '宽度',
                                                    value: (config?.axis as any)?.tickLine?.style?.lineWidth,
                                                    config: {
                                                        min: 0,
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            key: 'length',
                                            type: 'number-input',
                                            label: '长度',
                                            value: (config?.axis as any)?.tickLine?.length,
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            type: 'sub-accordion',
                            label: '子刻度',
                            key: 'axis',
                            children: [
                                {
                                    key: 'subTickLine',
                                    type: 'grid',
                                    children: [
                                        {
                                            key: 'style',
                                            children: [
                                                {
                                                    id: 'subTickLineStroke',
                                                    key: 'stroke',
                                                    type: 'enhanced-color-picker',
                                                    label: '颜色',
                                                    value: (config?.axis as any)?.subTickLine?.style?.stroke,
                                                    config: {
                                                        showText: true,
                                                    }
                                                },
                                                {
                                                    key: 'lineWidth',
                                                    type: 'number-input',
                                                    label: '宽度',
                                                    value: (config?.axis as any)?.subTickLine?.style?.lineWidth,
                                                    config: {
                                                        min: 0,
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            key: 'length',
                                            type: 'number-input',
                                            label: '长度',
                                            value: (config?.axis as any)?.subTickLine?.length,
                                        },
                                        {
                                            key: 'count',
                                            type: 'number-input',
                                            label: '数量',
                                            value: (config?.axis as any)?.subTickLine?.count,
                                            config: {
                                                min: 0,
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                    ]
                },
                {
                    type: 'accordion',
                    label: '文本',
                    key: 'statistic',
                    children: [
                        {
                            key: 'content',
                            type: 'grid',
                            children: [
                                {
                                    key: 'style',
                                    children: [
                                        {
                                            id: 'contentColor',
                                            key: 'color',
                                            type: 'enhanced-color-picker',
                                            label: '颜色',
                                            value: ((config?.statistic?.content as StatisticText)?.style as CSSProperties)?.color,
                                            config: {
                                                showText: true,
                                            }
                                        },
                                        {
                                            key: 'fontSize',
                                            type: 'number-input',
                                            label: '字号',
                                            value: parseInt(((config?.statistic?.content as StatisticText)?.style as CSSProperties)?.fontSize as string),
                                            config: {
                                                min: 0,
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },

            ]
        }

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataKeyPath} = fieldChangeData;
        let {dataFragment} = fieldChangeData;

        if (id === 'startAngle' || id === 'endAngle') {
            dataFragment = LCGUIUtil.createObjectFromArray(dataKeyPath, Number(data!) / 180 * Math.PI);
        }
        // 处理刻度范围颜色
        else if (id === 'rangeColor') {
            if (data && Array.isArray(data)) {
                // 多色模式
                // 检查数组中是否有渐变对象
                const hasGradient = data.some(color =>
                    typeof color === 'object' && color !== null && 'type' in color &&
                    (color.type === 'linear-gradient' || color.type === 'radial-gradient')
                );

                if (hasGradient) {
                    // 如果数组中有渐变对象，我们需要将它们转换为渐变字符串
                    const processedColors = data.map(color => {
                        if (typeof color === 'object' && color !== null && 'type' in color &&
                            (color.type === 'linear-gradient' || color.type === 'radial-gradient')) {
                            return ColorUtil.gradientToCss(color);
                        }
                        return color;
                    });
                    controller.update({style: {range: {color: processedColors as any}}});
                } else {
                    // 如果数组中没有渐变对象，直接使用原始数组
                    controller.update({style: {range: {color: data as any}}});
                }
            } else if (data && typeof data === 'object' && data !== null && 'type' in data &&
                      (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {range: {color: gradientCss as any}}});
            } else {
                // 处理普通颜色
                controller.update({style: {range: {color: data as string}}});
            }
        }
        // 处理指针颜色
        else if (id === 'pointerStroke') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                const indicator = {...config?.indicator as Indicator};
                if (!indicator.pointer) indicator.pointer = {};
                const pointerStyle = {...(indicator.pointer.style || {})};
                pointerStyle.stroke = gradientCss;
                indicator.pointer = {...indicator.pointer, style: pointerStyle};
                controller.update({style: {indicator}});
            } else {
                // 处理普通颜色
                controller.update(dataFragment);
            }
        }
        // 处理指针原点描边颜色
        else if (id === 'pinStroke') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                const indicator = {...config?.indicator as Indicator};
                if (!indicator.pin) indicator.pin = {};
                const pinStyle = {...(indicator.pin.style || {})};
                pinStyle.stroke = gradientCss;
                indicator.pin = {...indicator.pin, style: pinStyle};
                controller.update({style: {indicator}});
            } else {
                // 处理普通颜色
                controller.update(dataFragment);
            }
        }
        // 处理指针原点填充颜色
        else if (id === 'pinFill') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                const indicator = {...config?.indicator as Indicator};
                if (!indicator.pin) indicator.pin = {};
                const pinStyle = {...(indicator.pin.style || {})};
                pinStyle.fill = gradientCss;
                indicator.pin = {...indicator.pin, style: pinStyle};
                controller.update({style: {indicator}});
            } else {
                // 处理普通颜色
                controller.update(dataFragment);
            }
        }
        // 处理主刻度颜色
        else if (id === 'tickLineStroke') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                const axis = {...config?.axis as any};
                if (!axis.tickLine) axis.tickLine = {};
                controller.update({
                    style: {
                        axis: {
                            ...axis,
                            tickLine: {
                                ...axis.tickLine,
                                style: {
                                    ...(axis.tickLine.style || {}),
                                    stroke: gradientCss
                                }
                            }
                        }
                    }
                });
            } else {
                // 处理普通颜色
                controller.update(dataFragment);
            }
        }
        // 处理子刻度颜色
        else if (id === 'subTickLineStroke') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                const axis = {...config?.axis as any};
                if (!axis.subTickLine) axis.subTickLine = {};
                controller.update({
                    style: {
                        axis: {
                            ...axis,
                            subTickLine: {
                                ...axis.subTickLine,
                                style: {
                                    ...(axis.subTickLine.style || {}),
                                    stroke: gradientCss
                                }
                            }
                        }
                    }
                });
            } else {
                // 处理普通颜色
                controller.update(dataFragment);
            }
        }
        // 处理文本颜色
        else if (id === 'contentColor') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                const statistic = {...config?.statistic};
                if (!statistic.content) statistic.content = {};
                controller.update({
                    style: {
                        statistic: {
                            ...statistic,
                            content: {
                                ...(statistic.content as StatisticText),
                                style: {
                                    ...((statistic.content as StatisticText).style || {}),
                                    color: gradientCss
                                }
                            }
                        }
                    }
                });
            } else {
                // 处理普通颜色
                controller.update(dataFragment);
            }
        } else {
            controller.update(dataFragment);
        }
    }

    return <LCGUI schema={schema} onFieldChange={onFieldChange}/>;
}
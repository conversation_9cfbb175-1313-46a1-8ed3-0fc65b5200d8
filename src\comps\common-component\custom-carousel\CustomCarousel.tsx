import React, { useEffect, useRef, useState, ReactNode } from 'react';
import './CustomCarousel.less';

export interface CustomCarouselProps {
    children: ReactNode[];
    mode: 'continuous' | 'step';
    speed: number;
    slidesToScroll: number;
    pauseOnHover: boolean;
    rowHeight: number;
}

export const CustomCarousel: React.FC<CustomCarouselProps> = ({
    children,
    mode,
    speed,
    slidesToScroll,
    pauseOnHover,
    rowHeight
}) => {
    const contentRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const [isPaused, setIsPaused] = useState(false);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);
    const isAnimatingRef = useRef(false); // 用于防止在动画期间重复触发
    
    // 使用 ref 来存储和更新当前滚动的偏移量，以避免 setInterval 的闭包问题
    const positionRef = useRef(0);

    // 清理定时器和事件监听器
    const cleanup = () => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
        if (contentRef.current) {
            contentRef.current.removeEventListener('transitionend', handleTransitionEnd);
        }
    };
    
    // 关键修复：当动画结束后执行的无缝重置逻辑
    const handleTransitionEnd = () => {
        const contentElement = contentRef.current;
        const totalItems = children.length;
        if (!contentElement || totalItems === 0) return;

        // 当滚动距离超过一轮内容的高度时，执行重置
        if (Math.abs(positionRef.current) >= totalItems * rowHeight) {
            // 1. 计算出等效的滚动位置
            positionRef.current %= (totalItems * rowHeight);
            
            // 2. 立即、无动画地跳回等效的初始位置
            contentElement.style.transition = 'none'; // 关闭动画
            contentElement.style.transform = `translateY(${positionRef.current}px)`; // 瞬移
        }
        
        isAnimatingRef.current = false; // 动画结束，允许下一次触发
    };

    // 主要的动画逻辑
    useEffect(() => {
        const contentElement = contentRef.current;
        const totalItems = children.length;
        if (!contentElement || totalItems === 0 || rowHeight <= 0) return;
        
        cleanup(); // 先清理旧的定时器和监听器

        if (mode === 'step') {
            // 为步进模式添加 transitionend 监听器
            contentElement.addEventListener('transitionend', handleTransitionEnd);
            
            const stepIntervalCallback = () => {
                if (isPaused || isAnimatingRef.current) return;
                
                isAnimatingRef.current = true; // 动画开始
                
                // 恢复动画（如果之前被禁用了）
                contentElement.style.transition = 'transform 0.5s ease-in-out';
                
                // 计算并应用下一次的滚动位置
                positionRef.current -= slidesToScroll * rowHeight;
                contentElement.style.transform = `translateY(${positionRef.current}px)`;
            };

            intervalRef.current = setInterval(stepIntervalCallback, speed * 1000);
        } else { // 'continuous' 模式
            const contentTotalHeight = rowHeight * totalItems;
            // 使用传入的speed参数作为动画持续时间（秒）
            const animationDuration = speed;
            containerRef.current?.style.setProperty('--scroll-height', `-${contentTotalHeight}px`);
            containerRef.current?.style.setProperty('--animation-duration', `${animationDuration}s`);
            contentElement.style.transform = '';
            contentElement.style.transition = '';
        }

        return cleanup; // 组件卸载时执行清理

    }, [children.length, mode, speed, slidesToScroll, rowHeight, isPaused]);
    
    const handleMouseEnter = () => pauseOnHover && setIsPaused(true);
    const handleMouseLeave = () => pauseOnHover && setIsPaused(false);

    return (
        <div
            className="custom-carousel-container"
            ref={containerRef}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
        >
            <div
                className={`carousel-content-wrapper ${mode === 'continuous' ? 'continuous-mode' : ''}`}
                style={{ animationPlayState: isPaused ? 'paused' : 'running' }}
                ref={contentRef}
            >
                {/* 渲染两遍子元素以实现无缝效果 */}
                {children}
                {children}
            </div>
        </div>
    );
};
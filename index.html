<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <link rel="icon" href="/favicon.ico"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#000000"/>
    <meta name="description" content="数据可视化设计器"/>
    <meta name="robots" content="index,follow">
    <meta name="author" content="xiaopujun"/>
    <meta name="keywords"
          content="light,chaser,light chaser,LIGHT,LIGHT CHASER,数据,可视化,数据可视化,大屏可视化,看板,大屏看板,数据看板,编辑器,web编辑器,web设计器,设计器,拖拽,可拖拽,可视化图表,可视化工具,追光者设计器,追光者,react大屏,react大屏设计器,react数据可视化,数字孪生">
    <title>交子数字金融集团领导驾驶舱</title>
    <style>
        * {
            user-select: none;
        }
    </style>
</head>
<body style="padding: 0;margin: 0">
<div id="root" style="height: 100%"></div>
<script type="module" src="/src/index.tsx"></script>
</body>
</html>

lc项目规范化开发文档及标准制定

# 一、前言

# 二、约定规范

## 2.1 文件目录

## 2.2 文件命名

## 2.3 react组件参数

## 2.4 class类名（css)

### 2.4.1 lc设计器右侧配置项

![img.png](img.png)

|class名|说明|对应|
|---|---|---|
|lc-cfg-item|设置项|A|
|lc-cfg-title|设置项标题|B|
|lc-cfg-content-row|设置项内容|C|
|lc-cfg-content-block|设置项内容|C|
|lc-cfg-block|块设置项容器|E|
|lc-cfg-block-title|块设置项标题|F|
|lc-cfg-block-content|块设置项内容|G|

# 三、设计器功能大纲

1. 画布设置
    1. 基准高度
    2. 列个数
    3. 元素间隔
    4. 比例
2. 背景设置
    1. 背景模式
    2. 图片尺寸
    3. 图片元数据
    4. 填充方式
    5. 颜色模式
    6. 背景色
3. 项目设置
    1. 大屏名称
    2. 大屏描述
    3. 大屏状态
    4. 元素个数
    5. 创建时间
    6. 更新时间
    7. 数据存储模式

# 四、问题列表

1、组件唯一key问题需要优化

# 本地保存数据结构设计

保存到浏览器本地的数据分成两部分。

1. 第一部分为设计器的所有信息。存储在对象中。方便检索
2. 第二部分为项目的基本信息，存储在数组中。方便列表展示

第二部分项目的基本信息包括

1. 项目id
2. 项目名称
3. 项目截图
4. 项目状态

# 点对点（去中心化）组件通信改造方案

- 第一步: 保持动态扫描机制不变，将扫描得到的数据（其中包括组件类型，组件预览图，组件的class实现等）。将扫描好的数据放入到一个对象中或者map中。以key-value的方式保存
- 第二步: 双击新增组件，传入组件类型key，在上面的map中找到对应的组件class实现。new一个组件实例，调用实例方法创建组件，更新组件

重新设计designer的数据结构，每个组件的数据结构






# G2Plot自定义组件全局变量使用示例

## 功能说明

G2Plot自定义组件现在支持使用全局变量，提供了两种引用方式：

### 1. globalVars.get() API方式
```javascript
function renderG2Plot(container, G2Plot, data, globalVars) {
    // 使用globalVars.get()方法获取全局变量值
    const lineWidth = globalVars.get('lineWidth') || 4;
    const fontSize = globalVars.get('fontSize') || 10;
    const textColor = globalVars.get('textColor') || '#fff';
    
    const g2plot = new G2Plot.Line(container, {
        data: data,
        xField: 'date',
        yField: 'value',
        lineStyle: {
            lineWidth: lineWidth // 使用全局变量
        },
        label: {
            style: {
                fill: textColor, // 使用全局变量
                fontSize: fontSize // 使用全局变量
            }
        }
    });
    
    g2plot.render();
    return g2plot;
}
```

### 2. ${GV::variableName} 模板语法
```javascript
function renderG2Plot(container, G2Plot, data, globalVars) {
    // 注意：模板语法会在代码执行前被替换
    // 例如：${GV::chartTitle} 会被替换为实际的全局变量值
    
    const g2plot = new G2Plot.Column(container, {
        data: data,
        xField: 'category',
        yField: 'value',
        title: {
            text: '${GV::chartTitle}' // 模板语法，会被自动替换
        },
        meta: {
            value: {
                max: ${GV::maxValue} // 数值类型的全局变量
            }
        }
    });
    
    g2plot.render();
    return g2plot;
}
```

## 自动更新机制

当全局变量值发生变化时，G2Plot自定义组件会自动：
1. 检测代码中引用的全局变量
2. 订阅这些变量的变化通知
3. 当变量值改变时，自动重新创建图表

## 支持的全局变量类型

- 字符串类型：用于标题、颜色等
- 数值类型：用于尺寸、阈值等
- 布尔类型：用于开关控制
- 对象类型：用于复杂配置
- 数组类型：用于数据源等

## 最佳实践

1. **使用默认值**：始终为全局变量提供默认值，避免undefined错误
   ```javascript
   const threshold = globalVars.get('threshold') || 100;
   ```

2. **类型检查**：对于复杂类型，建议进行类型检查
   ```javascript
   const config = globalVars.get('chartConfig');
   if (config && typeof config === 'object') {
       // 使用配置
   }
   ```

3. **性能考虑**：避免在循环中频繁调用globalVars.get()
   ```javascript
   // 好的做法：在函数开始时获取所有需要的变量
   const color = globalVars.get('color');
   const size = globalVars.get('size');
   
   // 然后在配置中使用
   ```

## 完整示例

以下是一个完整的示例，展示如何创建一个响应式的柱状图：

```javascript
function renderG2Plot(container, G2Plot, data, globalVars) {
    // 获取全局变量
    const theme = globalVars.get('theme') || 'light';
    const primaryColor = globalVars.get('primaryColor') || '#1890ff';
    const showLegend = globalVars.get('showLegend') !== false; // 默认显示
    const animationDuration = globalVars.get('animationDuration') || 1000;
    const chartTitle = globalVars.get('chartTitle') || '默认标题';

    // 根据主题设置颜色
    const colors = theme === 'dark'
        ? [primaryColor, '#722ed1', '#13c2c2', '#52c41a']
        : [primaryColor, '#fa541c', '#fadb14', '#a0d911'];

    const g2plot = new G2Plot.Column(container, {
        data: data,
        xField: 'category',
        yField: 'value',
        seriesField: 'type',
        color: colors,
        legend: showLegend ? {
            position: 'top-right'
        } : false,
        animation: {
            appear: {
                animation: 'wave-in',
                duration: animationDuration
            }
        },
        title: {
            visible: true,
            text: chartTitle,
            style: {
                fontSize: 16,
                fontWeight: 'bold',
                fill: theme === 'dark' ? '#fff' : '#000'
            }
        },
        label: {
            visible: true,
            style: {
                fill: theme === 'dark' ? '#fff' : '#000'
            }
        }
    });

    g2plot.render();
    return g2plot;
}
```

## 全局变量配置建议

为了配合上述示例，建议创建以下全局变量：

1. **theme** (字符串): 'light' 或 'dark'
2. **primaryColor** (字符串): 主色调，如 '#1890ff'
3. **showLegend** (布尔): 是否显示图例
4. **animationDuration** (数值): 动画持续时间(毫秒)
5. **chartTitle** (字符串): 图表标题

## 引用计数和自动更新

G2Plot自定义组件现在完全集成了全局变量系统：

1. **自动引用计数**：当您在代码中使用全局变量时，系统会自动检测并更新引用计数
2. **实时更新**：当您点击"刷新图表"按钮或保存项目(Ctrl+S)时，引用计数会自动重新计算
3. **变化监听**：当全局变量值发生变化时，图表会自动重新渲染
4. **删除保护**：当全局变量被G2Plot组件引用时，删除时会显示警告

## 测试步骤

1. **创建全局变量**：
   - 创建名为 `primaryColor` 的全局变量，值为 `#ff6b6b`
   - 创建名为 `chartTitle` 的全局变量，值为 `测试图表`

2. **使用全局变量**：
   ```javascript
   function renderG2Plot(container, G2Plot, data, globalVars) {
       const color = globalVars.get('primaryColor') || '#1890ff';
       const title = globalVars.get('chartTitle') || '默认标题';

       const g2plot = new G2Plot.Column(container, {
           data: data,
           xField: 'category',
           yField: 'value',
           color: color,
           title: {
               visible: true,
               text: title
           }
       });

       g2plot.render();
       return g2plot;
   }
   ```

3. **验证功能**：
   - 点击"刷新图表"按钮，检查全局变量列表中的引用计数是否正确
   - 修改全局变量的值，观察图表是否自动更新
   - 尝试删除被引用的全局变量，应该看到警告提示

## 注意事项

1. 模板语法`${GV::variableName}`在代码执行前被替换，适用于静态配置
2. API方式`globalVars.get()`在运行时获取值，适用于动态逻辑
3. 全局变量名称区分大小写
4. 当全局变量不存在时，`globalVars.get()`返回undefined
5. 组件会自动检测代码中的全局变量引用并订阅变化
6. 当全局变量值改变时，图表会自动重新渲染
7. 引用计数会在代码更新、项目保存时自动重新计算

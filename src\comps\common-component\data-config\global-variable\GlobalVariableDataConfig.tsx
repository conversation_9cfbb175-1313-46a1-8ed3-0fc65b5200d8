/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {useRef, useState} from 'react';
import AbstractDesignerController from "../../../../framework/core/AbstractDesignerController.ts";
import {FieldChangeData, LCGUI} from "../../../../json-schema/LCGUI.tsx";
import {Control} from "../../../../json-schema/SchemaTypes.ts";
import globalVariableManager from "../../../../designer/manager/GlobalVariableManager.ts";
import {observer} from "mobx-react";
import {globalMessage} from "../../../../framework/message/GlobalMessage.tsx";
import ObjectUtil from "../../../../utils/ObjectUtil.ts";
import {DataConfigType} from "../../../../designer/DesignerType.ts";

export interface GlobalVariableDataConfigProps {
    controller: AbstractDesignerController;
    selectedGlobalVariableId?: string;
}

export const GlobalVariableDataConfig: React.FC<GlobalVariableDataConfigProps> = observer((props) => {
    const {controller, selectedGlobalVariableId} = props;
    const dataSource = controller.getConfig()?.data as DataConfigType;
    const [currentVariableId, setCurrentVariableId] = useState<string>(selectedGlobalVariableId || '');
    const [filter, setFilter] = useState<string>(dataSource.globalVariableFilter || 'function filter(data){\n\n\n\treturn data\n}');
    const [testResult, setTestResult] = useState<string>('');
    const [count, setCount] = useState(0);
    const dataRef = useRef({
        selectedGlobalVariableId: selectedGlobalVariableId || '',
        filter: dataSource.globalVariableFilter || 'function filter(data){\n\n\n\treturn data\n}'
    });

    // 获取全局变量选项
    const getGlobalVariableOptions = () => {
        return globalVariableManager.getAllVariableDefinitions().map(def => ({
            value: def.id,
            label: def.name || '未命名变量'
        }));
    };

    // 测试全局变量数据和过滤器
    const testGlobalVariable = async () => {
        if (!dataRef.current.selectedGlobalVariableId) {
            globalMessage.messageApi?.error('请先选择一个全局变量');
            return;
        }

        try {
            // 获取全局变量定义和状态
            const definition = globalVariableManager.getVariableDefinition(dataRef.current.selectedGlobalVariableId);
            if (!definition) {
                setTestResult(JSON.stringify({error: '全局变量不存在'}, null, 2));
                return;
            }

            // 获取全局变量原始值
            let rawValue = globalVariableManager.getVariableValue(dataRef.current.selectedGlobalVariableId);

            // 检查是否正在加载
            const isLoading = globalVariableManager.isLoading.get(dataRef.current.selectedGlobalVariableId);
            const errorState = globalVariableManager.errorStates.get(dataRef.current.selectedGlobalVariableId);

            // 如果值为undefined，检查是否有错误状态或初始值
            if (rawValue === undefined || rawValue === null) {
                if (isLoading) {
                    setTestResult(JSON.stringify({info: '全局变量正在加载中...'}, null, 2));
                    return;
                } else if (errorState) {
                    // 有错误，但可能有初始值/默认值
                    if (definition.initialOrFallbackValue !== undefined) {
                        rawValue = definition.initialOrFallbackValue;
                        setTestResult(JSON.stringify({
                            warning: '数据获取失败，使用默认值',
                            error: errorState,
                            usingFallbackValue: true,
                            rawValue: rawValue
                        }, null, 2));
                    } else {
                        setTestResult(JSON.stringify({
                            error: '全局变量获取失败且无默认值',
                            message: errorState
                        }, null, 2));
                        return;
                    }
                } else {
                    setTestResult(JSON.stringify({error: '全局变量值未定义'}, null, 2));
                    return;
                }
            }

            let finalValue = rawValue;

            // 执行过滤器
            if (dataRef.current.filter && dataRef.current.filter.trim() !== '') {
                try {
                    // 使用GlobalVariableManager的过滤器执行方法，确保支持 ${GV::variableName} 格式
                    finalValue = await globalVariableManager._parseAndExecuteFilter(
                        dataRef.current.filter,
                        rawValue,
                        dataRef.current.selectedGlobalVariableId
                    );
                } catch (filterError) {
                    setTestResult(JSON.stringify({
                        error: '过滤器执行错误',
                        message: (filterError as Error).message,
                        rawValue: rawValue
                    }, null, 2));
                    return;
                }
            }

            setTestResult(JSON.stringify(finalValue, null, 2));
            globalMessage.messageApi?.success('测试成功');
        } catch (error) {
            setTestResult(JSON.stringify({
                error: '测试失败',
                message: (error as Error).message
            }, null, 2));
            globalMessage.messageApi?.error('测试失败');
        } finally {
            setCount(count + 1);
        }
    };

    // 保存配置
    const saveConfig = async () => {
        if (!dataRef.current.selectedGlobalVariableId) {
            globalMessage.messageApi?.error('请先选择一个全局变量');
            return;
        }

        try {
            // 获取全局变量定义和状态
            const definition = globalVariableManager.getVariableDefinition(dataRef.current.selectedGlobalVariableId);
            if (!definition) {
                globalMessage.messageApi?.error('全局变量不存在');
                return;
            }

            // 获取全局变量原始值
            let rawValue = globalVariableManager.getVariableValue(dataRef.current.selectedGlobalVariableId);

            // 检查是否正在加载
            const isLoading = globalVariableManager.isLoading.get(dataRef.current.selectedGlobalVariableId);
            const errorState = globalVariableManager.errorStates.get(dataRef.current.selectedGlobalVariableId);

            // 如果值为undefined，检查是否有错误状态或初始值
            if (rawValue === undefined || rawValue === null) {
                if (isLoading) {
                    globalMessage.messageApi?.warning('全局变量正在加载中，请稍后再试');
                    return;
                } else if (errorState) {
                    // 有错误，但可能有初始值/默认值
                    if (definition.initialOrFallbackValue !== undefined) {
                        rawValue = definition.initialOrFallbackValue;
                        globalMessage.messageApi?.warning('数据获取失败，使用默认值');
                    } else {
                        globalMessage.messageApi?.error(`全局变量获取失败且无默认值: ${errorState}`);
                        return;
                    }
                } else {
                    globalMessage.messageApi?.error('全局变量值未定义');
                    return;
                }
            }

            let finalValue = rawValue;

            // 执行过滤器
            if (dataRef.current.filter && dataRef.current.filter.trim() !== '') {
                try {
                    // 使用GlobalVariableManager的过滤器执行方法，确保支持 ${GV::variableName} 格式
                    finalValue = await globalVariableManager._parseAndExecuteFilter(
                        dataRef.current.filter,
                        rawValue,
                        dataRef.current.selectedGlobalVariableId
                    );
                } catch (filterError) {
                    globalMessage.messageApi?.error(`过滤器执行错误: ${(filterError as Error).message}`);
                    return;
                }
            }

            // 保存配置并更新组件数据
            controller.update({
                data: {
                    selectedGlobalVariableId: dataRef.current.selectedGlobalVariableId,
                    globalVariableFilter: dataRef.current.filter
                    // 注意：不要修改staticData，那是静态数据源的数据
                }
            }, {reRender: false});

            controller.changeData(finalValue);

            // 重新建立全局变量订阅关系（因为可能更换了变量或过滤器）
            if (typeof (controller as any).setupGlobalVariableSubscription === 'function') {
                // 先取消所有旧的订阅
                const componentId = (controller.getConfig() as any)?.base?.id;
                if (componentId) {
                    // 取消对所有全局变量的订阅
                    globalVariableManager.getAllVariableDefinitions().forEach(variable => {
                        globalVariableManager.unsubscribe(variable.id, componentId);
                    });
                }

                // 建立新的订阅关系
                await (controller as any).setupGlobalVariableSubscription(dataRef.current.selectedGlobalVariableId);
            }

            // 保存后重新计算引用计数（过滤器可能包含全局变量引用）
            globalVariableManager.recalculateAllReferenceCounts();

            globalMessage.messageApi?.success('配置已保存');
        } catch (error) {
            globalMessage.messageApi?.error(`保存失败: ${(error as Error).message}`);
        }
    };

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataFragment} = fieldChangeData;

        if (id === 'selectedGlobalVariableId') {
            const newVariableId = data as string;
            const oldVariableId = dataRef.current.selectedGlobalVariableId;

            // 更新引用计数
            if (oldVariableId && oldVariableId !== newVariableId) {
                // 减少旧变量的引用计数
                globalVariableManager.decrementReferenceCount(oldVariableId);
            }
            if (newVariableId && newVariableId !== oldVariableId) {
                // 增加新变量的引用计数
                globalVariableManager.incrementReferenceCount(newVariableId);
            }

            setCurrentVariableId(newVariableId);
            dataRef.current.selectedGlobalVariableId = newVariableId;
        } else if (id === 'filter') {
            setFilter(data as string);
            dataRef.current.filter = data as string;
        } else if (id === 'testGlobalVariable') {
            testGlobalVariable();
            return;
        } else if (id === 'saveConfig') {
            saveConfig();
            return;
        } else if (id === 'testResult') {
            return; // 只读字段，不处理
        } else {
            dataRef.current = ObjectUtil.merge(dataRef.current, dataFragment);
        }

        setCount(count + 1);
    };

    const variableOptions = getGlobalVariableOptions();

    const schema: Control = {
        type: 'grid',
        config: {gridGap: '10px'},
        children: [
            {
                key: 'selectedGlobalVariableId',
                type: 'select',
                label: '选择全局变量',
                value: currentVariableId,
                config: {
                    options: variableOptions,
                    placeholder: variableOptions.length > 0 ? '请选择一个全局变量' : '暂无可用的全局变量',
                    disabled: variableOptions.length === 0
                }
            },
            {
                type: 'card-panel',
                label: '过滤器',
                tip: '对全局变量数据进行二次处理的JavaScript函数',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        key: 'filter',
                        type: 'code-editor',
                        config: {
                            height: 200,
                            language: 'javascript'
                        },
                        value: filter,
                    }
                ]
            },
            {
                type: 'card-panel',
                label: '响应结果',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        id: 'testResult',
                        type: 'code-editor',
                        config: {
                            readonly: true,
                            height: 160,
                        },
                        reRender: true,
                        value: testResult,
                    }
                ]
            },
            {
                type: 'grid',
                config: {columns: 2, gridGap: '10px'},
                children: [
                    {
                        id: 'testGlobalVariable',
                        type: 'button',
                        config: {
                            children: '测试',
                        }
                    },
                    {
                        id: 'saveConfig',
                        type: 'button',
                        config: {
                            children: '保存'
                        }
                    }
                ]
            }
        ]
    };

    return (
        <div style={{marginTop: '10px'}}>
            <LCGUI schema={schema} onFieldChange={onFieldChange}/>

            {/* 无全局变量时的提示 */}
            {variableOptions.length === 0 && (
                <div style={{
                    marginTop: '12px',
                    padding: '12px',
                    backgroundColor: '#fff3cd',
                    borderRadius: '6px',
                    border: '1px solid #ffeaa7',
                    color: '#856404'
                }}>
                    <div style={{fontSize: '14px', marginBottom: '4px'}}>暂无可用的全局变量</div>
                    <div style={{fontSize: '12px'}}>
                        请先在左侧"全局变量"面板中创建全局变量
                    </div>
                </div>
            )}
        </div>
    );
});

export default GlobalVariableDataConfig;

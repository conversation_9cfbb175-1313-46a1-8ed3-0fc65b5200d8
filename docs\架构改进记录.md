# Light Chaser 架构改进记录

本文档记录 Light Chaser 项目中的重要架构改进和技术优化，用于追踪系统演进历程和设计决策。

---

## 2025-01-10 静态数据源架构重大改进

### 改进背景

静态数据源在使用全局变量时存在两个核心问题：

1. **重复处理问题**：设计模式保存后，预览模式再次应用过滤器导致数据被处理两次
   - 设计模式：原始数据 → 过滤器 → 过滤后数据存储到 `staticData`
   - 预览模式：从 `staticData` 读取（已是过滤后数据）→ 再次过滤器 → 重复处理

2. **自动更新缺失**：全局变量变化时，静态数据源组件无法自动更新
   - 全局变量数据源：支持自动订阅和更新
   - 静态数据源：缺乏全局变量变化的响应机制

### 问题分析

#### 现有数据流设计
系统采用 `staticData` 作为"数据中转站"的设计：
- **设计意图**：所有数据源的最终结果都存储在 `staticData` 中，组件统一从此读取
- **API数据源**：`apiData` (配置) + `filter` → `staticData` (结果)
- **全局变量**：`selectedGlobalVariableId` (配置) + `globalVariableFilter` → `staticData` (结果)
- **静态数据源**：直接存储在 `staticData` 中，缺乏配置与结果的分离

#### 根本原因
静态数据源缺乏与动态数据源一致的"配置 + 处理 → 结果"的处理模式，导致：
- 原始数据和处理后数据混淆
- 无法重新执行处理逻辑
- 缺乏响应全局变量变化的能力

### 解决方案：添加原始数据字段

采用**方案1**：为静态数据源添加专门的原始数据字段，实现与动态数据源完全一致的处理模式。

#### 核心设计思路
1. **数据分离**：`rawStaticData` 存储原始数据，`staticData` 存储处理结果
2. **模式统一**：静态数据源采用与动态数据源相同的"配置 + 处理 → 结果"模式
3. **架构兼容**：完全符合现有的 `staticData` 作为"数据中转站"的设计哲学

### 技术实现

#### 1. 数据结构扩展

**文件**：`src/designer/DesignerType.ts`

```typescript
export interface DataConfigType {
    sourceType?: 'static' | 'api' | 'database' | 'excel' | 'globalVariable';
    staticData?: any;        // 所有数据源的最终数据中转站
    rawStaticData?: any;     // 静态数据源的原始数据 ✨新增
    staticDataFilter?: string;
    // ... 其他字段
}
```

#### 2. 统一的数据处理模式

改进后的数据流：
- **API数据源**：`apiData` (配置) + `filter` → `staticData` (结果)
- **静态数据源**：`rawStaticData` (原始) + `staticDataFilter` → `staticData` (结果) ✨新增
- **全局变量**：`selectedGlobalVariableId` (配置) + `globalVariableFilter` → `staticData` (结果)

#### 3. 静态数据配置组件改进

**文件**：`src/comps/common-component/data-config/static/StaticDataConfig.tsx`

**关键改动**：
- 优先使用 `rawStaticData`，向后兼容 `staticData`
- 保存时：原始数据 → `rawStaticData`，处理后数据 → `staticData`

```typescript
// 保存配置并更新组件数据
controller.update({
    data: {
        rawStaticData: rawData,        // 保存原始数据
        staticData: finalData,         // 保存处理后的数据
        staticDataFilter: dataRef.current.filter
    }
}, {reRender: false});
```

#### 4. 专门的刷新机制

**文件**：`src/framework/core/AbstractDesignerController.ts`

**新增方法**：`refreshStaticData()`
- 从 `rawStaticData` 读取原始数据
- 重新执行过滤器处理
- 更新 `staticData` 和组件显示

```typescript
public refreshStaticData = async () => {
    const {data} = this.config! as ComponentBaseProps;
    if (!data || data.sourceType !== 'static') return;

    const {rawStaticData, staticDataFilter} = data;
    const sourceData = rawStaticData !== undefined ? rawStaticData : data.staticData;
    
    // 重新执行过滤器并更新
    const finalData = await globalVariableManager._parseAndExecuteFilter(
        staticDataFilter, sourceData, 'static-data-refresh'
    );
    
    this.config!.data!.staticData = finalData;
    this.changeData(finalData);
};
```

#### 5. 间接依赖通知优化

**文件**：`src/designer/manager/GlobalVariableManager.ts`

**改进逻辑**：
- 静态数据源调用 `refreshStaticData()` 而不是 `loadComponentData()`
- 引用计数优先检查 `rawStaticData`

### 技术优势

1. **架构一致性**：静态数据源与动态数据源处理逻辑完全统一
2. **向后兼容**：优先使用 `rawStaticData`，不存在时回退到 `staticData`
3. **设计符合性**：完全符合 `staticData` 作为"数据中转站"的核心设计
4. **功能完整性**：解决了重复处理和自动更新两个核心问题
5. **可维护性**：统一的处理模式降低了代码复杂度

### 影响范围

- `src/designer/DesignerType.ts` - 数据类型定义扩展
- `src/comps/common-component/data-config/static/StaticDataConfig.tsx` - 静态数据配置组件
- `src/framework/core/AbstractDesignerController.ts` - 控制器基类
- `src/designer/manager/GlobalVariableManager.ts` - 全局变量管理器

### 测试验证

改进后的功能验证：
1. ✅ 设计模式下过滤器正常工作，显示处理后数据
2. ✅ 预览模式下直接读取处理后数据，无重复处理
3. ✅ 全局变量变化时，静态数据源组件自动更新
4. ✅ 向后兼容，现有项目无需修改

### 总结

这个改进为静态数据源提供了与动态数据源完全一致的功能体验，是系统架构的重要完善。通过引入 `rawStaticData` 字段，实现了数据的合理分离，既保持了现有设计的一致性，又解决了功能缺陷，为后续的功能扩展奠定了坚实基础。

---

## 改进记录模板

### 改进标题
**日期**：YYYY-MM-DD  
**类型**：架构改进 | 性能优化 | 功能增强 | 问题修复

### 改进背景
描述改进的背景和动机

### 问题分析
详细分析现有问题和根本原因

### 解决方案
描述采用的解决方案和设计思路

### 技术实现
具体的技术实现细节

### 技术优势
改进带来的技术优势

### 影响范围
涉及的文件和模块

### 测试验证
功能验证和测试结果

### 总结
改进的总体评价和意义

/**
 * 组织架构图组件定义
 * 基于ReactFlow实现的组织架构图组件
 */

import {BaseInfoType, EventInfo, MenuToConfigMappingType} from "../../../framework/core/AbstractDefinition";
import {ClazzTemplate} from "../../common-component/CommonTypes.ts";
import {MenuInfo} from "../../../designer/right/MenuType";
import orgChartImg from './org-chart.png';
import {OrgChartController} from "./OrgChartController";
import {OrgChartConfig} from "./OrgChartConfig";
import {OrgChartComponentProps} from "./OrgChartComponent";
import AbstractDesignerDefinition from "../../../framework/core/AbstractDesignerDefinition.ts";
import BaseInfo from "../../common-component/base-info/BaseInfo";
import DataConfig from "../../common-component/data-config/DataConfig";
import FilterConfig from "../../common-component/filter-config/FilterConfig";

export default class OrgChartDefinition extends AbstractDesignerDefinition<OrgChartController, OrgChartComponentProps> {
    getBaseInfo(): BaseInfoType {
        return {
            compName: "组织架构图",
            compKey: "OrgChart",
            categorize: "other",
            width: 600,
            height: 400,
        };
    }

    getChartImg(): string | null {
        return orgChartImg;
    }

    getController(): ClazzTemplate<OrgChartController> | null {
        return OrgChartController;
    }

    getInitConfig(): OrgChartComponentProps {
        return {
            base: {
                id: "",
                name: '组织架构图',
                type: 'OrgChart',
            },
            style: {
                // 简化的ReactFlow配置 - 只保留3个配置项
                zoomOnScroll: true,        // 滚轮缩放
                panOnDrag: false,          // 拖拽平移（默认关闭，避免与组件双击配置冲突）
                nodeDraggable: true,       // 节点可拖拽
            },
            filter: {
                enable: false,
                blur: 0,
                brightness: 1,
                contrast: 1,
                opacity: 1,
                saturate: 1,
                hueRotate: 0
            },
            data: {
                sourceType: 'static',
                staticData: [
                    {
                        id: 'node1',
                        type: 'from',
                        label: '数金本部',
                        connections: [
                            { target: 'node2', count: 5 }
                        ]
                    },
                    {
                        id: 'node3',
                        type: 'from',
                        label: '天府通',
                        connections: [
                            { target: 'node4', count: 8 },
                            { target: 'node7', count: 3 }
                        ]
                    },
                    {
                        id: 'node5',
                        type: 'from',
                        label: '金控数据',
                        connections: [
                            { target: 'node2', count: 12 }
                        ]
                    },
                    {
                        id: 'node6',
                        type: 'from',
                        label: '金控征信',
                        connections: [
                            { target: 'node2', count: 7 },
                            { target: 'node4', count: 4 }
                        ]
                    },
                    {
                        id: 'node4',
                        type: 'to',
                        label: '集团'
                    },
                    {
                        id: 'node2',
                        type: 'to',
                        label: '发改委'
                    },
                    {
                        id: 'node7',
                        type: 'to',
                        label: '金融局'
                    }
                ]
            }
        };
    }

    getMenuList(): Array<MenuInfo> {
        return super.getMenuList().filter((item: MenuInfo) => (item.key !== 'theme'));
    }

    getMenuToConfigContentMap(): MenuToConfigMappingType {
        return {
            base: BaseInfo,
            style: OrgChartConfig,
            data: DataConfig,
            filter: FilterConfig,
        };
    }

    getEventList(): Array<EventInfo> {
        return [
            {id: "nodeClick", name: "点击节点"},
            {id: "nodeDoubleClick", name: "双击节点"},
            {id: "nodeHover", name: "节点悬停"},
        ];
    }
}

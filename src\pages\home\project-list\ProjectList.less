/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.project-list-container {
  height: calc(100vh - 60px);
  overflow: scroll;

  .project-list-header {
    color: #c5c5c5;
    display: flex;
    padding: 13px;
    border-bottom: 1px solid #2e2e2e;
    justify-content: space-between;

    .project-list-header-left {
      .project-list-search {
        margin-right: 13px;
      }
    }
  }

  .project-list {
    padding: 13px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    overflow-y: scroll;
    grid-gap: 25px;

  }

  .project-list-page {
    text-align: right;
    margin: 30px 10px 0 0;
    display: block;
  }
}
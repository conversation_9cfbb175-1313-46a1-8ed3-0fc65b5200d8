/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import Loading from "../loading/Loading";
import {loader} from "@monaco-editor/react";
import './MonacoEditor.less';

import * as monaco from 'monaco-editor';
import EditorSimpleWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
import JSONWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
import TypeScriptWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';
import 'monaco-editor/esm/vs/basic-languages/sql/sql.contribution';
import {UIContainer, UIContainerProps} from "../ui-container/UIContainer.tsx";
import {Suspense, lazy, useRef, forwardRef, useImperativeHandle} from "react";

self.MonacoEnvironment = {
    getWorker(_, label) {
        if (label === 'json')
            return new JSONWorker();
        if (label === 'typescript' || label === 'javascript')
            return new TypeScriptWorker();
        return new EditorSimpleWorker();
    },
};

loader.config({monaco});

const Editor = lazy(() => import('@monaco-editor/react'));

export interface MonacoEditorRef {
    formatDocument: () => void;
}

export interface MonacoEditorProps extends UIContainerProps {
    value?: string;
    defaultValue?: string;
    onChange?: (value?: string) => void;
    onSave?: (value?: string) => void; // 新增保存事件回调
    readonly?: boolean;
    language?: 'javascript' | 'json' | 'sql';
    width?: string | number;
    height?: string | number;
    quickSuggestions?: boolean;
    folding?: boolean;
    foldingStrategy?: 'auto' | 'indentation';
}

const MonacoEditor = forwardRef<MonacoEditorRef, MonacoEditorProps>((props, ref) => {
    const {
        value, defaultValue, onChange, onSave, language
        , width, height, readonly, quickSuggestions, folding, foldingStrategy, ...containerProps
    } = props;

    const editorRef = useRef<any>(null);

    useImperativeHandle(ref, () => ({
        formatDocument: () => {
            if (editorRef.current) {
                editorRef.current.trigger('anyString', 'editor.action.formatDocument');
            }
        }
    }));

    const handleEditorDidMount = (editor: any) => {
        editorRef.current = editor;

        // 添加Ctrl+S保存事件监听
        if (onSave) {
            editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
                const currentValue = editor.getValue();
                onSave(currentValue);
            });
        }
    };

    return (
        <UIContainer className={'lc-code-editor'} {...containerProps}>
            <div style={{width, height}} className={'monaco-editor-container'}>
                <Suspense fallback={<Loading/>}>
                    <Editor defaultLanguage={language || 'json'}
                            theme="vs-dark"
                            onChange={onChange}
                            onMount={handleEditorDidMount}
                            height={'100%'}
                            width={'100%'}
                            options={{
                                minimap: {enabled: false},
                                quickSuggestions: !!quickSuggestions,
                                folding: folding !== undefined ? folding : true,
                                foldingStrategy: foldingStrategy || 'indentation',
                                readOnly: readonly || false,
                                renderValidationDecorations: 'off',
                                mouseWheelZoom: true,
                                scrollBeyondLastLine: false,
                                renderLineHighlight: 'none',
                                hideCursorInOverviewRuler: true,
                                overviewRulerLanes: 0,
                                overviewRulerBorder: false,
                            }}
                            loading={<Loading/>}
                            value={value}
                            defaultValue={defaultValue}
                    />
                </Suspense>
            </div>
        </UIContainer>
    )
});

MonacoEditor.displayName = 'MonacoEditor';

export default MonacoEditor;

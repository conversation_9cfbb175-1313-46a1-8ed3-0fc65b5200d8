/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

export default class ColorUtil {

    public static colorConversion(color: string) {
        if (color.length >= 4 && color.length <= 9 && color[0] === '#') {
            const rgba = ColorUtil.hexToRgba(color, 1);
            return {hex: color, rgba: rgba};
        } else if (color.includes('rgb')) {
            const hex = ColorUtil.rgbaToHex(color);
            return {hex: hex, rgba: color};
        } else {
            console.warn('color is not valid', color);
            return {hex: '#000000', rgba: 'rgba(0,0,0,1)'};
        }
    }

    public static hexToRgba(hex: string, alpha: number) {
        // 去掉颜色值中的 # 符号
        hex = hex.replace('#', '');
        // 计算 RGB 值
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);
        // 返回 RGBA 值
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    public static rgbaToHex(rgba: string) {
        const parts = rgba.substring(rgba.indexOf('(')).split(',');
        const r = parseInt(parts[0].substring(1).trim());
        const g = parseInt(parts[1].trim());
        const b = parseInt(parts[2].trim());
        const a = parseFloat(parts[3] ? parts[3].substring(0, parts[3].length - 1).trim() : '1');

        const rr = r.toString(16).length === 1 ? "0" + r.toString(16) : r.toString(16);
        const gg = g.toString(16).length === 1 ? "0" + g.toString(16) : g.toString(16);
        const bb = b.toString(16).length === 1 ? "0" + b.toString(16) : b.toString(16);

        const aa = Math.round(a * 255).toString(16).length === 1 ? "0" + Math.round(a * 255).toString(16) : Math.round(a * 255).toString(16);
        return "#" + rr + gg + bb + aa;
    }

    public static parseGradient(gradient: string) {
        // 提取角度
        const angle = gradient.match(/-?\d+deg/);
        const angleValue = angle ? parseInt(angle[0]) : 0;

        // 提取颜色和位置
        const colors = gradient.match(/rgba?\([^)]+\) \d+(\.\d+)?%?/g);
        const parsedColors = colors?.map((color) => {
            const cutPos = color.lastIndexOf(' ');
            return {
                color: color.substring(0, cutPos),
                pos: parseFloat(color.substring(cutPos)) / 100
            };
        });

        return {
            angle: angleValue,
            colors: parsedColors
        };
    }

    public static parseAntdGradientToCss(gradient: string) {
        const angle = gradient.match(/l\((\d+)\)/);
        const angleValue = angle ? parseInt(angle[1]) : 0;

        const colors = gradient.match(/(\d+):rgba?\([^)]+\)/g);

        const parsedColors = colors?.map((color) => {
            const [pos, rgba] = color.split(':');
            return {
                color: rgba,
                pos: parseFloat(pos) / 100
            };
        });

        return `linear-gradient(${angleValue}deg,${parsedColors?.map((color) =>
            `${color.color} ${color.pos * 100}%`)
            .join(', ')})`;
    }

    // 将线性渐变对象转换为G2Plot支持的渐变字符串
    public static linearGradientToCss(gradient: any): string {
        if (!gradient || !gradient.colorStops || !Array.isArray(gradient.colorStops)) {
            return '';
        }

        // 处理每个颜色停止点的透明度
        const colorStops = gradient.colorStops.map((stop: any) => {
            const color = stop.color;
            const opacity = stop.opacity !== undefined ? stop.opacity : 1;
            let processedColor = color;

            if (opacity < 1) {
                // 如果是hex颜色，转换为rgba并应用透明度
                if (color.startsWith('#')) {
                    processedColor = ColorUtil.hexToRgba(color, opacity);
                } else if (color.startsWith('rgb(')) {
                    // 将rgb转换为rgba
                    processedColor = color.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`);
                } else if (color.startsWith('rgba(')) {
                    // 如果已经是rgba，需要合并透明度
                    const rgbaMatch = color.match(/rgba\(([^)]+)\)/);
                    if (rgbaMatch) {
                        const parts = rgbaMatch[1].split(',');
                        if (parts.length >= 4) {
                            const existingAlpha = parseFloat(parts[3].trim());
                            const newAlpha = existingAlpha * opacity;
                            parts[3] = ` ${newAlpha}`;
                            processedColor = `rgba(${parts.join(',')})`;
                        }
                    }
                }
            }

            return {
                ...stop,
                color: processedColor
            };
        });

        // G2Plot使用的格式: l(angle) colorStop1:color1 colorStop2:color2 ...
        return `l(${gradient.angle || 0}) ${colorStops
            .map((stop: any) => `${stop.offset}:${stop.color}`)
            .join(' ')}`;
    }

    // 将径向渐变对象转换为G2Plot支持的渐变字符串
    public static radialGradientToCss(gradient: any): string {
        if (!gradient || !gradient.colorStops || !Array.isArray(gradient.colorStops) || !gradient.position) {
            return '';
        }

        // 处理每个颜色停止点的透明度
        const colorStops = gradient.colorStops.map((stop: any) => {
            const color = stop.color;
            const opacity = stop.opacity !== undefined ? stop.opacity : 1;
            let processedColor = color;

            if (opacity < 1) {
                // 如果是hex颜色，转换为rgba并应用透明度
                if (color.startsWith('#')) {
                    processedColor = ColorUtil.hexToRgba(color, opacity);
                } else if (color.startsWith('rgb(')) {
                    // 将rgb转换为rgba
                    processedColor = color.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`);
                } else if (color.startsWith('rgba(')) {
                    // 如果已经是rgba，需要合并透明度
                    const rgbaMatch = color.match(/rgba\(([^)]+)\)/);
                    if (rgbaMatch) {
                        const parts = rgbaMatch[1].split(',');
                        if (parts.length >= 4) {
                            const existingAlpha = parseFloat(parts[3].trim());
                            const newAlpha = existingAlpha * opacity;
                            parts[3] = ` ${newAlpha}`;
                            processedColor = `rgba(${parts.join(',')})`;
                        }
                    }
                }
            }

            return {
                ...stop,
                color: processedColor
            };
        });

        const { x = 0.5, y = 0.5 } = gradient.position;
        // G2Plot使用的格式: r(x,y,r) colorStop1:color1 colorStop2:color2 ...
        return `r(${x}, ${y}, 0.1) ${colorStops
            .map((stop: any) => `${stop.offset}:${stop.color}`)
            .join(' ')}`;
    }

    // 将渐变对象转换为G2Plot支持的渐变字符串
    public static gradientToCss(gradient: any): string {
        if (!gradient || typeof gradient !== 'object') {
            return '';
        }

        if (gradient.type === 'linear-gradient') {
            return ColorUtil.linearGradientToCss(gradient);
        } else if (gradient.type === 'radial-gradient') {
            return ColorUtil.radialGradientToCss(gradient);
        }

        return '';
    }

    // 判断值是否为渐变对象
    public static isGradient(value: any): boolean {
        return value &&
               typeof value === 'object' &&
               (value.type === 'linear-gradient' || value.type === 'radial-gradient') &&
               Array.isArray(value.colorStops);
    }

    // 将渐变CSS字符串解析为渐变对象
    public static parseCssGradient(cssGradient: string): any {
        if (!cssGradient) return null;

        // 线性渐变
        if (cssGradient.startsWith('linear-gradient')) {
            const angleMatch = cssGradient.match(/linear-gradient\(\s*(\d+)deg/);
            const angle = angleMatch ? parseInt(angleMatch[1]) : 0;

            const colorStopsMatch = cssGradient.match(/(?:#[0-9a-f]{3,8}|rgba?\([^)]+\))\s+(\d+(?:\.\d+)?)%/g);
            const colorStops = colorStopsMatch?.map(stop => {
                const lastSpaceIndex = stop.lastIndexOf(' ');
                const color = stop.substring(0, lastSpaceIndex).trim();
                const offset = parseFloat(stop.substring(lastSpaceIndex).trim()) / 100;

                // 从颜色中提取透明度
                let opacity = 1;
                if (color.startsWith('rgba(')) {
                    const rgbaMatch = color.match(/rgba\(([^)]+)\)/);
                    if (rgbaMatch) {
                        const parts = rgbaMatch[1].split(',');
                        if (parts.length >= 4) {
                            opacity = parseFloat(parts[3].trim());
                        }
                    }
                }

                return { color, offset, opacity };
            }) || [];

            return {
                type: 'linear-gradient',
                angle,
                colorStops
            };
        }

        // 径向渐变
        if (cssGradient.startsWith('radial-gradient')) {
            const posMatch = cssGradient.match(/radial-gradient\(\s*circle\s+at\s+(\d+(?:\.\d+)?)%\s+(\d+(?:\.\d+)?)%/);
            const position = posMatch ? { x: parseFloat(posMatch[1]) / 100, y: parseFloat(posMatch[2]) / 100 } : { x: 0.5, y: 0.5 };

            const colorStopsMatch = cssGradient.match(/(?:#[0-9a-f]{3,8}|rgba?\([^)]+\))\s+(\d+(?:\.\d+)?)%/g);
            const colorStops = colorStopsMatch?.map(stop => {
                const lastSpaceIndex = stop.lastIndexOf(' ');
                const color = stop.substring(0, lastSpaceIndex).trim();
                const offset = parseFloat(stop.substring(lastSpaceIndex).trim()) / 100;

                // 从颜色中提取透明度
                let opacity = 1;
                if (color.startsWith('rgba(')) {
                    const rgbaMatch = color.match(/rgba\(([^)]+)\)/);
                    if (rgbaMatch) {
                        const parts = rgbaMatch[1].split(',');
                        if (parts.length >= 4) {
                            opacity = parseFloat(parts[3].trim());
                        }
                    }
                }

                return { color, offset, opacity };
            }) || [];

            return {
                type: 'radial-gradient',
                position,
                colorStops
            };
        }

        return null;
    }
}

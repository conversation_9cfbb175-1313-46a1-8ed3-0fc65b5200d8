/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.custom-component {
  position: relative;
  box-sizing: border-box;
  
  .custom-component-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    
    .custom-component-title {
      font-size: 16px;
      font-weight: 500;
      color: #505050;
      margin-bottom: 10px;
    }
    
    .custom-component-error {
      margin-top: 10px;
      padding: 8px 12px;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 4px;
      color: #ff4d4f;
      font-size: 12px;
      max-width: 90%;
      overflow: auto;
      max-height: 100px;
    }
    
    .custom-component-hint {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 5px;
    }
  }
  
  .custom-component-content {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
} 
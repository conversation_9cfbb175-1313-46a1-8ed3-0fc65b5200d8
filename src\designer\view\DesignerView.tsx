/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import viewDesignerLoader from "../loader/ViewDesignerLoader.ts";
import {lazy, Suspense, useEffect} from 'react';
import './DesignerView.less';
import layerManager from "../manager/LayerManager.ts";
import {observer} from "mobx-react";
import Loading from "../../json-schema/ui/loading/Loading";
import layerBuilder from "../left/layer-list/LayerBuilder";
import {SaveType} from "../DesignerType.ts";
import canvasManager from "../header/items/canvas/CanvasManager.ts";
import designerManager from "../manager/DesignerManager.ts";
import '../../designer/resource/font/FontGlobal.css';
import ScaleAction from "../../framework/core/ScaleAction.ts";
import GlobalVariableDebugger from "../debug/GlobalVariableDebugger.tsx";

const ScreenFit = lazy(() => import('../../framework/screen-fit/ScreenFit.tsx'));


export interface DesignerViewProps {
    id: string;
    type: SaveType;
}

const DesignerView = observer((props: DesignerViewProps) => {

    const {id, type} = props;

    useEffect(() => {
        viewDesignerLoader.load(id, type);
    }, []);

    const {layerConfigs} = layerManager!;
    const {loaded} = designerManager;
    const {canvasConfig: {width, height, adaptationType}} = canvasManager
    if (!loaded)
        return <Loading/>;

    // 根据适配模式决定画布样式
    const getCanvasStyle = () => {
        // fit-width 模式下也需要设置原始尺寸，让 ScreenFit 进行缩放处理
        return {width, height, background: 'black', overflow: 'hidden', position: "relative" as const}
    }

    const canvasDom = <div style={getCanvasStyle()}>
        {layerBuilder.buildCanvasComponents(layerConfigs)}
    </div>

    return (
        <Suspense fallback={<Loading/>}>
            {adaptationType && adaptationType === 'none' ? canvasDom :
                <ScreenFit width={width!} height={height!} mode={adaptationType}
                           scaleChange={(xScale, yScale) => {
                               ScaleAction.doScale(xScale, yScale)
                           }}>{canvasDom}</ScreenFit>}
            <GlobalVariableDebugger/>
        </Suspense>
    );
});

export default DesignerView;
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

@font-face {
    font-family: "DingTalk JinBuTi";
    src: url('./DingTalk JinBuTi.ttf');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "DouyinSansBold";
    src: url('./DouyinSansBold.ttf');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "优设标题黑";
    src: url('./优设标题黑.ttf');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "庞门正道标题体免费版";
    src: url('./庞门正道标题体免费版.ttf');
    font-weight: normal;
    font-style: normal;
}
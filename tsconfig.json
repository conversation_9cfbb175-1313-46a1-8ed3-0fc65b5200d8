{
  "compilerOptions": {
    "target": "es2015",
    "useDefineForClassFields": true,
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noUnusedLocals": true,
    "noUnusedParameters": false,
    "noEmitOnError": true,
    "noFallthroughCasesInSwitch": true,
    "allowSyntheticDefaultImports": true,
    "noEmit": true,
    "jsx": "react-jsx"
  },
  "include": [
    "src",
    "node_modules/.pnpm/@amap+amap-jsapi-loader@1.0.1/node_modules/@amap/amap-jsapi-loader/src/global.d.ts",
  ],
  "exclude": [
    "node_modules"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}

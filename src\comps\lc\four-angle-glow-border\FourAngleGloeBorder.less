/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.four-angle-glow {
  --fagb-color: #00ebff;
  --fagb-width: 2px;
  --fagb-length: 8px;
  --fagb-radius: 3px;
  width: 100%;
  height: 100%;
  position: relative;

  .angle {
    position: absolute;
    border: var(--fagb-width) solid var(--fagb-color);
    width: var(--fagb-length);
    height: var(--fagb-length);
  }

  .angle-tl {
    margin: -1px 0 0 -1px;
    border-right: 0;
    border-bottom: 0;
    border-top-left-radius: var(--fagb-radius);
  }

  .angle-tr {
    top: 0;
    right: 0;
    border-left: 0;
    border-bottom: 0;
    margin: -1px -1px 0 0;
    border-top-right-radius: var(--fagb-radius);
  }

  .angle-bl {
    bottom: 0;
    left: 0;
    border-top: 0;
    border-right: 0;
    margin: 0 0 -1px -1px;
    border-bottom-left-radius: var(--fagb-radius);
  }

  .angle-br {
    bottom: 0;
    right: 0;
    border-left: 0;
    border-top: 0;
    margin: 0 -1px -1px 0;
    border-bottom-right-radius: var(--fagb-radius);
  }
}




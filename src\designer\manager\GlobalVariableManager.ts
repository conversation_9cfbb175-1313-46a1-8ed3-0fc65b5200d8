/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractManager from "./core/AbstractManager.ts";
import {action, makeObservable, observable} from "mobx";
import {APIConfig, IDatabase} from "../DesignerType.ts";
import FetchUtil from "../../utils/FetchUtil.ts";
import Base64Util from "../../utils/Base64Util.ts";
import GlobalVariableParser from "../../utils/GlobalVariableParser.ts";
import layerManager from "./LayerManager.ts";

/**
 * 全局变量定义接口
 */
export interface GlobalVariableDefinition {
    id: string;
    name: string;
    description?: string;
    sourceType: 'static' | 'api' | 'database';
    sourceConfig: {
        staticData?: any;
        apiData?: APIConfig;
        database?: IDatabase;
    };
    filterFunctionString?: string;
    isActiveRendering?: boolean;
    initialOrFallbackValue?: any; // 仅当数据来源为 API 或数据库时有意义
}

/**
 * 全局变量管理器数据类型
 */
export interface GlobalVariableManagerDataType {
    definitions: GlobalVariableDefinition[];
}

/**
 * 全局变量管理器
 * 负责全局变量的定义、存储、获取和更新
 */
class GlobalVariableManager extends AbstractManager<GlobalVariableManagerDataType> {
    constructor() {
        super();
        makeObservable(this, {
            definitions: observable,
            values: observable,
            isLoading: observable,
            errorStates: observable,
            referenceCounts: observable,
            editingDefinition: observable,
            isPanelVisible: observable,
            addDefinition: action,
            updateDefinition: action,
            deleteDefinition: action,
            setEditingDefinition: action,
            setPanelVisible: action,
            _setVariableValueInternal: action,
            setVariableValue: action,
            incrementReferenceCount: action,
            decrementReferenceCount: action,
            recalculateAllReferenceCounts: action,
            init: action,
        });
    }

    /**
     * 全局变量定义存储
     */
    definitions: Map<string, GlobalVariableDefinition> = new Map();

    /**
     * 全局变量当前值存储
     */
    values: Map<string, any> = new Map();

    /**
     * 加载状态存储
     */
    isLoading: Map<string, boolean> = new Map();

    /**
     * 错误状态存储
     */
    errorStates: Map<string, string | null> = new Map();

    /**
     * 引用计数存储
     */
    referenceCounts: Map<string, number> = new Map();

    /**
     * 当前编辑的全局变量定义（用于配置面板）
     */
    editingDefinition: GlobalVariableDefinition | null = null;

    /**
     * 配置面板可见性
     */
    isPanelVisible: boolean = false;

    /**
     * 订阅者存储 (variableId -> subscriberId -> callback)
     */
    private subscribers: Map<string, Map<string, (newValue: any) => void>> = new Map();



    /**
     * 添加全局变量定义
     */
    addDefinition = async (definition: GlobalVariableDefinition): Promise<boolean> => {
        // 检查名称唯一性
        for (const [, existingDef] of this.definitions) {
            if (existingDef.name === definition.name) {
                return false; // 名称重复
            }
        }

        this.definitions.set(definition.id, definition);
        // 异步初始化变量值
        await this.refreshVariableValue(definition.id);

        // 重新计算引用计数（新变量可能在过滤器中被引用）
        this.recalculateAllReferenceCounts();

        return true;
    };

    /**
     * 更新全局变量定义
     */
    updateDefinition = async (definition: GlobalVariableDefinition): Promise<boolean> => {
        // 检查名称唯一性（排除自己）
        for (const [id, existingDef] of this.definitions) {
            if (id !== definition.id && existingDef.name === definition.name) {
                return false; // 名称重复
            }
        }

        this.definitions.set(definition.id, definition);
        // 异步刷新变量值
        await this.refreshVariableValue(definition.id);

        // 重新计算引用计数（因为变量名可能发生变化，影响间接引用）
        this.recalculateAllReferenceCounts();

        return true;
    };

    /**
     * 删除全局变量定义
     */
    deleteDefinition = (variableId: string): void => {
        this.definitions.delete(variableId);
        this.values.delete(variableId);
        this.isLoading.delete(variableId);
        this.errorStates.delete(variableId);
        this.referenceCounts.delete(variableId);

        // 清理订阅者
        this.subscribers.delete(variableId);

        // 重新计算引用计数（删除变量后需要更新其他变量的引用计数）
        this.recalculateAllReferenceCounts();
    };

    /**
     * 获取全局变量值（通过ID）
     */
    getVariableValue = (variableId: string): any => {
        return this.values.get(variableId);
    };

    /**
     * 获取全局变量值（通过名称）
     */
    getVariableValueByName = (variableName: string): any => {
        // 根据名称查找变量ID
        for (const [id, definition] of this.definitions) {
            if (definition.name === variableName) {
                return this.values.get(id);
            }
        }
        return undefined;
    };

    /**
     * 获取全局变量定义
     */
    getVariableDefinition = (variableId: string): GlobalVariableDefinition | undefined => {
        return this.definitions.get(variableId);
    };

    /**
     * 获取所有全局变量定义
     */
    getAllVariableDefinitions = (): GlobalVariableDefinition[] => {
        return Array.from(this.definitions.values());
    };

    /**
     * 设置编辑中的定义
     */
    setEditingDefinition = (definition: GlobalVariableDefinition | null): void => {
        this.editingDefinition = definition;
    };

    /**
     * 设置配置面板可见性
     */
    setPanelVisible = (visible: boolean): void => {
        this.isPanelVisible = visible;
    };

    /**
     * 内部方法：设置变量值
     */
    _setVariableValueInternal = (variableId: string, newValue: any, definition?: GlobalVariableDefinition): void => {
        const oldValue = this.values.get(variableId);
        this.values.set(variableId, newValue);

        // 如果值发生变化且开启了主动渲染，通知订阅者
        if (oldValue !== newValue) {
            const def = definition || this.definitions.get(variableId);
            if (def?.isActiveRendering) {
                this._notifySubscribers(variableId, newValue);
            }
        }
    };

    /**
     * 内部方法：解析并执行过滤器
     */
    _parseAndExecuteFilter = async (filterString: string | undefined, data: any, variableId: string, callStack?: Set<string>): Promise<any> => {
        if (!filterString || filterString.trim() === '') {
            return data;
        }

        try {
            // 创建全局变量访问对象，支持循环依赖检测
            const globalVars = {
                get: (name: string) => {
                    // 检查是否会导致循环依赖
                    if (callStack && callStack.has(name)) {
                        console.warn(`过滤器中检测到循环依赖: ${Array.from(callStack).join(' -> ')} -> ${name}`);
                        return null; // 返回null避免循环
                    }
                    return this.getVariableValueByName(name);
                }
            };

            // 检查过滤器函数格式
            let filterCode = filterString.trim();

            // 如果是完整的函数定义，提取函数体
            if (filterCode.startsWith('function')) {
                const match = filterCode.match(/function\s+\w*\s*\([^)]*\)\s*\{([\s\S]*)\}/);
                if (match) {
                    filterCode = match[1];
                }
            }

            // 首先解析和替换过滤器代码中的 ${GV::variableName} 引用
            const GlobalVariableParser = (await import('../../utils/GlobalVariableParser')).default;
            const parsedFilterCode = await GlobalVariableParser.parseAndReplace(filterCode, this, callStack, 'javascript');

            // 执行过滤器函数
            const func = new Function('data', 'globalVars', parsedFilterCode);
            const result = func(data, globalVars);

            // 验证返回值
            if (result === undefined) {
                console.warn(`全局变量 ${variableId} 过滤器返回了 undefined，将使用原始数据`);
                return data;
            }

            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            console.error(`全局变量 ${variableId} 过滤器执行错误:`, errorMessage);

            // 记录详细的错误信息
            this.errorStates.set(variableId, `过滤器执行错误: ${errorMessage}`);

            return data; // 过滤器执行失败时返回原始数据
        }
    };

    /**
     * 内部方法：获取变量的原始数据
     */
    _fetchDataForVariable = async (definition: GlobalVariableDefinition, callStack?: Set<string>): Promise<any> => {
        switch (definition.sourceType) {
            case 'static':
                return definition.sourceConfig.staticData;

            case 'api': {
                if (!definition.sourceConfig.apiData) {
                    throw new Error('API配置不存在');
                }
                const apiConfig = definition.sourceConfig.apiData;
                if (!apiConfig.url) {
                    throw new Error('API URL不能为空');
                }

                try {
                    // 解析URL中的全局变量引用，传递调用栈以检测循环依赖，使用文本上下文
                    const url = await GlobalVariableParser.parseAndReplace(apiConfig.url, this, callStack, 'text');
                    const method = apiConfig.method || 'get';

                    // 解析请求头中的全局变量引用
                    const headers = await GlobalVariableParser.parseObjectAndReplace(apiConfig.header || {}, this, callStack, 'text');

                    // 解析请求参数中的全局变量引用
                    const params = await GlobalVariableParser.parseObjectAndReplace(apiConfig.params || {}, this, callStack, 'text');

                    const result = await FetchUtil.doRequestNativeResult(url, method, headers, params);
                    if (result === null) {
                        throw new Error('API请求失败：返回结果为空');
                    }
                    return result;
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : '未知错误';
                    throw new Error(`API请求失败: ${errorMessage}`);
                }
            }

            case 'database': {
                if (!definition.sourceConfig.database) {
                    throw new Error('数据库配置不存在');
                }
                const dbConfig = definition.sourceConfig.database;
                if (!dbConfig.sql || dbConfig.sql.trim() === '') {
                    throw new Error('SQL语句不能为空');
                }
                if (!dbConfig.targetDb) {
                    throw new Error('目标数据库不能为空');
                }

                try {
                    const response = await FetchUtil.post('/api/db/executor/execute', {
                        id: dbConfig.targetDb,
                        sql: Base64Util.toBase64(dbConfig.sql)
                    });

                    if (response.code !== 200) {
                        throw new Error(response.msg || '数据库查询失败');
                    }

                    if (response.data === null || response.data === undefined) {
                        throw new Error('数据库查询返回结果为空');
                    }

                    return response.data;
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : '未知错误';
                    throw new Error(`数据库查询失败: ${errorMessage}`);
                }
            }

            default:
                throw new Error(`不支持的数据源类型: ${definition.sourceType}`);
        }
    };

    /**
     * 调用栈跟踪，用于检测循环依赖
     */
    private _callStack: Set<string> = new Set();

    /**
     * 刷新全局变量值
     */
    refreshVariableValue = async (variableId: string, _triggerType?: string, optionalNewValue?: any, callStack?: Set<string>): Promise<void> => {
        const definition = this.definitions.get(variableId);
        if (!definition) {
            console.warn(`尝试刷新不存在的全局变量: ${variableId}`);
            return;
        }

        // 使用传入的调用栈或创建新的调用栈
        const currentCallStack = callStack || new Set();

        // 检测循环依赖
        if (currentCallStack.has(variableId)) {
            const stackArray = Array.from(currentCallStack);
            const errorMessage = `检测到循环依赖: ${stackArray.join(' -> ')} -> ${variableId}`;
            console.error(errorMessage);
            this.errorStates.set(variableId, errorMessage);

            // 使用初始值/错误默认值作为降级处理
            if (definition.initialOrFallbackValue !== undefined) {
                try {
                    const filteredData = await this._parseAndExecuteFilter(
                        definition.filterFunctionString,
                        definition.initialOrFallbackValue,
                        variableId,
                        new Set() // 使用新的调用栈避免循环
                    );
                    this._setVariableValueInternal(variableId, filteredData, definition);
                } catch (filterError) {
                    console.error(`全局变量 ${variableId} 循环依赖降级处理失败:`, filterError);
                    this._setVariableValueInternal(variableId, definition.initialOrFallbackValue, definition);
                }
            } else {
                this._setVariableValueInternal(variableId, null, definition);
            }
            return;
        }

        // 将当前变量添加到调用栈
        const newCallStack = new Set(currentCallStack);
        newCallStack.add(variableId);

        // 设置加载状态
        this.isLoading.set(variableId, true);
        this.errorStates.set(variableId, null);

        let rawData: any;

        try {
            // 如果提供了可选的新值，直接使用
            if (optionalNewValue !== undefined) {
                rawData = optionalNewValue;
            } else {
                // 异步获取数据
                rawData = await this._fetchDataForVariable(definition, newCallStack);
            }

            // 执行过滤器
            const filteredData = await this._parseAndExecuteFilter(definition.filterFunctionString, rawData, variableId, newCallStack);

            // 设置变量值
            this._setVariableValueInternal(variableId, filteredData, definition);

        } catch (error) {
            // 数据获取失败，使用初始值/错误默认值
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            console.error(`全局变量 ${variableId} 刷新失败:`, errorMessage);
            this.errorStates.set(variableId, errorMessage);

            if (definition.initialOrFallbackValue !== undefined) {
                try {
                    // 使用初始值/错误默认值，并通过过滤器处理
                    const filteredData = await this._parseAndExecuteFilter(
                        definition.filterFunctionString,
                        definition.initialOrFallbackValue,
                        variableId,
                        new Set() // 使用新的调用栈避免循环
                    );
                    this._setVariableValueInternal(variableId, filteredData, definition);
                } catch (filterError) {
                    console.error(`全局变量 ${variableId} 初始值过滤器处理失败:`, filterError);
                    this._setVariableValueInternal(variableId, definition.initialOrFallbackValue, definition);
                }
            } else {
                // 没有配置初始值，设置为null
                this._setVariableValueInternal(variableId, null, definition);
            }
        } finally {
            // 清除加载状态
            this.isLoading.set(variableId, false);
        }
    };

    /**
     * 内部方法：通知订阅者
     */
    private _notifySubscribers = (variableId: string, newValue: any): void => {
        // 1. 通知直接订阅者
        const variableSubscribers = this.subscribers.get(variableId);
        if (variableSubscribers) {
            variableSubscribers.forEach((callback) => {
                try {
                    callback(newValue);
                } catch (error) {
                    console.error(`全局变量 ${variableId} 订阅者回调执行错误:`, error);
                }
            });
        }

        // 2. 通知间接依赖的组件
        this._notifyIndirectDependencies(variableId);
    };

    /**
     * 通知间接依赖的组件和全局变量
     */
    private _notifyIndirectDependencies = (variableId: string): void => {
        try {
            const variableDefinition = this.definitions.get(variableId);
            if (!variableDefinition) return;

            const variableName = variableDefinition.name;

            // 1. 检查其他全局变量是否依赖此变量
            for (const [otherVariableId, otherDefinition] of this.definitions) {
                if (otherVariableId === variableId) continue; // 跳过自己

                let needsRefresh = false;

                // 检查过滤器中的引用
                if (otherDefinition.filterFunctionString) {
                    if (this._textContainsVariableReference(otherDefinition.filterFunctionString, variableName)) {
                        needsRefresh = true;
                    }
                }

                // 检查API配置中的引用
                if (otherDefinition.sourceType === 'api' && otherDefinition.sourceConfig?.apiData) {
                    const { url, header, params } = otherDefinition.sourceConfig.apiData;
                    if ((url && this._textContainsVariableReference(url, variableName)) ||
                        this._objectContainsVariableReference(header, variableName) ||
                        this._objectContainsVariableReference(params, variableName)) {
                        needsRefresh = true;
                    }
                }

                // 检查数据库配置中的引用
                if (otherDefinition.sourceType === 'database' && otherDefinition.sourceConfig?.database) {
                    const { sql } = otherDefinition.sourceConfig.database;
                    if (sql && this._textContainsVariableReference(sql, variableName)) {
                        needsRefresh = true;
                    }
                }

                // 如果需要刷新，异步刷新该全局变量
                if (needsRefresh) {
                    console.log(`全局变量 ${otherDefinition.name} 依赖 ${variableName}，触发刷新`);
                    // 异步刷新，避免阻塞当前操作
                    setTimeout(() => {
                        this.refreshVariableValue(otherVariableId);
                    }, 0);
                }
            }

            // 2. 检查组件是否间接依赖此变量
            if (!layerManager || !layerManager.compController) return;

            // 遍历所有组件控制器
            Object.values(layerManager.compController).forEach((controller: any) => {
                const config = controller.getConfig();
                if (!config?.data) return;

                let needsRefresh = false;

                // 检查API配置中的间接引用
                if (config.data.sourceType === 'api' && config.data.apiData) {
                    const { url, filter, header, params } = config.data.apiData;
                    if (this._textContainsVariableReference(url, variableName) ||
                        this._textContainsVariableReference(filter, variableName) ||
                        this._objectContainsVariableReference(header, variableName) ||
                        this._objectContainsVariableReference(params, variableName)) {
                        needsRefresh = true;
                    }
                }

                // 检查数据库配置中的间接引用
                if (config.data.sourceType === 'database' && config.data.database) {
                    const { filter } = config.data.database;
                    if (this._textContainsVariableReference(filter, variableName)) {
                        needsRefresh = true;
                    }
                }

                // 检查静态数据过滤器中的间接引用
                if (config.data.sourceType === 'static' && config.data.staticDataFilter) {
                    if (this._textContainsVariableReference(config.data.staticDataFilter, variableName)) {
                        // 静态数据源使用专门的刷新方法
                        if (typeof controller.refreshStaticData === 'function') {
                            try {
                                controller.refreshStaticData();
                            } catch (error) {
                                console.error(`组件 ${config.base?.id} 静态数据刷新失败:`, error);
                            }
                        }
                        return; // 静态数据源已处理，跳过通用处理
                    }
                }

                // 检查全局变量过滤器中的间接引用
                if (config.data.sourceType === 'globalVariable' && config.data.globalVariableFilter) {
                    if (this._textContainsVariableReference(config.data.globalVariableFilter, variableName)) {
                        needsRefresh = true;
                    }
                }

                // 如果需要刷新，调用组件的数据加载方法
                if (needsRefresh && typeof controller.loadComponentData === 'function') {
                    try {
                        controller.loadComponentData();
                    } catch (error) {
                        console.error(`组件 ${config.base?.id} 间接依赖刷新失败:`, error);
                    }
                }
            });
        } catch (error) {
            console.warn('通知间接依赖时出错:', error);
        }
    };

    /**
     * 检查文本中是否包含对指定变量的引用
     */
    private _textContainsVariableReference = (text: string, variableName: string): boolean => {
        if (!text || typeof text !== 'string' || !variableName) return false;

        // 检查 ${GV::variableName} 格式的引用
        const gvRegex = new RegExp(`\\$\\{GV::${this._escapeRegExp(variableName)}\\}`, 'g');
        if (gvRegex.test(text)) return true;

        // 检查 globalVars.get('variableName') 或 globalVars.get("variableName") 格式的引用
        const globalVarsRegex = new RegExp(`globalVars\\.get\\(['"]${this._escapeRegExp(variableName)}['"]\\)`, 'g');
        if (globalVarsRegex.test(text)) return true;

        return false;
    };

    /**
     * 检查对象中是否包含对指定变量的引用
     */
    private _objectContainsVariableReference = (obj: any, variableName: string): boolean => {
        if (obj === null || obj === undefined || !variableName) {
            return false;
        }

        if (typeof obj === 'string') {
            // 字符串直接检查
            return this._textContainsVariableReference(obj, variableName);
        }

        if (Array.isArray(obj)) {
            // 数组递归检查每个元素
            return obj.some(item => this._objectContainsVariableReference(item, variableName));
        }

        if (typeof obj === 'object') {
            // 对象递归检查每个属性
            return Object.entries(obj).some(([key, value]) => {
                // 检查键名和值
                return this._textContainsVariableReference(key, variableName) ||
                       this._objectContainsVariableReference(value, variableName);
            });
        }

        // 其他类型（数字、布尔值等）不包含变量引用
        return false;
    };



    /**
     * 转义正则表达式特殊字符
     */
    private _escapeRegExp = (string: string): string => {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    };

    /**
     * 订阅全局变量变化
     */
    subscribe = (variableId: string, subscriberId: string, callback: (newValue: any) => void): void => {
        if (!this.subscribers.has(variableId)) {
            this.subscribers.set(variableId, new Map());
        }
        this.subscribers.get(variableId)!.set(subscriberId, callback);
    };

    /**
     * 取消订阅全局变量变化
     */
    unsubscribe = (variableId: string, subscriberId: string): void => {
        const variableSubscribers = this.subscribers.get(variableId);
        if (variableSubscribers) {
            variableSubscribers.delete(subscriberId);
            if (variableSubscribers.size === 0) {
                this.subscribers.delete(variableId);
            }
        }
    };

    /**
     * 设置全局变量值（主要由蓝图节点调用）
     */
    setVariableValue = (variableId: string, newValue: any, fromBlueprint: boolean = false): void => {
        const definition = this.definitions.get(variableId);
        if (!definition) {
            console.warn(`尝试设置不存在的全局变量: ${variableId}`);
            return;
        }

        // 直接设置值，不经过数据获取和过滤器处理
        this._setVariableValueInternal(variableId, newValue, definition);

        // 如果来自蓝图，需要触发蓝图节点的输出
        if (fromBlueprint) {
            // 动态导入蓝图管理器以避免循环依赖
            import('../blueprint/manager/BluePrintManager.ts').then(module => {
                const bluePrintManager = module.default;
                bluePrintManager.triggerGlobalVariableNodeOutput(variableId, newValue);
            });
        }
    };

    /**
     * 增加引用计数
     */
    incrementReferenceCount = (variableId: string): void => {
        const currentCount = this.referenceCounts.get(variableId) || 0;
        this.referenceCounts.set(variableId, currentCount + 1);
    };

    /**
     * 减少引用计数
     */
    decrementReferenceCount = (variableId: string): void => {
        const currentCount = this.referenceCounts.get(variableId) || 0;
        if (currentCount > 0) {
            this.referenceCounts.set(variableId, currentCount - 1);
        }
    };

    /**
     * 重新计算所有引用计数
     */
    recalculateAllReferenceCounts = (): void => {
        // 清空现有引用计数
        this.referenceCounts.clear();

        try {
            // 使用全局对象避免循环依赖
            const bluePrintManager = (window as any).bluePrintManager;

            // 1. 检查全局变量自身配置中的引用
            for (const [, definition] of this.definitions) {
                // 检查过滤器中的引用
                if (definition.filterFunctionString) {
                    this._extractVariableReferences(definition.filterFunctionString);
                }

                // 检查API配置中的引用
                if (definition.sourceType === 'api' && definition.sourceConfig?.apiData) {
                    const { url, header, params } = definition.sourceConfig.apiData;
                    if (url) this._extractVariableReferences(url);
                    this._extractVariableReferencesFromObject(header);
                    this._extractVariableReferencesFromObject(params);
                }

                // 检查数据库配置中的引用（SQL语句可能包含全局变量引用）
                if (definition.sourceType === 'database' && definition.sourceConfig?.database) {
                    const { sql } = definition.sourceConfig.database;
                    if (sql) this._extractVariableReferences(sql);
                }
            }

            // 2. 遍历所有组件控制器，检查直接引用
            if (layerManager && layerManager.compController) {
                Object.values(layerManager.compController).forEach((controller: any) => {
                    const config = controller.getConfig();
                    if (config?.data) {
                        // 检查直接引用（组件数据源为全局变量）
                        if (config.data.sourceType === 'globalVariable' && config.data.selectedGlobalVariableId) {
                            this.incrementReferenceCount(config.data.selectedGlobalVariableId);
                        }

                        // 检查间接引用（API URL和过滤器中的引用）
                        this._checkIndirectReferences(config.data);
                    }

                    // 检查样式配置中的引用（特别是G2Plot自定义组件）
                    if (config?.style) {
                        this._checkStyleReferences(config.style);
                    }

                    // 检查自定义组件的customCode中的引用
                    if (config?.customCode?.code) {
                        this._extractVariableReferences(config.customCode.code);
                    }
                });
            }

            // 3. 遍历蓝图节点，检查全局变量节点
            if (bluePrintManager && bluePrintManager.bpNodeLayoutMap) {
                Object.values(bluePrintManager.bpNodeLayoutMap).forEach((node: any) => {
                    if (node.type === 'global-variable-node') {
                        this.incrementReferenceCount(node.id);
                    }
                    // TODO: 检查其他节点中可能的全局变量引用
                });
            }
        } catch (error) {
            console.warn('重新计算引用计数时出错:', error);
        }
    };

    /**
     * 检查数据配置中的间接引用
     */
    private _checkIndirectReferences = (dataConfig: any): void => {
        if (!dataConfig) return;

        // 检查API配置中的引用
        if (dataConfig.apiData) {
            const { url, filter, header, params } = dataConfig.apiData;
            this._extractVariableReferences(url);
            this._extractVariableReferences(filter);
            // 检查请求头和参数中的全局变量引用
            this._extractVariableReferencesFromObject(header);
            this._extractVariableReferencesFromObject(params);
        }

        // 检查数据库配置中的引用
        if (dataConfig.database) {
            const { filter } = dataConfig.database;
            this._extractVariableReferences(filter);
        }

        // 检查静态数据中的引用（优先检查 rawStaticData）
        const staticDataToCheck = dataConfig.rawStaticData !== undefined ? dataConfig.rawStaticData : dataConfig.staticData;
        if (staticDataToCheck) {
            this._extractVariableReferencesFromObject(staticDataToCheck);
        }

        // 检查静态数据过滤器中的引用
        if (dataConfig.staticDataFilter) {
            this._extractVariableReferences(dataConfig.staticDataFilter);
        }

        // 检查全局变量过滤器中的引用
        if (dataConfig.globalVariableFilter) {
            this._extractVariableReferences(dataConfig.globalVariableFilter);
        }
    };

    /**
     * 检查组件样式配置中的间接引用（特别是G2Plot和Echarts自定义组件）
     */
    private _checkStyleReferences = (styleConfig: any): void => {
        if (!styleConfig) return;

        // 检查自定义组件的customCode中的引用（G2Plot和Echarts）
        if (styleConfig.customCode) {
            this._extractVariableReferences(styleConfig.customCode);
        }

        // 可以在这里添加其他样式配置的检查
        // 例如：主题配置、颜色配置等
    };

    /**
     * 从文本中提取全局变量引用并增加引用计数
     */
    private _extractVariableReferences = (text: string): void => {
        if (!text || typeof text !== 'string') return;

        // 匹配 ${GV::variableName} 格式的引用
        const gvRegex = /\$\{GV::([^}]+)\}/g;
        let match;
        while ((match = gvRegex.exec(text)) !== null) {
            const variableName = match[1];
            // 根据名称查找变量ID
            for (const [id, definition] of this.definitions) {
                if (definition.name === variableName) {
                    this.incrementReferenceCount(id);
                    break;
                }
            }
        }

        // 匹配 globalVars.get('variableName') 或 globalVars.get("variableName") 格式的引用
        const globalVarsRegex = /globalVars\.get\(['"]([^'"]+)['"]\)/g;
        while ((match = globalVarsRegex.exec(text)) !== null) {
            const variableName = match[1];
            // 根据名称查找变量ID
            for (const [id, definition] of this.definitions) {
                if (definition.name === variableName) {
                    this.incrementReferenceCount(id);
                    break;
                }
            }
        }
    };

    /**
     * 从对象中递归提取全局变量引用并增加引用计数
     */
    private _extractVariableReferencesFromObject = (obj: any): void => {
        if (obj === null || obj === undefined) {
            return;
        }

        if (typeof obj === 'string') {
            // 字符串直接提取引用
            this._extractVariableReferences(obj);
        } else if (Array.isArray(obj)) {
            // 数组递归处理每个元素
            obj.forEach(item => this._extractVariableReferencesFromObject(item));
        } else if (typeof obj === 'object') {
            // 对象递归处理每个属性
            Object.entries(obj).forEach(([key, value]) => {
                // 键名也可能包含全局变量引用
                this._extractVariableReferences(key);
                this._extractVariableReferencesFromObject(value);
            });
        }
        // 其他类型（数字、布尔值等）不处理
    };



    /**
     * 初始化管理器
     */
    init = async (data?: GlobalVariableManagerDataType): Promise<void> => {
        if (data?.definitions) {
            // 清空现有数据
            this.definitions.clear();
            this.values.clear();
            this.isLoading.clear();
            this.errorStates.clear();
            this.referenceCounts.clear();

            // 先加载所有定义，不初始化值
            data.definitions.forEach(definition => {
                this.definitions.set(definition.id, definition);
            });

            // 按依赖关系排序初始化
            const sortedDefinitions = this._sortDefinitionsByDependency(data.definitions);

            // 按顺序初始化变量值
            for (const definition of sortedDefinitions) {
                try {
                    await this.refreshVariableValue(definition.id);
                } catch (error) {
                    console.error(`初始化全局变量 ${definition.name} 失败:`, error);
                }
            }

            // 注意：引用计数的计算现在由 DesignerManager 统一处理
            // 确保在所有管理器和组件控制器都初始化完成后再计算
        }
    };

    /**
     * 按依赖关系对全局变量定义进行排序
     * 被依赖的变量优先初始化
     */
    private _sortDefinitionsByDependency = (definitions: any[]): any[] => {
        const sorted: any[] = [];
        const visited = new Set<string>();
        const visiting = new Set<string>();

        const visit = (definition: any) => {
            if (visiting.has(definition.id)) {
                // 检测到循环依赖，跳过
                console.warn(`检测到循环依赖，涉及变量: ${definition.name}`);
                return;
            }

            if (visited.has(definition.id)) {
                return;
            }

            visiting.add(definition.id);

            // 找到当前变量依赖的其他变量
            const dependencies = this._extractDependencies(definition);

            // 先访问依赖的变量
            for (const depName of dependencies) {
                const depDefinition = definitions.find(def => def.name === depName);
                if (depDefinition && !visited.has(depDefinition.id)) {
                    visit(depDefinition);
                }
            }

            visiting.delete(definition.id);
            visited.add(definition.id);
            sorted.push(definition);
        };

        // 访问所有定义
        for (const definition of definitions) {
            if (!visited.has(definition.id)) {
                visit(definition);
            }
        }

        return sorted;
    };

    /**
     * 提取变量定义中依赖的其他变量名
     */
    private _extractDependencies = (definition: any): string[] => {
        const dependencies: string[] = [];

        // 检查过滤器中的依赖
        if (definition.filterFunctionString) {
            dependencies.push(...this._extractVariableNamesFromText(definition.filterFunctionString));
        }

        // 检查API配置中的依赖
        if (definition.sourceType === 'api' && definition.sourceConfig?.apiData) {
            const { url, header, params } = definition.sourceConfig.apiData;
            if (url) dependencies.push(...this._extractVariableNamesFromText(url));
            dependencies.push(...this._extractVariableNamesFromObject(header));
            dependencies.push(...this._extractVariableNamesFromObject(params));
        }

        // 检查数据库配置中的依赖
        if (definition.sourceType === 'database' && definition.sourceConfig?.database) {
            const { sql } = definition.sourceConfig.database;
            if (sql) dependencies.push(...this._extractVariableNamesFromText(sql));
        }

        return [...new Set(dependencies)]; // 去重
    };

    /**
     * 从文本中提取变量名（不增加引用计数）
     */
    private _extractVariableNamesFromText = (text: string): string[] => {
        if (!text || typeof text !== 'string') return [];

        const variableNames: string[] = [];

        // 匹配 ${GV::variableName} 格式
        const gvRegex = /\$\{GV::([^}]+)\}/g;
        let match;
        while ((match = gvRegex.exec(text)) !== null) {
            variableNames.push(match[1]);
        }

        // 匹配 globalVars.get('variableName') 格式
        const globalVarsRegex = /globalVars\.get\(['"]([^'"]+)['"]\)/g;
        while ((match = globalVarsRegex.exec(text)) !== null) {
            variableNames.push(match[1]);
        }

        return variableNames;
    };

    /**
     * 从对象中提取变量名（不增加引用计数）
     */
    private _extractVariableNamesFromObject = (obj: any): string[] => {
        if (obj === null || obj === undefined) return [];

        if (typeof obj === 'string') {
            return this._extractVariableNamesFromText(obj);
        }

        if (Array.isArray(obj)) {
            const names: string[] = [];
            obj.forEach(item => {
                names.push(...this._extractVariableNamesFromObject(item));
            });
            return names;
        }

        if (typeof obj === 'object') {
            const names: string[] = [];
            Object.entries(obj).forEach(([key, value]) => {
                names.push(...this._extractVariableNamesFromText(key));
                names.push(...this._extractVariableNamesFromObject(value));
            });
            return names;
        }

        return [];
    };

    /**
     * 获取管理器数据用于持久化
     */
    getData = (): GlobalVariableManagerDataType => {
        return {
            definitions: Array.from(this.definitions.values())
        };
    };





    /**
     * 调试方法：打印所有全局变量定义
     */
    debugPrintDefinitions = (): void => {
        console.log('=== 全局变量定义调试信息 ===');
        console.log(`总共有 ${this.definitions.size} 个全局变量`);
        for (const [id, definition] of this.definitions) {
            console.log(`ID: ${id}`);
            console.log(`Name: ${definition.name}`);
            console.log(`SourceType: ${definition.sourceType}`);
            console.log(`Filter: ${definition.filterFunctionString}`);
            console.log(`Value: ${JSON.stringify(this.values.get(id))}`);
            console.log(`ReferenceCount: ${this.referenceCounts.get(id) || 0}`);
            console.log('---');
        }
        console.log('=== 调试信息结束 ===');
    };

    /**
     * 销毁管理器
     */
    destroy = (): void => {
        // 清空所有数据
        this.definitions.clear();
        this.values.clear();
        this.isLoading.clear();
        this.errorStates.clear();
        this.referenceCounts.clear();
        this.subscribers.clear();
    };
}

// 创建全局实例
const globalVariableManager = new GlobalVariableManager();

// 在开发环境下暴露到window对象，方便调试
if (typeof window !== 'undefined') {
    (window as any).globalVariableManager = globalVariableManager;
}

export default globalVariableManager;

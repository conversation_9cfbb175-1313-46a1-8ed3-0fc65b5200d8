/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

@import "../../../designer/style/DesignerStyle.less";

.lc-dialog {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 999;
  background: rgba(0, 0, 0, 0.64);
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-radius: 3px;

  .dialog-body {
    .dialog-header {
      background: #26292e;
      height: 35px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: @lc-config-sub-title;
      padding: 0 10px;
    }

    .dialog-content {
      background: #1b1e23;
      padding: 5px;
    }
  }
}
.global-variable-list {
    width: 250px;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #1f1f1f;
    border-right: 1px solid #404040;

    .gv-list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 13px 10px;
        font-size: 12px;
        height: 47px;
        align-items: center;
        border-bottom: 1px solid #404040;
        color: #bababa;

        .gv-list-header-title {
            font-size: 12px;
            font-weight: 500;
            color: #bababa;
        }

        .gv-list-header-operate {
            display: flex;
            align-items: center;
            color: #9c9c9cd6;
            cursor: pointer;
            transition: color 0.3s;

            &:hover {
                color: #ebebeb;
            }
        }
    }

    .gv-list-toolbar {
        padding: 12px 16px;
        border-bottom: 1px solid #404040;
    }

    .gv-list-content {
        flex: 1;
        overflow-y: auto;
        padding: 8px;

        .gv-list-empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            text-align: center;
            color: #8c8c8c;
        }

        .gv-list-items {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .gv-list-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 12px;
            border: 1px solid #404040;
            border-radius: 4px;
            background: #2a2a2a;
            transition: all 0.2s ease;
            cursor: pointer;

            &:hover {
                border-color: #0080ff;
                background: #333333;
            }

            .gv-item-content {
                flex: 1;
                display: flex;
                align-items: center;
                gap: 8px;
                min-width: 0; // 防止文字溢出

                .gv-item-name {
                    font-size: 13px;
                    font-weight: 400;
                    color: #e3e3e3;
                    line-height: 1.4;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .gv-item-badge {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-width: 18px;
                    height: 18px;
                    border-radius: 9px;
                    background: #0080ff;
                    color: #ffffff;
                    font-size: 11px;
                    font-weight: 500;
                    line-height: 1;

                    &.zero {
                        background: #666666;
                        color: #aaaaaa;
                    }
                }
            }

            .gv-item-actions {
                display: flex;
                align-items: center;
                gap: 2px;
                margin-left: 8px;

                .gv-action-btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 24px;
                    height: 24px;
                    border-radius: 3px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    color: #8c8c8c;

                    &:hover {
                        background: rgba(255, 255, 255, 0.1);
                        color: #e3e3e3;
                    }

                    &.gv-edit-btn:hover {
                        background: rgba(0, 128, 255, 0.2);
                        color: #0080ff;
                    }

                    &.gv-delete-btn:hover {
                        background: rgba(255, 77, 79, 0.2);
                        color: #ff4d4f;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .global-variable-list {
        .gv-list-header {
            padding: 8px 12px;
        }

        .gv-list-toolbar {
            padding: 8px 12px;
        }

        .gv-list-content {
            padding: 4px;

            .gv-list-item {
                padding: 6px 8px;
                margin-bottom: 6px;
            }
        }
    }
}

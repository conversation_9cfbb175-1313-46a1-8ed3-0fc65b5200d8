/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {Component} from 'react';
import {AntdCartesianCoordinateSys} from "../config/AntdFragment";
import {BarOptions, ColorAttr} from "@antv/g2plot";
import AntdCommonBarController from "./AntdCommonBarController";
import {Control} from "../../../json-schema/SchemaTypes";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI";
import {AntdLegend} from "../config/legend/AntdLegend";
import {ConfigType} from "../../../designer/right/ConfigContent";
import {ISelectOption} from "../../../json-schema/ui/select/Select";
import ColorUtil from "../../../utils/ColorUtil";

class AntdBarCommonStyleConfig extends Component<ConfigType<AntdCommonBarController>> {

    barCoordinateSysChange = (config: BarOptions) => {
        const controller = this.props.controller as AntdCommonBarController;
        controller.update({style: config});
    }

    render() {
        const {controller} = this.props;
        const config = controller.getConfig()!.style!;
        return (
            <>
                <AntdBarGraphics controller={controller}/>
                <AntdLegend controller={controller}/>
                <AntdCartesianCoordinateSys onChange={this.barCoordinateSysChange} config={config}/>
            </>
        );
    }
}

export {AntdBarCommonStyleConfig};


export const AntdBarGraphics: React.FC<ConfigType<AntdCommonBarController>> = ({controller}) => {

    const config = controller.getConfig()!.style!;

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataFragment} = fieldChangeData;
        if (id === 'barColor') {
            if (data && Array.isArray(data)) {
                // 多色模式
                // 检查数组中是否有渐变对象
                const hasGradient = data.some(color =>
                    typeof color === 'object' && color !== null && 'type' in color &&
                    (color.type === 'linear-gradient' || color.type === 'radial-gradient')
                );

                if (hasGradient) {
                    // 如果数组中有渐变对象，我们需要将它们转换为渐变字符串
                    const processedColors = data.map(color => {
                        if (typeof color === 'object' && color !== null && 'type' in color &&
                            (color.type === 'linear-gradient' || color.type === 'radial-gradient')) {
                            return ColorUtil.gradientToCss(color);
                        }
                        return color;
                    });
                    controller.update({style: {color: processedColors as ColorAttr, barStyle: {fill: undefined}}});
                } else {
                    // 如果数组中没有渐变对象，直接使用原始数组
                    controller.update({style: {color: data as ColorAttr, barStyle: {fill: undefined}}});
                }
            } else if (data && typeof data === 'object' && data !== null && 'type' in data &&
                      (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {barStyle: {fill: gradientCss}}});
            } else if (data && typeof data === 'string' && data.indexOf('gradient') !== -1) {
                // 处理渐变字符串
                controller.update({style: {barStyle: {fill: data as string}}});
            } else {
                controller.update({style: {barStyle: {fill: data as string}}});
            }
        } else {
            controller.update(dataFragment);
        }
    }

    const schema: Control = {
        key: 'style',
        type: 'accordion',
        label: '图形',
        children: [
            {
                type: 'grid',
                children: [
                    {
                        key: 'maxBarWidth',
                        type: 'number-input',
                        label: '宽度',
                        value: config?.maxBarWidth,
                        config: {
                            min: 1,
                            max: 100,
                        }
                    },
                    {
                        key: 'barStyle',
                        children: [
                            {
                                key: 'radius',
                                type: 'number-input',
                                label: '圆角',
                                value: (config?.barStyle as any)?.radius,
                                config: {
                                    min: 1,
                                    max: 100,
                                }
                            }
                        ]

                    },
                    {
                        id: 'barColor',
                        type: 'enhanced-color-mode',
                        label: '颜色',
                        value: config?.color,
                    }
                ]
            },
        ]
    }

    return (
        <LCGUI schema={schema} onFieldChange={onFieldChange}/>
    )
}


export const AntdBarFieldMapping: React.FC<ConfigType<AntdCommonBarController>> = ({controller}) => {
    const config = controller.getConfig()!.style;
    const {data, xField, yField, seriesField} = config!;
    const options: ISelectOption[] = [];
    if (data && data.length >= 1) {
        const dataObj = data[0];
        Object.keys(dataObj).forEach(key => options.push({label: key, value: key}))
    }

    const fieldChange = (fieldChangeData: FieldChangeData) => {
        controller.update(fieldChangeData.dataFragment);
    }

    const schema: Control = {
        key: 'style',
        type: 'grid',
        children: [
            {
                key: 'xField',
                type: 'select',
                label: 'X字段',
                value: xField,
                config: {
                    options,
                }
            },
            {
                key: 'yField',
                type: 'select',
                label: 'Y字段',
                value: yField,
                config: {
                    options,
                }
            },
            {
                key: 'seriesField',
                type: 'select',
                label: '分组字段',
                value: seriesField,
                config: {
                    options,
                }
            }
        ]
    }

    return (
        <div style={{padding: 10}}><LCGUI schema={schema} onFieldChange={fieldChange}/></div>
    )
}

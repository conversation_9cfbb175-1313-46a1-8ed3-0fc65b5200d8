declare module '@babel/standalone' {
    export interface BabelTransformResult {
        code: string | null;
        map?: any;
        ast?: any;
    }
    
    export interface TransformOptions {
        presets?: string[];
        plugins?: string[];
        [key: string]: any;
    }
    
    export function transform(
        code: string,
        options?: TransformOptions
    ): BabelTransformResult;
} 
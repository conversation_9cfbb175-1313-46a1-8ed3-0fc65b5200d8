/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.lc-legend-config {
  display: flex;
  padding: 10px 0;
  justify-content: space-between;


  .legend-position {
    font-size: 6px;
    width: 50%;

    .pos-row {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .pos-item {
        width: calc(100% / 3);
        text-align: center;
        line-height: 38px;
      }
    }
  }

  .legend-config-right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;


    .legend-layout {
      display: flex;
      flex-direction: column;
      align-items: center;

      .legend-layout-value {
        margin-bottom: 5px;
      }
    }

    .legend-color {
      display: flex;
      flex-direction: column;
      align-items: center;

      .legend-label-color {
        margin-bottom: 5px;
      }
    }
  }

}





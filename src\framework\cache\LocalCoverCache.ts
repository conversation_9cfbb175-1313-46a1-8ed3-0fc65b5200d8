/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractCache from "./AbstractCache";

class LocalCoverCache extends AbstractCache<any> {

    public getAllCoverCache(): string[] {
        return Array.from(this.getCachePool().values()) || [];
    }

    public clearCoverCache(): void {
        this.getAllCoverCache().forEach((url: string) => {
            URL.revokeObjectURL(url);
        });
    }
}

const localCoverCache = new LocalCoverCache();
export default localCoverCache;


/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import DataTypeUtil from "../utils/DataTypeUtil.ts";

/**
 * DataTypeUtil 测试用例
 * 用于验证数据类型处理工具的各种功能
 */
export class DataTypeUtilTest {
    
    /**
     * 测试智能解析功能
     */
    static testSmartParse() {
        console.log('=== 测试 DataTypeUtil.smartParse ===');
        
        const testCases = [
            // 字符串测试
            { input: 'hello', expected: 'hello', description: '普通字符串' },
            { input: '', expected: '', description: '空字符串' },
            { input: '   ', expected: '', description: '空白字符串' },
            
            // 数字测试
            { input: '123', expected: 123, description: '整数字符串' },
            { input: '123.45', expected: 123.45, description: '浮点数字符串' },
            { input: '-123', expected: -123, description: '负整数字符串' },
            { input: '-123.45', expected: -123.45, description: '负浮点数字符串' },
            { input: '1.23e5', expected: 123000, description: '科学计数法' },
            
            // 布尔值测试
            { input: 'true', expected: true, description: '布尔值true' },
            { input: 'false', expected: false, description: '布尔值false' },
            
            // null/undefined测试
            { input: 'null', expected: null, description: 'null值' },
            { input: 'undefined', expected: undefined, description: 'undefined值' },
            
            // JSON对象测试
            { input: '{"name": "test", "value": 123}', expected: {name: "test", value: 123}, description: 'JSON对象' },
            { input: '[1, 2, 3]', expected: [1, 2, 3], description: 'JSON数组' },
            { input: '"quoted string"', expected: 'quoted string', description: '带引号的字符串' },
            
            // 非字符串输入测试
            { input: 123, expected: 123, description: '数字输入' },
            { input: true, expected: true, description: '布尔值输入' },
            { input: {test: 'value'}, expected: {test: 'value'}, description: '对象输入' },
            { input: [1, 2, 3], expected: [1, 2, 3], description: '数组输入' },
        ];
        
        testCases.forEach(testCase => {
            const result = DataTypeUtil.smartParse(testCase.input);
            const passed = JSON.stringify(result) === JSON.stringify(testCase.expected);
            console.log(`${passed ? '✓' : '✗'} ${testCase.description}: ${JSON.stringify(testCase.input)} -> ${JSON.stringify(result)} ${passed ? '' : `(期望: ${JSON.stringify(testCase.expected)})`}`);
        });
    }
    
    /**
     * 测试格式化显示功能
     */
    static testFormatForDisplay() {
        console.log('\n=== 测试 DataTypeUtil.formatForDisplay ===');
        
        const testCases = [
            { input: null, expected: 'null', description: 'null值' },
            { input: undefined, expected: 'undefined', description: 'undefined值' },
            { input: 'hello', expected: 'hello', description: '字符串' },
            { input: 123, expected: '123', description: '数字' },
            { input: true, expected: 'true', description: '布尔值' },
            { input: {name: 'test'}, expected: '{\n  "name": "test"\n}', description: '对象' },
            { input: [1, 2, 3], expected: '[\n  1,\n  2,\n  3\n]', description: '数组' },
        ];
        
        testCases.forEach(testCase => {
            const result = DataTypeUtil.formatForDisplay(testCase.input);
            const passed = result === testCase.expected;
            console.log(`${passed ? '✓' : '✗'} ${testCase.description}: ${JSON.stringify(testCase.input)} -> ${JSON.stringify(result)} ${passed ? '' : `(期望: ${JSON.stringify(testCase.expected)})`}`);
        });
    }
    
    /**
     * 测试数据类型检测功能
     */
    static testGetDataType() {
        console.log('\n=== 测试 DataTypeUtil.getDataType ===');
        
        const testCases = [
            { input: null, expected: 'null', description: 'null值' },
            { input: undefined, expected: 'undefined', description: 'undefined值' },
            { input: 'hello', expected: 'string', description: '字符串' },
            { input: 123, expected: 'number', description: '数字' },
            { input: true, expected: 'boolean', description: '布尔值' },
            { input: {}, expected: 'object', description: '对象' },
            { input: [], expected: 'array', description: '数组' },
            { input: () => {}, expected: 'function', description: '函数' },
        ];
        
        testCases.forEach(testCase => {
            const result = DataTypeUtil.getDataType(testCase.input);
            const passed = result === testCase.expected;
            console.log(`${passed ? '✓' : '✗'} ${testCase.description}: ${typeof testCase.input} -> ${result} ${passed ? '' : `(期望: ${testCase.expected})`}`);
        });
    }
    
    /**
     * 测试JSON验证功能
     */
    static testIsValidJSON() {
        console.log('\n=== 测试 DataTypeUtil.isValidJSON ===');
        
        const testCases = [
            { input: '{"name": "test"}', expected: true, description: '有效JSON对象' },
            { input: '[1, 2, 3]', expected: true, description: '有效JSON数组' },
            { input: '"string"', expected: true, description: '有效JSON字符串' },
            { input: '123', expected: true, description: '有效JSON数字' },
            { input: 'true', expected: true, description: '有效JSON布尔值' },
            { input: 'null', expected: true, description: '有效JSON null' },
            { input: '{name: "test"}', expected: false, description: '无效JSON（缺少引号）' },
            { input: 'hello', expected: false, description: '无效JSON（普通字符串）' },
            { input: 123, expected: false, description: '非字符串输入' },
        ];
        
        testCases.forEach(testCase => {
            const result = DataTypeUtil.isValidJSON(testCase.input);
            const passed = result === testCase.expected;
            console.log(`${passed ? '✓' : '✗'} ${testCase.description}: ${JSON.stringify(testCase.input)} -> ${result} ${passed ? '' : `(期望: ${testCase.expected})`}`);
        });
    }
    
    /**
     * 运行所有测试
     */
    static runAllTests() {
        console.log('开始运行 DataTypeUtil 测试...\n');
        
        this.testSmartParse();
        this.testFormatForDisplay();
        this.testGetDataType();
        this.testIsValidJSON();
        
        console.log('\n测试完成！');
    }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
    DataTypeUtilTest.runAllTests();
}

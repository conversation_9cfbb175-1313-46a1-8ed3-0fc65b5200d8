/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {observer} from "mobx-react";
import {Button, Popconfirm, Typography} from "antd";
import {Close, Edit, Delete, Plus} from "@icon-park/react";
import globalVariableManager, {GlobalVariableDefinition} from "../../manager/GlobalVariableManager.ts";
import designerLeftStore from "../DesignerLeftStore.ts";
import eventOperateStore from "../../operate-provider/EventOperateStore.ts";
import IdGenerate from "../../../utils/IdGenerate.ts";
import GlobalVariablePanel from "./GlobalVariablePanel.tsx";
import layerManager from "../../manager/LayerManager.ts";
import './GlobalVariableList.less';

const {Text} = Typography;

export const GlobalVariableList = observer(() => {
    const {setMenu} = designerLeftStore;
    const {rulerRef} = eventOperateStore;

    // 获取全局变量列表
    const variableList = globalVariableManager.getAllVariableDefinitions();

    // 关闭面板
    const closePanel = () => {
        setMenu("");
        rulerRef?.ruleWheel();
    };

    // 新建全局变量
    const handleCreate = () => {
        const newDefinition: GlobalVariableDefinition = {
            id: IdGenerate.generateId(),
            name: '',
            description: '',
            sourceType: 'static',
            sourceConfig: {
                staticData: null
            },
            filterFunctionString: '',
            isActiveRendering: false,
            initialOrFallbackValue: null
        };
        globalVariableManager.setEditingDefinition(newDefinition);
        globalVariableManager.setPanelVisible(true);
    };

    // 编辑全局变量
    const handleEdit = (definition: GlobalVariableDefinition) => {
        // 创建定义的副本用于编辑
        const editingDefinition = {
            ...definition,
            sourceConfig: {
                ...definition.sourceConfig
            }
        };
        globalVariableManager.setEditingDefinition(editingDefinition);
        globalVariableManager.setPanelVisible(true);
    };

    // 删除全局变量
    const handleDelete = (variableId: string) => {
        try {
            const success = globalVariableManager.deleteDefinition(variableId);
            if (success) {
                // 重新计算引用计数
                globalVariableManager.recalculateAllReferenceCounts();
            }
        } catch (error) {
            console.error('删除全局变量失败:', error);
        }
    };

    // 获取引用计数
    const getReferenceCount = (variableId: string): number => {
        return globalVariableManager.referenceCounts.get(variableId) || 0;
    };

    // 获取删除警告信息
    const getDeleteWarning = (variableId: string): {title: string, description: string} => {
        const referenceCount = getReferenceCount(variableId);
        const variable = globalVariableManager.getVariableDefinition(variableId);

        if (referenceCount === 0) {
            return {
                title: "确认删除？",
                description: "删除后无法恢复"
            };
        }

        // 分析引用详情
        const referenceDetails = analyzeReferences(variableId);
        let description = `该变量被引用了 ${referenceCount} 次，删除后将导致以下影响：\n\n`;

        if (referenceDetails.directReferences > 0) {
            description += `• ${referenceDetails.directReferences} 个组件直接使用此变量作为数据源\n`;
        }

        if (referenceDetails.indirectReferences > 0) {
            description += `• ${referenceDetails.indirectReferences} 个组件在API URL或过滤器中引用此变量\n`;
        }

        if (referenceDetails.blueprintReferences > 0) {
            description += `• ${referenceDetails.blueprintReferences} 个蓝图节点使用此变量\n`;
        }

        if (referenceDetails.filterReferences > 0) {
            description += `• ${referenceDetails.filterReferences} 个全局变量的过滤器中引用此变量\n`;
        }

        description += "\n删除后这些引用将失效，可能导致相关功能异常！";

        return {
            title: "警告：删除被引用的变量",
            description: description
        };
    };

    // 分析引用详情
    const analyzeReferences = (variableId: string): {
        directReferences: number,
        indirectReferences: number,
        blueprintReferences: number,
        filterReferences: number
    } => {
        const variable = globalVariableManager.getVariableDefinition(variableId);
        if (!variable) return { directReferences: 0, indirectReferences: 0, blueprintReferences: 0, filterReferences: 0 };

        let directReferences = 0;
        let indirectReferences = 0;
        let blueprintReferences = 0;
        let filterReferences = 0;

        // 检查组件引用
        if (layerManager && layerManager.compController) {
            Object.values(layerManager.compController).forEach((controller: any) => {
                const config = controller.getConfig();
                if (config?.data) {
                    // 直接引用
                    if (config.data.sourceType === 'globalVariable' && config.data.selectedGlobalVariableId === variableId) {
                        directReferences++;
                    }

                    // 间接引用
                    const hasIndirectRef = checkIndirectReference(config.data, variable.name);
                    if (hasIndirectRef) {
                        indirectReferences++;
                    }
                }
            });
        }

        // 检查蓝图引用
        const bluePrintManager = (window as any).bluePrintManager;
        if (bluePrintManager && bluePrintManager.bpNodeLayoutMap) {
            Object.values(bluePrintManager.bpNodeLayoutMap).forEach((node: any) => {
                if (node.type === 'global-variable-node' && node.id === variableId) {
                    blueprintReferences++;
                }
            });
        }

        // 检查其他全局变量的过滤器引用
        const allDefinitions = globalVariableManager.getAllVariableDefinitions();
        allDefinitions.forEach(def => {
            if (def.id !== variableId && def.filterFunctionString) {
                if (checkTextContainsVariableReference(def.filterFunctionString, variable.name)) {
                    filterReferences++;
                }
            }
        });

        return { directReferences, indirectReferences, blueprintReferences, filterReferences };
    };

    // 检查间接引用
    const checkIndirectReference = (dataConfig: any, variableName: string): boolean => {
        if (!dataConfig || !variableName) return false;

        // 检查API配置
        if (dataConfig.apiData) {
            const { url, filter } = dataConfig.apiData;
            if (checkTextContainsVariableReference(url, variableName) ||
                checkTextContainsVariableReference(filter, variableName)) {
                return true;
            }
        }

        // 检查数据库配置
        if (dataConfig.database) {
            const { filter } = dataConfig.database;
            if (checkTextContainsVariableReference(filter, variableName)) {
                return true;
            }
        }

        // 检查静态数据中的引用
        if (dataConfig.staticData) {
            if (checkObjectContainsVariableReference(dataConfig.staticData, variableName)) {
                return true;
            }
        }

        // 检查过滤器
        if (checkTextContainsVariableReference(dataConfig.staticDataFilter, variableName) ||
            checkTextContainsVariableReference(dataConfig.globalVariableFilter, variableName)) {
            return true;
        }

        return false;
    };

    // 检查文本中是否包含变量引用
    const checkTextContainsVariableReference = (text: string, variableName: string): boolean => {
        if (!text || typeof text !== 'string' || !variableName) return false;

        // 检查 ${GV::variableName} 格式
        const gvRegex = new RegExp(`\\$\\{GV::${escapeRegExp(variableName)}\\}`, 'g');
        if (gvRegex.test(text)) return true;

        // 检查 globalVars.get('variableName') 格式
        const globalVarsRegex = new RegExp(`globalVars\\.get\\(['"]${escapeRegExp(variableName)}['"]\\)`, 'g');
        if (globalVarsRegex.test(text)) return true;

        return false;
    };

    // 检查对象中是否包含变量引用
    const checkObjectContainsVariableReference = (obj: any, variableName: string): boolean => {
        if (obj === null || obj === undefined || !variableName) return false;

        if (typeof obj === 'string') {
            return checkTextContainsVariableReference(obj, variableName);
        }

        if (Array.isArray(obj)) {
            return obj.some(item => checkObjectContainsVariableReference(item, variableName));
        }

        if (typeof obj === 'object') {
            return Object.entries(obj).some(([key, value]) => {
                return checkTextContainsVariableReference(key, variableName) ||
                       checkObjectContainsVariableReference(value, variableName);
            });
        }

        return false;
    };

    // 转义正则表达式特殊字符
    const escapeRegExp = (string: string): string => {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    };

    return (
        <div className={'global-variable-list'}>
            <div className={'gv-list-header'}>
                <div className={'gv-list-header-title'}>全局变量</div>
                <div className={'gv-list-header-operate'}>
                    <Close style={{cursor: 'pointer'}} onClick={closePanel}/>
                </div>
            </div>

            <div className={'gv-list-toolbar'}>
                <Button
                    type="primary"
                    icon={<Plus />}
                    size="small"
                    onClick={handleCreate}
                >
                    新建变量
                </Button>
            </div>

            <div className={'gv-list-content'}>
                {variableList.length === 0 ? (
                    <div className={'gv-list-empty'}>
                        <Text type="secondary">暂无全局变量</Text>
                        <br />
                        <Text type="secondary" style={{fontSize: '12px'}}>
                            点击上方"新建变量"按钮创建第一个全局变量
                        </Text>
                    </div>
                ) : (
                    <div className={'gv-list-items'}>
                        {variableList.map((item: GlobalVariableDefinition) => (
                            <div key={item.id} className={'gv-list-item'}>
                                <div className={'gv-item-content'} onClick={() => handleEdit(item)}>
                                    <div className={'gv-item-name'}>
                                        {item.name || '未命名变量'}
                                    </div>
                                    <div className={`gv-item-badge ${getReferenceCount(item.id) === 0 ? 'zero' : ''}`}>
                                        {getReferenceCount(item.id)}
                                    </div>
                                </div>
                                <div className={'gv-item-actions'}>
                                    <div
                                        className={'gv-action-btn gv-edit-btn'}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleEdit(item);
                                        }}
                                        title="编辑"
                                    >
                                        <Edit size={14} />
                                    </div>
                                    <Popconfirm
                                        title={getDeleteWarning(item.id).title}
                                        description={getDeleteWarning(item.id).description}
                                        onConfirm={() => handleDelete(item.id)}
                                        okText="确认删除"
                                        cancelText="取消"
                                        placement="topRight"
                                        okType={getReferenceCount(item.id) > 0 ? "danger" : "primary"}
                                    >
                                        <div
                                            className={'gv-action-btn gv-delete-btn'}
                                            onClick={(e) => e.stopPropagation()}
                                            title="删除"
                                        >
                                            <Delete size={14} />
                                        </div>
                                    </Popconfirm>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* 配置面板 */}
            <GlobalVariablePanel />
        </div>
    );
});

export default GlobalVariableList;

/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractDesignerController from "../../../../framework/core/AbstractDesignerController.ts";
import {FieldChangeData, LCGUI} from "../../../../json-schema/LCGUI.tsx";
import {Control} from "../../../../json-schema/SchemaTypes.ts";
import {useEffect, useRef, useState} from "react";
import {globalMessage} from "../../../../framework/message/GlobalMessage.tsx";
import {ISelectOption} from "../../../../json-schema/ui/select/Select.tsx";
import ObjectUtil from "../../../../utils/ObjectUtil.ts";
import {IDatabase} from "../../../../designer/DesignerType.ts";
import FetchUtil from "../../../../utils/FetchUtil.ts";
import {IDataSource} from "../../../../pages/home/<USER>/DataSourceStore.ts";
import Base64Util from "../../../../utils/Base64Util.ts";
import globalVariableManager from "../../../../designer/manager/GlobalVariableManager.ts";

export interface DatabaseDataConfigProps {
    controller: AbstractDesignerController;
    data: IDatabase;
}

export function DatabaseDataConfig(props: DatabaseDataConfigProps) {
    const {data, controller} = props;
    const dataRef = useRef(data);
    const [dataSourceList, setDataSourceList] = useState<ISelectOption[]>([]);
    const [testRes, setTestRes] = useState<string | null>(null);
    const [count, setCount] = useState(0);

    useEffect(() => {
        FetchUtil.get(`/api/datasource/list`).then(res => {
            if (res.code === 200) {
                const options = (res.data as Array<IDataSource>).map(item => {
                    return {label: item.name, value: item.id}
                })
                setDataSourceList(options as ISelectOption[]);
            } else
                globalMessage.messageApi?.error(res.msg);
        })
    }, []);

    const validate = () => {
        const {targetDb, sql} = dataRef.current;
        if (!targetDb) {
            globalMessage.messageApi?.error('请选择数据库');
            return false;
        }
        if (!sql) {
            globalMessage.messageApi?.error('请输入SQL语句');
            return false;
        }
        if (!sql.trim().startsWith('select')) {
            globalMessage.messageApi?.error('SQL语句必须以select开头');
            return false;
        }
        return true;
    }

    // 测试数据库查询
    const testDatabase = async () => {
        if (!validate()) return;

        const {targetDb, sql, filter} = dataRef.current;
        if (!sql || sql === '') return;

        try {
            const res = await FetchUtil.post(`/api/db/executor/execute`, {id: targetDb, sql: Base64Util.toBase64(sql)});
            let {data} = res;
            const {code, msg} = res;

            if (code === 200) {
                if (filter && filter !== '') {
                    // 使用GlobalVariableManager的过滤器执行方法，确保支持 ${GV::variableName} 格式
                    data = await globalVariableManager._parseAndExecuteFilter(
                        filter,
                        data,
                        'database-data-filter' // 数据库数据过滤器的虚拟ID
                    );
                }
                setTestRes(JSON.stringify(data, null, 2));
                globalMessage.messageApi?.success('数据库测试成功');
            } else {
                setTestRes(JSON.stringify({msg}, null, 2));
                globalMessage.messageApi?.error('数据库查询失败: ' + msg);
            }
        } catch (error) {
            setTestRes(JSON.stringify({
                error: '数据库测试失败',
                message: (error as Error).message
            }, null, 2));
            globalMessage.messageApi?.error(`数据库测试失败: ${(error as Error).message}`);
        }
    };

    // 保存数据库配置
    const saveDatabase = async () => {
        if (!validate()) return;

        const {targetDb, sql, filter} = dataRef.current;
        if (!sql || sql === '') return;

        try {
            const res = await FetchUtil.post(`/api/db/executor/execute`, {id: targetDb, sql: Base64Util.toBase64(sql)});
            let {data} = res;
            const {code, msg} = res;

            if (code === 200) {
                if (filter && filter !== '') {
                    // 使用GlobalVariableManager的过滤器执行方法，确保支持 ${GV::variableName} 格式
                    data = await globalVariableManager._parseAndExecuteFilter(
                        filter,
                        data,
                        'database-data-filter' // 数据库数据过滤器的虚拟ID
                    );
                }

                // 更新组件配置和数据
                controller.update({data: {database: dataRef.current, staticData: data}});
                controller.changeData(data);
                globalMessage.messageApi?.success('数据库配置保存成功');
            } else {
                // 即使查询失败也保存配置
                controller.update({data: {database: dataRef.current}}, {reRender: false});
                globalMessage.messageApi?.warning('配置项已保存，但数据未成功刷新: ' + msg);
            }

            // 保存后重新计算引用计数（过滤器可能包含全局变量引用）
            globalVariableManager.recalculateAllReferenceCounts();
        } catch (error) {
            // 即使查询失败也保存配置
            controller.update({data: {database: dataRef.current}}, {reRender: false});
            globalMessage.messageApi?.error(`保存失败: ${(error as Error).message}`);

            // 保存后重新计算引用计数
            globalVariableManager.recalculateAllReferenceCounts();
        }
    };

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {reRender, id, dataFragment} = fieldChangeData;
        if (id === 'testDatabase') {
            testDatabase();
        } else if (id === 'saveDatabase') {
            saveDatabase();
        } else {
            dataRef.current = ObjectUtil.merge(dataRef.current, dataFragment);
        }
        if (reRender) {
            setCount(count + 1);
        }
    }

    const schema: Control = {
        type: 'grid',
        config: {gridGap: '10px'},
        children: [
            {
                key: 'targetDb',
                label: '数据库',
                type: 'select',
                value: dataRef.current?.targetDb,
                reRender: true,
                config: {
                    options: dataSourceList
                }
            },
            {
                type: 'grid',
                label: '自动更新',
                config: {columns: 8},
                children: [
                    {
                        key: 'autoFlush',
                        type: 'checkbox',
                        value: !!dataRef.current?.autoFlush,
                    },
                    {
                        key: 'frequency',
                        type: 'number-input',
                        config: {
                            min: 5,
                            containerStyle: {
                                gridColumn: '2/9',
                            }
                        },
                        value: dataRef.current?.frequency || 5,
                    },
                ]
            },
            {
                type: 'card-panel',
                label: 'SQL语句',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        key: 'sql',
                        type: 'code-editor',
                        config: {
                            height: 160,
                            language: 'sql'
                        },
                        value: dataRef.current?.sql,
                    }
                ]
            },
            {
                type: 'card-panel',
                label: '过滤器',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        key: 'filter',
                        type: 'code-editor',
                        config: {
                            height: 200,
                            language: 'javascript'
                        },
                        value: dataRef.current?.filter || "function filter(data){\n\n\n\treturn data\n}",
                    }
                ]
            },
            {
                id: 'databaseTestRes',
                type: 'card-panel',
                label: '响应结果',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        id: 'databaseTestRes',
                        type: 'code-editor',
                        config: {
                            height: 200,
                            language: 'json'
                        },
                        reRender: true,
                        value: testRes,
                    }
                ]
            },
            {
                type: 'grid',
                config: {
                    columns: 2,
                    gap: 8
                },
                children: [
                    {
                        id: 'testDatabase',
                        type: 'button',
                        config: {
                            children: '测试',
                            type: 'default'
                        }
                    },
                    {
                        id: 'saveDatabase',
                        type: 'button',
                        config: {
                            children: '保存',
                            type: 'primary'
                        }
                    }
                ]
            },
        ]
    }

    return (
        <LCGUI schema={schema} onFieldChange={onFieldChange}/>
    );
}
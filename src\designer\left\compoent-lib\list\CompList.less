/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

@import '../../../style/DesignerStyle.less';


.list-search {
  padding: 5px;

  input {
    background-color: #363636;
    transition: background-color 0.5s;
  }

  input:hover {
    background-color: #2a2a2a;
    transition-duration: 0.3s;
  }

  input::placeholder {
    color: #818181;
    transition-duration: 0.3s;
  }
}

.list-items {
  padding: 0 5px;
  overflow-y: auto;
  height: calc(100% - 39px);


  .list-item {
    width: 100%;
    height: 120px;
    cursor: pointer;
    margin-bottom: 10px;
    background-color: #363636;

    .item-header {
      height: 25px;
      line-height: 25px;
      background-color: #363636;
      display: flex;
      justify-content: space-between;
      padding: 0 5px;
      color: #8d8d8d;
      font-size: 10px;
    }

    .item-content {
      box-sizing: content-box;
      padding: 5px 10px 10px 10px;

      img {
        width: 100%;
      }
    }
  }
}


@keyframes lcFadeInUp1 {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  50% {
    opacity: 1;
    transform: translateX(20px);
  }
  55% {
    opacity: 1;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }

}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import * as React from 'react';
import * as antd from 'antd';
import * as Babel from '@babel/standalone';
import globalVariableManager from '../../../designer/manager/GlobalVariableManager';
import { ThemeItemType } from '../../../designer/DesignerType';

/**
 * 自定义组件转换器
 * 负责将用户编写的JSX代码转换为可执行的React组件
 */
export default class CustomComponentTransformer {
    /**
     * 转换JSX代码为JavaScript代码
     * @param code 用户编写的JSX代码
     * @returns 转换后的JavaScript代码
     */
    public static transformJSXCode(code: string): string {
        try {
            // 将函数表达式包装成可解析的形式
            const wrappedCode = `const userFunction = ${code}`;

            const result = Babel.transform(wrappedCode, {
                presets: ['react'],
                plugins: []
            });

            if (!result.code) {
                throw new Error('转换结果为空');
            }

            // 提取转换后的函数部分，移除末尾的分号
            let transformedCode = result.code.replace('const userFunction = ', '');
            // 移除末尾的分号，因为我们要将其作为函数表达式使用
            transformedCode = transformedCode.replace(/;$/, '');
            return transformedCode;
        } catch (error) {
            console.error('JSX转换错误:', error);
            throw new Error(`JSX语法错误: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    
    /**
     * 创建执行上下文
     * 包含在执行自定义代码时可用的组件和工具
     */
    public static createExecutionContext() {
        // 构建Antd命名空间
        const Antd = {
            // 常用组件
            Button: antd.Button,
            Input: antd.Input,
            Select: antd.Select,
            Checkbox: antd.Checkbox,
            Radio: antd.Radio,
            Switch: antd.Switch,
            DatePicker: antd.DatePicker,
            TimePicker: antd.TimePicker,
            Form: antd.Form,
            
            // 数据展示
            Table: antd.Table,
            Card: antd.Card,
            Descriptions: antd.Descriptions,
            List: antd.List,
            Tag: antd.Tag,
            Badge: antd.Badge,
            
            // 反馈
            Alert: antd.Alert,
            Modal: antd.Modal,
            message: antd.message,
            notification: antd.notification,
            Progress: antd.Progress,
            Result: antd.Result,
            Spin: antd.Spin,
            
            // 布局
            Divider: antd.Divider,
            Grid: antd.Grid,
            Layout: antd.Layout,
            Space: antd.Space,
            
            // 导航
            Menu: antd.Menu,
            Pagination: antd.Pagination,
            Steps: antd.Steps,
            
            // 其他组件
            Avatar: antd.Avatar,
            Image: antd.Image,
            Typography: antd.Typography,
            Upload: antd.Upload,
            Drawer: antd.Drawer,
            Popover: antd.Popover,
            Tooltip: antd.Tooltip,
            Empty: antd.Empty,
            Statistic: antd.Statistic,
            Rate: antd.Rate,
            
            // 快捷访问
            Row: antd.Row,
            Col: antd.Col
        };
        
        return {
            React,
            Antd
        };
    }
    
    /**
     * 从代码中提取全局变量引用并更新引用计数
     * @param code 要分析的代码
     */
    private static updateGlobalVariableReferences(code: string): void {
        if (!code || typeof code !== 'string') return;

        try {
            // 匹配 globalVars.get('variableName') 或 globalVars.get("variableName") 格式的引用
            const globalVarsRegex = /globalVars\.get\(['"]([^'"]+)['"]\)/g;
            let match;
            const referencedVars = new Set<string>();

            while ((match = globalVarsRegex.exec(code)) !== null) {
                const variableName = match[1];
                referencedVars.add(variableName);
            }

            if (referencedVars.size > 0) {
                // 将引用的变量名转换为变量ID并增加引用计数
                const definitions = globalVariableManager.getAllVariableDefinitions();
                for (const definition of definitions) {
                    if (referencedVars.has(definition.name)) {
                        globalVariableManager.incrementReferenceCount(definition.id);
                    }
                }

                // 重新计算所有引用计数以确保UI更新
                globalVariableManager.recalculateAllReferenceCounts();
            }
        } catch (error) {
            console.warn('提取全局变量引用时出错:', error);
        }
    }

    /**
     * 公开方法：仅更新全局变量引用计数（用于保存时调用）
     * @param code 要分析的代码
     */
    public static updateGlobalVariableReferencesOnly(code: string): void {
        // 先更新当前代码的引用
        this.updateGlobalVariableReferences(code);
        // 然后重新计算所有引用计数，确保UI更新
        globalVariableManager.recalculateAllReferenceCounts();
    }
    
    /**
     * 执行自定义组件代码
     * @param code 用户编写的代码
     * @param data 传递给组件的数据
     * @param globalVars 全局变量访问器
     * @param theme 主题配置
     * @returns 执行结果（React元素）
     */
    public static executeComponentCode(code: string, data: any, globalVars: any, theme?: ThemeItemType): React.ReactNode {
        try {
            // 更新全局变量引用计数
            this.updateGlobalVariableReferences(code);
            
            // 1. 转换JSX为JavaScript
            const transformedCode = this.transformJSXCode(code);
            
            // 2. 创建执行上下文
            const context = this.createExecutionContext();
            
            // 3. 创建函数并执行
            const func = new Function(
                'React',
                'Antd',
                'data',
                'globalVars',
                'theme',
                `
                try {
                    // 执行转换后的代码并返回结果
                    const renderFunc = ${transformedCode};
                    if (typeof renderFunc !== 'function') {
                        throw new Error("代码必须返回一个可执行的函数");
                    }
                    return renderFunc(data, globalVars, theme);
                } catch (error) {
                    console.error("执行错误详情:", error);
                    throw new Error("组件执行错误: " + error.message);
                }
                `
            );

            // 4. 执行函数并获取结果
            const result = func(
                context.React,
                context.Antd,
                data,
                globalVars,
                theme
            );
            
            // 确保结果是有效的React元素
            if (!React.isValidElement(result) && result !== null && result !== undefined) {
                console.warn('自定义组件返回的不是有效的React元素:', result);
                return React.createElement('div', { style: { color: 'red' } }, '错误: 返回值不是有效的React元素');
            }
            
            return result;
        } catch (error) {
            console.error('自定义组件执行错误:', error);
            throw error;
        }
    }
    
    /**
     * 验证代码语法
     * @param code 要验证的代码
     * @returns 验证结果
     */
    public static validateCode(code: string): { valid: boolean; error?: string } {
        try {
            this.transformJSXCode(code);
            return { valid: true };
        } catch (error) {
            return { 
                valid: false, 
                error: error instanceof Error ? error.message : '未知错误' 
            };
        }
    }
} 
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import HistoryQueue from "./HistoryQueue";
import {IHistoryRecords} from "./OperateType";

class HistoryOperator {
    private queue: HistoryQueue<IHistoryRecords> = new HistoryQueue<IHistoryRecords>(100);

    public put(record: IHistoryRecords): void {
        this.queue.enqueue(record);
    }

    public backoff(): IHistoryRecords | null {
        return this.queue.backoff();
    }

    public forward(): IHistoryRecords | null {
        return this.queue.forward();
    }
}

const historyOperator = new HistoryOperator();
export {historyOperator}
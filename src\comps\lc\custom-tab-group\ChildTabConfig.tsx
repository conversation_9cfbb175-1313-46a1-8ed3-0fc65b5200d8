/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, { useState } from 'react';
import {ConfigType} from "../../../designer/right/ConfigContent";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI";
import {Control} from "../../../json-schema/SchemaTypes";
import {ChildTabController} from "./ChildTabController";

const ChildTabConfig: React.FC<ConfigType<ChildTabController>> = ({controller}) => {
    const config = controller.getConfig();
    const {activeStyle, inactiveStyle} = config?.style || {};
    const [, setRerenderCount] = useState(0);

    const handleFieldChange = (fieldChangeData: FieldChangeData) => {
        if (config) {
            const { id, data, dataFragment, reRender } = fieldChangeData;

            // 处理图片上传的特殊情况 - 检查id是否包含backgroundImage
            if (id && id.includes('backgroundImage') && data && typeof data === 'object' && 'url' in data) {

                // 从data中提取url和hash
                const { url, hash } = data as { url: string; hash: string };

                // 使用点分隔路径创建更新对象
                const updatePath = id.split('.');
                const updateObj: any = {};
                let current = updateObj;

                // 构建嵌套结构 (例如 style.activeStyle.backgroundImage.localUrl)
                for (let i = 0; i < updatePath.length - 1; i++) {
                    current[updatePath[i]] = {};
                    current = current[updatePath[i]];
                }

                // 设置最终的值 - 处理backgroundImage的情况
                const finalPath = updatePath[updatePath.length - 1];
                if (finalPath === 'backgroundImage') {
                    if (url && url.trim() !== '') {
                        // 有图片URL，设置完整的backgroundImage对象
                        current.backgroundImage = {
                            type: 'local',
                            localUrl: url,  // 只存储URL字符串
                            hash: hash      // hash单独存储
                        };
                    } else {
                        // 删除图片，清空backgroundImage对象
                        current.backgroundImage = {
                            type: 'local',
                            localUrl: '',
                            hash: ''
                        };
                    }
                } else {
                    current[finalPath] = url; // 其他情况直接赋值
                }



                // 更新组件配置
                controller.update(updateObj);

                // 强制重新渲染
                if (reRender) {
                    setRerenderCount(count => count + 1);
                }

                return; // 图片上传逻辑处理完毕，不再继续下面的通用逻辑
            }
            
            // 处理点分隔的键名，转换为正确的嵌套结构
            const styleUpdates = dataFragment.style || {};
            const processedStyleUpdates: any = {};

            Object.keys(styleUpdates).forEach(key => {
                const value = styleUpdates[key];
                if (key.includes('.')) {
                    // 处理点分隔的键名，如 "activeStyle.background"
                    const parts = key.split('.');
                    let current = processedStyleUpdates;
                    for (let i = 0; i < parts.length - 1; i++) {
                        if (!current[parts[i]]) {
                            current[parts[i]] = {};
                        }
                        current = current[parts[i]];
                    }
                    current[parts[parts.length - 1]] = value;
                } else {
                    processedStyleUpdates[key] = value;
                }
            });

            const updatedConfig = {
                ...config,
                style: {
                    ...config.style,
                    ...processedStyleUpdates
                }
            };

            controller.update(updatedConfig);
            
            // 可能需要重新渲染
            if (reRender) {
                setRerenderCount(count => count + 1);
            }
        }
    };

    const schema: Control = {
        key: 'style',
        type: 'accordion',
        label: '样式配置',
        children: [
            {
                type: 'sub-accordion',
                label: '激活样式',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                key: 'activeStyle.background',
                                type: 'color-picker',
                                label: '背景色',
                                value: activeStyle?.background,
                            },
                            {
                                key: 'activeStyle.color',
                                type: 'color-picker',
                                label: '文字颜色',
                                value: activeStyle?.color,
                            },
                            {
                                key: 'activeStyle.borderColor',
                                type: 'color-picker',
                                label: '边框颜色',
                                value: activeStyle?.borderColor,
                            },
                            {
                                key: 'activeStyle.borderWidth',
                                type: 'number-input',
                                label: '边框宽度',
                                value: activeStyle?.borderWidth,
                                config: {
                                    min: 0,
                                    max: 10,
                                }
                            },
                            {
                                key: 'activeStyle.borderRadius',
                                type: 'number-input',
                                label: '圆角',
                                value: activeStyle?.borderRadius,
                                config: {
                                    min: 0,
                                    max: 50,
                                }
                            },
                            {
                                key: 'activeStyle.fontSize',
                                type: 'number-input',
                                label: '字体大小',
                                value: activeStyle?.fontSize,
                                config: {
                                    min: 8,
                                    max: 72,
                                }
                            },
                            {
                                key: 'activeStyle.fontWeight',
                                type: 'select',
                                label: '字体粗细',
                                value: activeStyle?.fontWeight,
                                config: {
                                    options: [
                                        {label: '正常', value: 'normal'},
                                        {label: '粗体', value: 'bold'},
                                        {label: '100', value: '100'},
                                        {label: '200', value: '200'},
                                        {label: '300', value: '300'},
                                        {label: '400', value: '400'},
                                        {label: '500', value: '500'},
                                        {label: '600', value: '600'},
                                        {label: '700', value: '700'},
                                        {label: '800', value: '800'},
                                        {label: '900', value: '900'},
                                    ]
                                }
                            },
                            {
                                key: 'activeStyle.textAlign',
                                type: 'select',
                                label: '文本对齐',
                                value: activeStyle?.textAlign,
                                config: {
                                    options: [
                                        {label: '左对齐', value: 'left'},
                                        {label: '居中', value: 'center'},
                                        {label: '右对齐', value: 'right'},
                                    ]
                                }
                            },
                            {
                                key: 'activeStyle.padding',
                                type: 'input',
                                label: '内边距',
                                value: activeStyle?.padding,
                                config: {
                                    placeholder: '例如: 8px 16px'
                                }
                            },
                        ]
                    },
                    {
                        type: 'sub-accordion',
                        label: '背景图片',
                        children: [
                            {
                                key: 'activeStyle.backgroundImage.type',
                                type: 'radio',
                                label: '来源',
                                value: activeStyle?.backgroundImage?.type || 'local',
                                reRender: true,
                                config: {
                                    options: [
                                        {label: '本地', value: 'local'},
                                        {label: '在线', value: 'online'},
                                    ],
                                }
                            },
                            {
                                key: 'activeStyle.backgroundImage.onLineUrl',
                                rules: "{activeStyle.backgroundImage.type} === 'online'",
                                type: 'input',
                                label: '链接',
                                value: activeStyle?.backgroundImage?.onLineUrl || '',
                            },
                            {
                                id: 'style.activeStyle.backgroundImage',
                                key: 'activeStyle.backgroundImage.localUrl',
                                rules: "{activeStyle.backgroundImage.type} === 'local'",
                                type: 'image-upload',
                                label: '上传',
                                value: activeStyle?.backgroundImage?.localUrl || '',
                                config: {
                                    accept: 'image/*',
                                    size: 3,
                                }
                            },
                        ]
                    }
                ]
            },
            {
                type: 'sub-accordion',
                label: '未激活样式',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                key: 'inactiveStyle.background',
                                type: 'color-picker',
                                label: '背景色',
                                value: inactiveStyle?.background,
                            },
                            {
                                key: 'inactiveStyle.color',
                                type: 'color-picker',
                                label: '文字颜色',
                                value: inactiveStyle?.color,
                            },
                            {
                                key: 'inactiveStyle.borderColor',
                                type: 'color-picker',
                                label: '边框颜色',
                                value: inactiveStyle?.borderColor,
                            },
                            {
                                key: 'inactiveStyle.borderWidth',
                                type: 'number-input',
                                label: '边框宽度',
                                value: inactiveStyle?.borderWidth,
                                config: {
                                    min: 0,
                                    max: 10,
                                }
                            },
                            {
                                key: 'inactiveStyle.borderRadius',
                                type: 'number-input',
                                label: '圆角',
                                value: inactiveStyle?.borderRadius,
                                config: {
                                    min: 0,
                                    max: 50,
                                }
                            },
                            {
                                key: 'inactiveStyle.fontSize',
                                type: 'number-input',
                                label: '字体大小',
                                value: inactiveStyle?.fontSize,
                                config: {
                                    min: 8,
                                    max: 72,
                                }
                            },
                            {
                                key: 'inactiveStyle.fontWeight',
                                type: 'select',
                                label: '字体粗细',
                                value: inactiveStyle?.fontWeight,
                                config: {
                                    options: [
                                        {label: '正常', value: 'normal'},
                                        {label: '粗体', value: 'bold'},
                                        {label: '100', value: '100'},
                                        {label: '200', value: '200'},
                                        {label: '300', value: '300'},
                                        {label: '400', value: '400'},
                                        {label: '500', value: '500'},
                                        {label: '600', value: '600'},
                                        {label: '700', value: '700'},
                                        {label: '800', value: '800'},
                                        {label: '900', value: '900'},
                                    ]
                                }
                            },
                            {
                                key: 'inactiveStyle.textAlign',
                                type: 'select',
                                label: '文本对齐',
                                value: inactiveStyle?.textAlign,
                                config: {
                                    options: [
                                        {label: '左对齐', value: 'left'},
                                        {label: '居中', value: 'center'},
                                        {label: '右对齐', value: 'right'},
                                    ]
                                }
                            },
                            {
                                key: 'inactiveStyle.padding',
                                type: 'input',
                                label: '内边距',
                                value: inactiveStyle?.padding,
                                config: {
                                    placeholder: '例如: 8px 16px'
                                }
                            },
                        ]
                    },
                    {
                        type: 'sub-accordion',
                        label: '背景图片',
                        children: [
                            {
                                key: 'inactiveStyle.backgroundImage.type',
                                type: 'radio',
                                label: '来源',
                                value: inactiveStyle?.backgroundImage?.type || 'local',
                                reRender: true,
                                config: {
                                    options: [
                                        {label: '本地', value: 'local'},
                                        {label: '在线', value: 'online'},
                                    ],
                                }
                            },
                            {
                                key: 'inactiveStyle.backgroundImage.onLineUrl',
                                rules: "{inactiveStyle.backgroundImage.type} === 'online'",
                                type: 'input',
                                label: '链接',
                                value: inactiveStyle?.backgroundImage?.onLineUrl || '',
                            },
                            {
                                id: 'style.inactiveStyle.backgroundImage',
                                key: 'inactiveStyle.backgroundImage.localUrl',
                                rules: "{inactiveStyle.backgroundImage.type} === 'local'",
                                type: 'image-upload',
                                label: '上传',
                                value: inactiveStyle?.backgroundImage?.localUrl || '',
                                config: {
                                    accept: 'image/*',
                                    size: 3,
                                }
                            },
                        ]
                    }
                ]
            }
        ]
    };

    return (
        <div style={{padding: 10}}>
            <div style={{
                marginBottom: 16,
                padding: '8px 12px',
                background: '#1890ff',
                borderRadius: 4,
                color: '#fff',
                fontWeight: 'bold',
                textAlign: 'center'
            }}>
                配置子Tab: {config?.base?.name || 'Tab'}
            </div>
            <LCGUI schema={schema} onFieldChange={handleFieldChange}/>
        </div>
    );
};

export default ChildTabConfig;

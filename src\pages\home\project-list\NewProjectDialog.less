/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.add-new-screen-dialog {

  .lc-add-new-screen {
    padding: 15px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .item-content {
      width: 150px !important;
    }
  }

  .add-new-screen-explain {
    color: #6e6e6e;
    padding: 0 15px;
    margin-bottom: 10px;

    p {
      line-height: 20px;
      font-size: 12px;
      margin-bottom: 2px;
    }
  }

  .add-new-screen-footer {
    margin-top: 3px;
    border-top: 2px solid #272b34;
    display: flex;
    flex-direction: row-reverse;

    .lc-button {
      font-size: 12px;
      color: #a3a3a3;
      width: 85px;
      height: 30px;
      margin: 6px 5px 10px 0;
    }
  }
}
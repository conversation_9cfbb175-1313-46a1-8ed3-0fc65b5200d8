# Echarts自定义组件全局变量使用示例

## 功能说明

Echarts自定义组件现在支持使用全局变量，提供了两种引用方式：

### 1. globalVars.get() API方式
```javascript
function renderEcharts(container, echarts, data, globalVars) {
    // 使用globalVars.get()方法获取全局变量值
    const lineWidth = globalVars.get('lineWidth') || 2;
    const fontSize = globalVars.get('fontSize') || 12;
    const textColor = globalVars.get('textColor') || '#333';
    
    const chart = echarts.init(container);
    
    const option = {
        title: {
            text: 'Echarts示例',
            textStyle: {
                color: textColor, // 使用全局变量
                fontSize: fontSize // 使用全局变量
            }
        },
        xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            data: data || [120, 200, 150, 80, 70, 110, 130],
            type: 'line',
            lineStyle: {
                width: lineWidth // 使用全局变量
            }
        }]
    };
    
    chart.setOption(option);
    return chart;
}
```

### 2. ${GV::variableName} 模板语法
```javascript
function renderEcharts(container, echarts, data, globalVars) {
    // 注意：模板语法会在代码执行前被替换
    // 例如：${GV::chartTitle} 会被替换为实际的全局变量值
    
    const chart = echarts.init(container);
    
    const option = {
        title: {
            text: '${GV::chartTitle}' // 模板语法，会被自动替换
        },
        xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
            type: 'value',
            max: ${GV::maxValue} // 数值类型的全局变量
        },
        series: [{
            data: data || [120, 200, 150, 80, 70, 110, 130],
            type: 'bar',
            itemStyle: {
                color: '${GV::primaryColor}' // 颜色类型的全局变量
            }
        }]
    };
    
    chart.setOption(option);
    return chart;
}
```

## 自动更新机制

当全局变量值发生变化时，Echarts自定义组件会自动：
1. 检测代码中引用的全局变量
2. 订阅这些变量的变化通知
3. 当变量值改变时，自动重新创建图表

## 支持的全局变量类型

- 字符串类型：用于标题、颜色等
- 数值类型：用于尺寸、阈值等
- 布尔类型：用于开关控制
- 对象类型：用于复杂配置
- 数组类型：用于数据源等

## 最佳实践

1. **使用默认值**：始终为全局变量提供默认值，避免undefined错误
   ```javascript
   const threshold = globalVars.get('threshold') || 100;
   ```

2. **合理选择引用方式**：
   - 静态配置使用模板语法：`${GV::variableName}`
   - 动态逻辑使用API方式：`globalVars.get('variableName')`

3. **性能优化**：避免在循环中频繁调用`globalVars.get()`

## 动态样式支持

组件还支持通过蓝图传入动态样式配置：

```javascript
function renderEcharts(container, echarts, data, globalVars, dynamicStyle) {
    const chart = echarts.init(container);
    
    // 基础配置
    const baseOption = {
        title: { text: 'Echarts图表' },
        // ... 其他配置
    };
    
    // 合并动态样式（关键步骤）
    const finalOption = dynamicStyle ? 
        Object.assign({}, baseOption, dynamicStyle) : baseOption;
    
    chart.setOption(finalOption);
    return chart;
}
```

## 常见图表类型示例

### 柱状图
```javascript
function renderEcharts(container, echarts, data, globalVars) {
    const chart = echarts.init(container);
    const option = {
        xAxis: { type: 'category', data: ['A', 'B', 'C', 'D'] },
        yAxis: { type: 'value' },
        series: [{ data: data || [10, 20, 30, 40], type: 'bar' }]
    };
    chart.setOption(option);
    return chart;
}
```

### 饼图
```javascript
function renderEcharts(container, echarts, data, globalVars) {
    const chart = echarts.init(container);
    const option = {
        tooltip: { trigger: 'item' },
        series: [{
            type: 'pie',
            data: data || [
                { value: 1048, name: '搜索引擎' },
                { value: 735, name: '直接访问' },
                { value: 580, name: '邮件营销' }
            ]
        }]
    };
    chart.setOption(option);
    return chart;
}
```

### 折线图
```javascript
function renderEcharts(container, echarts, data, globalVars) {
    const chart = echarts.init(container);
    const option = {
        xAxis: { type: 'category', data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'] },
        yAxis: { type: 'value' },
        series: [{ data: data || [820, 932, 901, 934, 1290], type: 'line' }]
    };
    chart.setOption(option);
    return chart;
}
```

## 注意事项

1. 模板语法`${GV::variableName}`在代码执行前被替换，适用于静态配置
2. API方式`globalVars.get()`在运行时获取值，适用于动态逻辑
3. 全局变量名称区分大小写
4. 当全局变量不存在时，`globalVars.get()`返回undefined
5. 组件会自动检测代码中的全局变量引用并订阅变化
6. 当全局变量值改变时，图表会自动重新渲染
7. 引用计数会在代码更新、项目保存时自动重新计算
8. 必须返回echarts实例，否则组件无法正常工作

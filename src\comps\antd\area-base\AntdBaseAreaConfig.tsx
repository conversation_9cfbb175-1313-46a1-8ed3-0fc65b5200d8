/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {Component} from 'react';
import {AreaOptions, ColorAttr, ShapeStyle} from "@antv/g2plot";
import AntdCommonAreaController from "../../antd-common/area/AntdCommonAreaController";
import {AntdCartesianCoordinateSys} from "../../antd-common/config/AntdFragment";
import {MappingOptions} from "@antv/g2plot/lib/adaptor/geometries/base";
import {AntdBaseDesignerController} from "../../antd-common/AntdBaseDesignerController";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI";
import {Control} from "../../../json-schema/SchemaTypes";
import AntdCommonUtil from "../../antd-common/AntdCommonUtil";
import {ConfigType} from "../../../designer/right/ConfigContent";
import ColorUtil from "../../../utils/ColorUtil";

class AntdBaseAreaStyleConfig extends Component<ConfigType> {

    AreaCoordinateSysChange = (config: AreaOptions) => {
        const controller = this.props.controller as AntdCommonAreaController;
        controller.update({style: config});
    }

    baseAreaGraphicsChange = (config: AreaOptions) => {
        const controller = this.props.controller as AntdCommonAreaController;
        controller.update({style: config});
    }

    render() {
        const {controller} = this.props;
        const config: AreaOptions = controller.getConfig().style;
        return (
            <>
                <AntdBaseAreaGraphics controller={controller}/>
                <AntdCartesianCoordinateSys onChange={this.AreaCoordinateSysChange} config={config}/>
            </>
        );
    }
}

export {AntdBaseAreaStyleConfig};

export const AntdBaseAreaGraphics: React.FC<ConfigType> = ({controller}) => {

    const config: AreaOptions = controller.getConfig().style;

    const _onChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataFragment} = fieldChangeData;

        // 处理数据面颜色
        if (id === 'areaFill') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {areaStyle: {...config?.areaStyle, fill: gradientCss as any}}});
            } else {
                // 处理普通颜色
                controller.update({style: {areaStyle: {...config?.areaStyle, fill: data as any}}});
            }
        }
        // 处理数据线颜色
        else if (id === 'lineColor') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {line: {...config?.line, color: gradientCss as any}}});
            } else {
                // 处理普通颜色
                controller.update({style: {line: {...config?.line, color: data as any}}});
            }
        }
        // 处理数据点填充颜色
        else if (id === 'pointFill') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {point: {...config?.point, style: {...(config?.point?.style as any), fill: gradientCss as any}}}});
            } else {
                // 处理普通颜色
                controller.update({style: {point: {...config?.point, style: {...(config?.point?.style as any), fill: data as any}}}});
            }
        }
        // 处理数据点描边颜色
        else if (id === 'pointStroke') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {point: {...config?.point, style: {...(config?.point?.style as any), stroke: gradientCss as any}}}});
            } else {
                // 处理普通颜色
                controller.update({style: {point: {...config?.point, style: {...(config?.point?.style as any), stroke: data as any}}}});
            }
        }
        else {
            controller.update(dataFragment);
        }
    }

    const schema: Control = {
        key: 'style',
        type: 'accordion',
        label: '图形',
        children: [
            {
                type: 'sub-accordion',
                label: '基础',
                children: [
                    {
                        label: '基准填充',
                        type: 'switch',
                        key: 'startOnZero',
                        value: !!config?.startOnZero,
                    },
                ]
            },
            {
                label: '数据点',
                type: 'sub-accordion',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                key: 'point',
                                children: [
                                    {
                                        label: '尺寸',
                                        type: 'number-input',
                                        key: 'size',
                                        value: (config?.point as MappingOptions)?.size as number || 0,
                                        config: {
                                            min: 0,
                                            max: 10,
                                        }
                                    },
                                    {
                                        key: 'style',
                                        children: [
                                            {
                                                key: 'lineWidth',
                                                type: 'number-input',
                                                label: '描边宽',
                                                value: (config?.point?.style as ShapeStyle)?.lineWidth,
                                                config: {
                                                    min: 0
                                                }
                                            },
                                            {
                                                id: 'pointFill',
                                                key: 'fill',
                                                type: 'enhanced-color-mode',
                                                label: '颜色',
                                                value: (config?.point?.style as ShapeStyle)?.fill,
                                            },
                                            {
                                                id: 'pointStroke',
                                                key: 'stroke',
                                                type: 'enhanced-color-mode',
                                                label: '描边色',
                                                value: (config?.point?.style as ShapeStyle)?.stroke,
                                            },
                                        ]
                                    },
                                    {
                                        key: 'shape',
                                        type: 'select',
                                        label: '形状',
                                        value: (config?.point as MappingOptions)?.shape as string || 'circle',
                                        config: {
                                            options: [
                                                {value: 'circle', label: '圈形'},
                                                {value: 'square', label: '方形'},
                                                {value: 'bowtie', label: '领结'},
                                                {value: 'diamond', label: '钻石'},
                                                {value: 'hexagon', label: '六角形'},
                                                {value: 'triangle', label: '三角形'}]
                                        }
                                    }
                                ]
                            }
                        ]
                    }
                ],
            },
            {
                label: '数据线',
                type: 'sub-accordion',
                children: [
                    {
                        type: 'grid',
                        config: {
                            gridGap: '15px'
                        },
                        children: [
                            {
                                label: '平滑',
                                type: 'switch',
                                key: 'smooth',
                                value: !!config?.smooth,
                            },
                            {
                                key: 'line',
                                children: [
                                    {
                                        key: 'size',
                                        type: 'number-input',
                                        label: '宽度',
                                        value: config?.line?.size as number || 0,
                                        config: {
                                            min: 0,
                                            max: 10,
                                        }
                                    },
                                    {
                                        id: 'lineColor',
                                        key: 'color',
                                        type: 'enhanced-color-mode',
                                        label: '颜色',
                                        value: config?.line?.color as string || '#fff',
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                label: '数据面',
                type: 'sub-accordion',
                key: 'areaStyle',
                children: [
                    {
                        type: 'grid',
                        children: [{
                            id: 'areaFill',
                            key: 'fill',
                            type: 'enhanced-color-mode',
                            label: '颜色',
                            value: (config?.areaStyle as ShapeStyle)?.fill || '#fff',
                        }]
                    }
                ]
            }
        ],
    }

    return (
        <LCGUI schema={schema} onFieldChange={_onChange}/>
    )
}

export const AntdBaseAreaFieldMapping: React.FC<ConfigType<AntdBaseDesignerController>> = (props) => {
    const {controller} = props;
    const config = controller?.config?.style;
    const options = AntdCommonUtil.getDataFieldOptions(controller);
    const schema: Control = {
        key: 'style',
        type: 'grid',
        children: [
            {
                key: 'xField',
                type: 'select',
                label: 'X字段',
                value: config?.xField,
                config: {
                    options,
                }
            },
            {
                key: 'yField',
                type: 'select',
                label: 'Y字段',
                value: config?.yField,
                config: {
                    options,
                }
            }
        ]
    }

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        controller.update(fieldChangeData.dataFragment);
    }

    return <div style={{padding: 10}}><LCGUI schema={schema} onFieldChange={onFieldChange}/></div>
}
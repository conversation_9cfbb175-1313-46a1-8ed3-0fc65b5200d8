/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

@tdRowHeight: 0px;

.base-table-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  
  /* 表头区域 */
  .base-table-header-div {
    width: 100%;
    z-index: 2;
    background-color: inherit;
    
    .base-table-row-div {
      width: 100%;
      display: grid;
      height: 100%; /* 确保行高与父元素一致 */
    }
    
    .base-table-cell-div {
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin: 0;
      padding: 0 8px; /* 增加水平内边距 */
      display: flex;
      align-items: center; /* 垂直居中 */
      justify-content: flex-start; /* 默认左对齐，将由 textAlign 覆盖 */
      height: 100%; /* 确保高度占满行高 */
    }
    
    /* 水平对齐方式的样式覆盖 */
    .text-align-left {
      justify-content: flex-start;
    }
    
    .text-align-center {
      justify-content: center;
    }
    
    .text-align-right {
      justify-content: flex-end;
    }
  }
  
  /* 表体区域 */
  .base-table-body-div {
    flex: 1;
    width: 100%;
    overflow: hidden;
    position: relative;
    
    .base-table-body-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      backface-visibility: hidden;
      transform: translateZ(0);
      -webkit-backface-visibility: hidden;
      -webkit-transform: translateZ(0);
      will-change: transform;
      animation-timing-function: linear !important;
      animation-fill-mode: forwards;
    }
    
    .base-table-row-div {
      width: 100%;
      display: grid;
      height: 100%; /* 确保行高与父元素一致 */
    }
    
    .base-table-cell-div {
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin: 0;
      padding: 0 8px; /* 增加水平内边距 */
      display: flex;
      align-items: center; /* 垂直居中 */
      justify-content: flex-start; /* 默认左对齐，将由 textAlign 覆盖 */
      height: 100%; /* 确保高度占满行高 */

      /* 自定义模式下不应用默认对齐，让内容自己控制 */
      &.custom-mode {
        justify-content: unset !important;

        /* 确保内容能够完全控制布局 */
        > * {
          width: 100%;
          height: 100%;
          /* 不强制设置display，让自定义代码控制 */
        }
      }
    }
    
    /* 水平对齐方式的样式覆盖 */
    .text-align-left {
      justify-content: flex-start;
    }
    
    .text-align-center {
      justify-content: center;
    }
    
    .text-align-right {
      justify-content: flex-end;
    }
  }
}


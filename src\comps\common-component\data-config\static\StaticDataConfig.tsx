/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractDesignerController from "../../../../framework/core/AbstractDesignerController.ts";
import {useRef, useState} from "react";
import ObjectUtil from "../../../../utils/ObjectUtil.ts";
import {globalMessage} from "../../../../framework/message/GlobalMessage.tsx";
import {Control} from "../../../../json-schema/SchemaTypes.ts";
import {FieldChangeData, LCGUI} from "../../../../json-schema/LCGUI.tsx";
import {DataConfigType} from "../../../../designer/DesignerType.ts";
import globalVariableManager from "../../../../designer/manager/GlobalVariableManager.ts";
import DataTypeUtil from "../../../../utils/DataTypeUtil.ts";

export interface StaticDataConfigProps {
    controller: AbstractDesignerController;
    data: any;
}

export function StaticDataConfig(props: StaticDataConfigProps) {
    const {data, controller} = props;
    const dataSource = controller.getConfig()?.data as DataConfigType;
    const [filter, setFilter] = useState<string>(dataSource.staticDataFilter || 'function filter(data){\n\n\n\treturn data\n}');
    const [testResult, setTestResult] = useState<string>('');
    const [count, setCount] = useState(0);

    // 初始化时确保数据格式一致性
    const initializeStaticData = (rawData: any): string => {
        // 如果数据已经是JSON格式的字符串，直接返回
        if (typeof rawData === 'string') {
            // 检查是否是有效的JSON格式
            const parseResult = DataTypeUtil.strictParse(rawData);
            if (parseResult.success) {
                return rawData; // 已经是正确的JSON格式
            }
        }

        // 否则格式化为JSON格式
        return DataTypeUtil.formatForEditor(rawData, 2);
    };

    // 优先使用 rawStaticData，如果不存在则使用 staticData（向后兼容）
    const getRawStaticData = () => {
        return dataSource.rawStaticData !== undefined ? dataSource.rawStaticData : data;
    };

    const dataRef = useRef({
        staticData: initializeStaticData(getRawStaticData()),
        filter: dataSource.staticDataFilter || 'function filter(data){\n\n\n\treturn data\n}'
    });

    // 严格解析静态数据
    const parseStaticData = (data: any): {success: boolean, data?: any, error?: string} => {
        if (typeof data !== 'string') {
            return {success: true, data: data};
        }

        // 使用严格模式解析，要求符合JSON格式
        return DataTypeUtil.strictParse(data);
    };

    // 测试静态数据和过滤器
    const testStaticData = async () => {
        try {
            let rawData: any;

            // 解析静态数据
            const parseResult = parseStaticData(dataRef.current.staticData);
            if (!parseResult.success) {
                const errorResult = {
                    success: false,
                    error: '静态数据格式错误',
                    message: parseResult.error,
                    suggestion: '请确保数据符合JSON格式，字符串需要用引号包围',
                    timestamp: new Date().toISOString()
                };
                setTestResult(DataTypeUtil.formatForDisplay(errorResult, 2, true));
                return;
            }
            rawData = parseResult.data;

            let finalData = rawData;

            // 执行过滤器
            if (dataRef.current.filter && dataRef.current.filter.trim() !== '') {
                try {
                    // 使用GlobalVariableManager的过滤器执行方法，确保支持 ${GV::variableName} 格式
                    finalData = await globalVariableManager._parseAndExecuteFilter(
                        dataRef.current.filter,
                        rawData,
                        'static-data-filter' // 静态数据过滤器的虚拟ID
                    );
                } catch (filterError) {
                    const errorResult = {
                        success: false,
                        error: '过滤器执行错误',
                        message: (filterError as Error).message,
                        originalData: rawData,
                        timestamp: new Date().toISOString()
                    };
                    setTestResult(DataTypeUtil.formatForDisplay(errorResult, 2, true));
                    return;
                }
            }

            // 构建详细的测试结果
            const testResult = {
                success: true,
                originalData: rawData,
                filteredData: finalData,
                dataType: DataTypeUtil.getDataType(finalData),
                hasFilter: !!(dataRef.current.filter && dataRef.current.filter.trim() !== ''),
                timestamp: new Date().toISOString()
            };

            setTestResult(DataTypeUtil.formatForDisplay(testResult, 2, true));
            globalMessage.messageApi?.success('测试成功');
        } catch (error) {
            const errorResult = {
                success: false,
                error: '测试失败',
                message: (error as Error).message,
                timestamp: new Date().toISOString()
            };
            setTestResult(DataTypeUtil.formatForDisplay(errorResult, 2, true));
            globalMessage.messageApi?.error('测试失败');
        } finally {
            setCount(count + 1);
        }
    };

    // 保存静态数据配置
    const saveStaticData = async () => {
        try {
            let rawData: any;

            // 解析静态数据
            const parseResult = parseStaticData(dataRef.current.staticData);
            if (!parseResult.success) {
                globalMessage.messageApi?.error(`静态数据格式错误: ${parseResult.error}`);
                return;
            }
            rawData = parseResult.data;

            let finalData = rawData;

            // 执行过滤器
            if (dataRef.current.filter && dataRef.current.filter.trim() !== '') {
                try {
                    // 使用GlobalVariableManager的过滤器执行方法，确保支持 ${GV::variableName} 格式
                    finalData = await globalVariableManager._parseAndExecuteFilter(
                        dataRef.current.filter,
                        rawData,
                        'static-data-filter' // 静态数据过滤器的虚拟ID
                    );
                } catch (filterError) {
                    globalMessage.messageApi?.error(`过滤器执行错误: ${(filterError as Error).message}`);
                    return;
                }
            }

            // 保存配置并更新组件数据
            controller.update({
                data: {
                    rawStaticData: rawData,        // 保存原始数据到 rawStaticData
                    staticData: finalData,         // 保存处理后的数据到 staticData（数据中转站）
                    staticDataFilter: dataRef.current.filter
                }
            }, {reRender: false});

            controller.changeData(finalData);

            // 保存后重新计算引用计数（过滤器可能包含全局变量引用）
            globalVariableManager.recalculateAllReferenceCounts();

            globalMessage.messageApi?.success('配置已保存');
        } catch (error) {
            globalMessage.messageApi?.error(`保存失败: ${(error as Error).message}`);
        }
    };

    const schema: Control = {
        type: 'grid',
        config: {gridGap: '10px'},
        children: [
            {
                type: 'card-panel',
                label: '静态数据',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        key: 'staticData',
                        type: 'code-editor',
                        config: {
                            height: 300,
                            format: true
                        },
                        value: dataRef.current.staticData,
                    }
                ]
            },
            {
                type: 'card-panel',
                label: '过滤器',
                tip: '对静态数据进行处理的JavaScript函数',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        key: 'filter',
                        type: 'code-editor',
                        config: {
                            height: 200,
                            language: 'javascript',
                            format: true
                        },
                        value: filter,
                    }
                ]
            },
            {
                type: 'card-panel',
                label: '响应结果',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        id: 'testResult',
                        type: 'code-editor',
                        config: {
                            readonly: true,
                            height: 160,
                        },
                        reRender: true,
                        value: testResult,
                    }
                ]
            },
            {
                type: 'grid',
                config: {columns: 2, gridGap: '10px'},
                children: [
                    {
                        id: 'testStaticData',
                        type: 'button',
                        config: {
                            children: '测试',
                        }
                    },
                    {
                        id: 'saveStaticData',
                        type: 'button',
                        config: {
                            children: '保存'
                        }
                    }
                ]
            }
        ]
    }

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataFragment} = fieldChangeData;

        if (id === 'staticData') {
            dataRef.current.staticData = data;
        } else if (id === 'filter') {
            setFilter(data as string);
            dataRef.current.filter = data as string;
        } else if (id === 'testStaticData') {
            testStaticData();
            return;
        } else if (id === 'saveStaticData') {
            saveStaticData();
            return;
        } else if (id === 'testResult') {
            return; // 只读字段，不处理
        } else {
            dataRef.current = ObjectUtil.merge(dataRef.current, dataFragment);
        }

        setCount(count + 1);
    }


    return (
        <LCGUI schema={schema} onFieldChange={onFieldChange}/>
    );
}
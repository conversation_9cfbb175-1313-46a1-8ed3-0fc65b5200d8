server {
    listen       80;
    server_name  localhost;

    root /usr/app/light-chaser;
    index index.html;

    location / {
        try_files $uri $uri/ @router;
        index index.html;
    }

    location /api {
        proxy_pass http://localhost:8080;
    }

    location /static {
        proxy_pass http://localhost:8080;
    }

    location @router {
        rewrite ^.*$ /index.html last;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }

}
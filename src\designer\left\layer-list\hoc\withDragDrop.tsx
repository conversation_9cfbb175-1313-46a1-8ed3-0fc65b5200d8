/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, { useRef, useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { LayerDragItem, LayerDragTypes } from '../constants/DragTypes';
import layerListStore from '../LayerListStore';
import { LayerProps } from '../item/BaseLayer';

/**
 * 拖拽高阶组件，为图层项添加拖拽功能
 * @param WrappedComponent 被包装的组件
 * @param layerType 图层类型
 */
export const withDragDrop = (WrappedComponent: React.ComponentType<LayerProps>, layerType: 'layer' | 'group') => {
    // 使用forwardRef转发ref
    const DraggableLayer = React.forwardRef<any, LayerProps>((props, ref) => {
        const { compId, index, parentId, lock, hide } = props;
        const dragRef = useRef<HTMLDivElement>(null);
        const [dropPosition, setDropPosition] = useState<'top' | 'bottom' | null>(null);

        // 设置拖拽源
        const [{ isDragging }, drag] = useDrag(() => ({
            type: LayerDragTypes.LAYER,
            item: { id: compId, type: layerType, index, parentId } as LayerDragItem,
            collect: (monitor) => ({
                isDragging: !!monitor.isDragging(),
            }),
            canDrag: () => {
                // 锁定或隐藏的图层不能拖拽
                return !lock && !hide;
            },
        }), [compId, index, parentId, lock, hide]);

        // 设置放置目标
        const [{ isOver, canDrop }, drop] = useDrop(() => ({
            accept: LayerDragTypes.LAYER,
            collect: (monitor) => ({
                isOver: !!monitor.isOver(),
                canDrop: !!monitor.canDrop(),
            }),
            canDrop: (item: LayerDragItem) => {
                // 不能拖放到自己上面
                if (item.id === compId) return false;
                // 锁定或隐藏的图层不能作为目标
                if (lock || hide) return false;
                // 父级必须相同
                return item.parentId === parentId;
            },
            hover: (item: LayerDragItem, monitor) => {
                if (!dragRef.current || item.id === compId) return;

                // 获取拖拽目标的边界矩形
                const hoverBoundingRect = dragRef.current.getBoundingClientRect();
                // 获取垂直方向的中点
                const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
                // 获取鼠标位置
                const clientOffset = monitor.getClientOffset();
                if (!clientOffset) return;

                // 获取鼠标相对于悬停目标的位置
                const hoverClientY = clientOffset.y - hoverBoundingRect.top;

                // 根据鼠标位置判断插入位置
                if (hoverClientY < hoverMiddleY) {
                    setDropPosition('top');
                } else {
                    setDropPosition('bottom');
                }
            },
            drop: (item: LayerDragItem) => {
                if (item.id !== compId) {
                    const { moveLayer } = layerListStore;
                    moveLayer(item, { id: compId, type: layerType, index, parentId } as LayerDragItem, dropPosition);
                }
                setDropPosition(null);
            },
        }), [compId, index, parentId, lock, hide, dropPosition]);

        // 当拖拽结束时清理状态
        React.useEffect(() => {
            if (!isOver) {
                setDropPosition(null);
            }
        }, [isOver]);

        // 合并引用
        const dragDropRef = (node: HTMLDivElement) => {
            if (dragRef.current !== node) {
                (dragRef as React.MutableRefObject<HTMLDivElement | null>).current = node;
            }
            drag(drop(node));
        };

        // 传递拖拽相关的属性给被包装的组件
        return (
            <WrappedComponent
                {...props}
                isDragging={isDragging}
                isOver={isOver}
                canDrop={canDrop}
                dropPosition={dropPosition}
                dragRef={dragDropRef}
            />
        );
    });

    // 设置显示名称
    DraggableLayer.displayName = `WithDragDrop(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

    return DraggableLayer;
};

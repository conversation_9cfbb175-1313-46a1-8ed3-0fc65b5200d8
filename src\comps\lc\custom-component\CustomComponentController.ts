/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import { UpdateOptions } from "../../../framework/core/AbstractController";
import AbstractDesignerController from "../../../framework/core/AbstractDesignerController";
import ComponentUtil from "../../../utils/ComponentUtil";
import CustomComponent, { CustomComponentProps, CustomComponentRef } from "./CustomComponent";
import ObjectUtil from "../../../utils/ObjectUtil";
import BPExecutor from "../../../designer/blueprint/core/BPExecutor";
import { ThemeItemType } from "../../../designer/DesignerType";
import themeManager from "../../../designer/header/items/theme/ThemeManager";
import globalVariableManager from "../../../designer/manager/GlobalVariableManager";

/**
 * 自定义组件控制器
 * 继承自AbstractDesignerController，管理组件的生命周期
 */
export class CustomComponentController extends AbstractDesignerController<CustomComponentRef, CustomComponentProps> {
    /**
     * 全局变量订阅信息
     */
    private globalVariableSubscriptions: Array<{variableId: string, subscriberId: string}> = [];
    /**
     * 创建组件实例
     * @param container 组件容器
     * @param config 组件配置
     */
    async create(container: HTMLElement, config: CustomComponentProps): Promise<void> {
        this.config = config;
        this.container = container;

        // 设置默认主题（如果没有主题信息）
        if (!this.config.theme && themeManager.themeConfig && themeManager.themeConfig.length > 0) {
            this.config.theme = themeManager.themeConfig[0]; // 使用第一个主题作为默认主题
        }

        this.instance = await ComponentUtil.createAndRender<CustomComponentRef>(container, CustomComponent, config);
        // 设置默认点击事件
        this.registerEvent();

        // 订阅全局变量变化
        this.subscribeToGlobalVariables(config.customCode?.code || '');
    }

    /**
     * 销毁组件实例
     */
    destroy(): void {
        // 取消全局变量订阅
        this.globalVariableSubscriptions.forEach(({variableId, subscriberId}) => {
            globalVariableManager.unsubscribe(variableId, subscriberId);
        });
        this.globalVariableSubscriptions = [];

        this.instance = null;
        this.config = null;
    }

    /**
     * 获取组件配置
     * @returns 组件配置
     */
    getConfig(): CustomComponentProps | null {
        return this.config;
    }

    /**
     * 更新组件配置
     * @param config 新的组件配置
     * @param upOp 更新选项
     */
    update(config: CustomComponentProps, upOp?: UpdateOptions | undefined): void {
        const oldCode = this.config?.customCode?.code || '';

        // 手动合并配置，确保自定义代码被正确保存
        if (config?.customCode && this.config) {
            if (!this.config.customCode) {
                this.config.customCode = { code: '', isPreview: false };
            }

            // 合并所有customCode字段
            if (config.customCode.code !== undefined) {
                this.config.customCode.code = config.customCode.code;
            }
            if (config.customCode.isPreview !== undefined) {
                this.config.customCode.isPreview = config.customCode.isPreview;
            }
            if (config.customCode.previewTimestamp !== undefined) {
                this.config.customCode.previewTimestamp = config.customCode.previewTimestamp;
            }
        } else {
            // 其他配置使用默认合并策略
            this.config = ObjectUtil.merge(this.config, config);
        }

        // 如果自定义代码发生变化，重新订阅全局变量
        const newCode = this.config?.customCode?.code || '';
        if (oldCode !== newCode) {
            this.subscribeToGlobalVariables(newCode);
        }

        upOp = upOp || { reRender: true };
        if (upOp.reRender && this.instance) {
            this.instance.updateConfig(this.config!);
        }
    }

    /**
     * 注册组件事件
     */
    registerEvent() {
        if (this.instance && this.config?.base?.id) {
            const nodeId = this.config.base.id;
            this.instance.setEventHandler({
                click: () => BPExecutor.triggerComponentEvent(nodeId, "click", this.config)
            });
        }
    }

    /**
     * 更新组件数据
     * @param data 新的数据
     */
    changeData(data: any) {
        if (this.config && this.config.data) {
            this.config.data.staticData = data;
            if (this.instance) {
                this.instance.updateConfig(this.config);
            }
        }
    }

    /**
     * 更新组件主题
     * @param newTheme 新主题
     */
    updateTheme(newTheme: ThemeItemType): void {
        if (!newTheme || !this.config) return;

        // 将主题信息存储到配置中，供自定义代码使用
        if (!this.config.customCode) {
            this.config.customCode = { code: '', isPreview: false };
        }

        // 将主题颜色添加到配置中
        (this.config as any).theme = newTheme;

        // 重新渲染组件以应用主题
        if (this.instance) {
            this.instance.updateConfig(this.config);
        }
    }

    /**
     * 订阅全局变量变化
     */
    private subscribeToGlobalVariables(customCode: string): void {
        if (!customCode) return;

        // 取消现有的全局变量订阅
        this.globalVariableSubscriptions.forEach(({variableId, subscriberId}) => {
            globalVariableManager.unsubscribe(variableId, subscriberId);
        });
        this.globalVariableSubscriptions = [];

        // 解析代码中引用的全局变量
        const referencedVariables = this.extractGlobalVariableReferences(customCode);

        // 订阅这些变量的变化
        referencedVariables.forEach(variableName => {
            const variableId = this.findVariableIdByName(variableName);
            if (variableId) {
                const componentId = this.config?.base?.id || `custom-component-${Date.now()}`;
                const subscriberId = `${componentId}-${variableId}`;

                globalVariableManager.subscribe(variableId, subscriberId, () => {
                    // 全局变量变化时重新执行自定义代码
                    this.triggerComponentRerender();
                });

                this.globalVariableSubscriptions.push({variableId, subscriberId});
            }
        });
    }

    /**
     * 解析代码中引用的全局变量
     */
    private extractGlobalVariableReferences(code: string): string[] {
        const referencedVariables: string[] = [];

        // 匹配 globalVars.get('variableName') 或 globalVars.get("variableName") 格式
        const globalVarsRegex = /globalVars\.get\(['"]([^'"]+)['"]\)/g;
        let match;

        while ((match = globalVarsRegex.exec(code)) !== null) {
            const variableName = match[1];
            if (!referencedVariables.includes(variableName)) {
                referencedVariables.push(variableName);
            }
        }

        return referencedVariables;
    }

    /**
     * 根据变量名查找变量ID
     */
    private findVariableIdByName(variableName: string): string | null {
        const definitions = globalVariableManager.getAllVariableDefinitions();
        const definition = definitions.find(def => def.name === variableName);
        return definition ? definition.id : null;
    }

    /**
     * 触发组件重新渲染
     */
    private triggerComponentRerender(): void {
        if (this.instance && this.config) {
            // 如果组件当前处于预览状态，强制重新执行代码
            if (this.config.customCode?.isPreview) {
                // 更新时间戳强制重新渲染
                this.config.customCode.previewTimestamp = Date.now();
                this.instance.updateConfig(this.config);
            }
        }
    }

    /**
     * 建立全局变量订阅关系（用于数据源配置）
     * 这个方法会被GlobalVariableDataConfig调用
     */
    public setupGlobalVariableSubscription = async (variableId?: string) => {
        if (!variableId) return;

        const {data} = this.config! as any;
        const globalVariableFilter = data?.globalVariableFilter;

        // 获取全局变量定义
        const definition = globalVariableManager.getVariableDefinition(variableId);
        if (!definition) {
            console.error(`全局变量 ${variableId} 不存在`);
            return;
        }

        // 如果全局变量开启了主动渲染，订阅变量变化
        if (definition?.isActiveRendering) {
            const componentId = this.config?.base?.id;
            if (componentId) {
                globalVariableManager.subscribe(variableId, componentId, async (newValue: any) => {
                    // 订阅回调中也需要应用过滤器
                    let finalValue = newValue;
                    if (globalVariableFilter && globalVariableFilter.trim() !== '') {
                        try {
                            // 使用GlobalVariableManager的过滤器执行方法，确保一致性
                            finalValue = await globalVariableManager._parseAndExecuteFilter(
                                globalVariableFilter,
                                newValue,
                                variableId
                            );
                        } catch (error) {
                            console.error('全局变量过滤器执行错误:', error);
                            // 过滤器执行失败时使用原始值
                        }
                    }
                    this.changeData(finalValue);

                    // 如果组件处于预览状态，也要触发重新渲染
                    this.triggerComponentRerender();
                });
            }
        }
    };
}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import GlobalVariableManager from "../designer/manager/GlobalVariableManager.ts";

/**
 * 全局变量解析器
 * 负责解析和替换文本中的全局变量引用
 */
export default class GlobalVariableParser {
    
    /**
     * 全局变量引用的正则表达式
     * 匹配格式: ${GV::variableName}
     */
    private static readonly GLOBAL_VARIABLE_REGEX = /\$\{GV::([^}]+)\}/g;

    /**
     * 解析并替换文本中的全局变量引用
     * @param text 要解析的文本
     * @param manager 全局变量管理器实例
     * @param callStack 调用栈，用于检测循环依赖
     * @param context 使用上下文，决定值的格式化方式
     * @returns 替换后的文本
     */
    public static async parseAndReplace(text: string, manager: GlobalVariableManager, callStack?: Set<string>, context: 'javascript' | 'text' = 'text'): Promise<string> {
        if (!text || typeof text !== 'string') {
            return text;
        }

        // 重置正则表达式的lastIndex
        GlobalVariableParser.GLOBAL_VARIABLE_REGEX.lastIndex = 0;

        let result = text;
        let match: RegExpExecArray | null;
        const replacements: Array<{original: string, replacement: string}> = [];

        // 查找所有匹配项
        while ((match = GlobalVariableParser.GLOBAL_VARIABLE_REGEX.exec(text)) !== null) {
            const fullMatch = match[0]; // ${GV::variableName}
            const variableName = match[1]; // variableName

            try {
                // 根据变量名查找对应的变量ID
                const variableId = GlobalVariableParser.findVariableIdByName(variableName, manager);

                if (variableId) {
                    // 检查循环依赖
                    if (callStack && callStack.has(variableId)) {
                        console.warn(`在文本解析中检测到循环依赖: ${Array.from(callStack).join(' -> ')} -> ${variableId}`);
                        replacements.push({
                            original: fullMatch,
                            replacement: `[CIRCULAR:${variableName}]`
                        });
                        continue;
                    }

                    // 获取变量值
                    const variableValue = manager.getVariableValue(variableId);

                    if (variableValue !== undefined && variableValue !== null) {
                        // 根据上下文将值转换为字符串
                        const stringValue = context === 'javascript'
                            ? GlobalVariableParser.valueToJavaScriptString(variableValue)
                            : GlobalVariableParser.valueToTextString(variableValue);
                        replacements.push({
                            original: fullMatch,
                            replacement: stringValue
                        });
                    } else {
                        // 变量值为空，检查是否有错误状态
                        const errorState = manager.errorStates.get(variableId);
                        if (errorState) {
                            replacements.push({
                                original: fullMatch,
                                replacement: `[ERROR:${errorState}]`
                            });
                        } else {
                            // 变量值为空，替换为空字符串
                            replacements.push({
                                original: fullMatch,
                                replacement: ''
                            });
                        }
                    }
                } else {
                    // 变量不存在，替换为错误标记
                    replacements.push({
                        original: fullMatch,
                        replacement: `[ERROR:变量'${variableName}'不存在]`
                    });
                }
            } catch (error) {
                // 处理错误，替换为错误标记
                const errorMessage = error instanceof Error ? error.message : '未知错误';
                console.error(`解析全局变量引用 ${variableName} 时出错:`, errorMessage);
                replacements.push({
                    original: fullMatch,
                    replacement: `[ERROR:${errorMessage}]`
                });
            }
        }

        // 执行所有替换
        for (const replacement of replacements) {
            result = result.replace(replacement.original, replacement.replacement);
        }

        return result;
    }

    /**
     * 根据变量名查找变量ID
     * @param variableName 变量名
     * @param manager 全局变量管理器实例
     * @returns 变量ID，如果未找到返回null
     */
    private static findVariableIdByName(variableName: string, manager: GlobalVariableManager): string | null {
        const definitions = manager.getAllVariableDefinitions();
        
        for (const definition of definitions) {
            if (definition.name === variableName) {
                return definition.id;
            }
        }
        
        return null;
    }

    /**
     * 将变量值转换为JavaScript代码中可用的字符串表示
     * @param value 变量值
     * @returns JavaScript代码中的字符串表示
     */
    private static valueToJavaScriptString(value: any): string {
        if (value === null || value === undefined) {
            return 'null';
        }

        // 在JavaScript代码中，所有类型都使用JSON.stringify来确保正确表示
        try {
            return JSON.stringify(value);
        } catch (error) {
            // 如果无法序列化，返回null
            console.warn('GlobalVariableParser: 无法序列化变量值:', value, error);
            return 'null';
        }
    }

    /**
     * 将变量值转换为文本替换中使用的字符串表示（如URL、普通文本等）
     * @param value 变量值
     * @returns 文本替换中的字符串表示
     */
    private static valueToTextString(value: any): string {
        if (value === null || value === undefined) {
            return '';
        }

        if (typeof value === 'string') {
            // 在文本替换中，字符串直接返回内容，不加引号
            return value;
        }

        if (typeof value === 'number' || typeof value === 'boolean') {
            return String(value);
        }

        if (typeof value === 'object') {
            try {
                // 对象和数组转换为JSON字符串（不含外层引号）
                return JSON.stringify(value);
            } catch {
                return '[Object]';
            }
        }

        return String(value);
    }

    /**
     * 将变量值转换为字符串（向后兼容方法）
     * @param value 变量值
     * @returns 字符串表示
     */
    private static valueToString(value: any): string {
        // 默认使用JavaScript格式，保持向后兼容
        return GlobalVariableParser.valueToJavaScriptString(value);
    }

    /**
     * 检查文本中是否包含全局变量引用
     * @param text 要检查的文本
     * @returns 是否包含全局变量引用
     */
    public static hasGlobalVariableReference(text: string): boolean {
        if (!text || typeof text !== 'string') {
            return false;
        }
        
        GlobalVariableParser.GLOBAL_VARIABLE_REGEX.lastIndex = 0;
        return GlobalVariableParser.GLOBAL_VARIABLE_REGEX.test(text);
    }

    /**
     * 提取文本中所有的全局变量引用
     * @param text 要分析的文本
     * @returns 变量名数组
     */
    public static extractGlobalVariableReferences(text: string): string[] {
        if (!text || typeof text !== 'string') {
            return [];
        }

        const variableNames: string[] = [];
        GlobalVariableParser.GLOBAL_VARIABLE_REGEX.lastIndex = 0;
        
        let match: RegExpExecArray | null;
        while ((match = GlobalVariableParser.GLOBAL_VARIABLE_REGEX.exec(text)) !== null) {
            const variableName = match[1];
            if (!variableNames.includes(variableName)) {
                variableNames.push(variableName);
            }
        }

        return variableNames;
    }

    /**
     * 递归解析对象中的全局变量引用
     * @param obj 要解析的对象
     * @param manager 全局变量管理器实例
     * @param callStack 调用栈，用于检测循环依赖
     * @param context 使用上下文，决定值的格式化方式
     * @returns 解析后的对象
     */
    public static async parseObjectAndReplace(obj: any, manager: GlobalVariableManager, callStack?: Set<string>, context: 'javascript' | 'text' = 'text'): Promise<any> {
        if (obj === null || obj === undefined) {
            return obj;
        }

        if (typeof obj === 'string') {
            // 字符串直接解析
            return await GlobalVariableParser.parseAndReplace(obj, manager, callStack, context);
        }

        if (typeof obj === 'number' || typeof obj === 'boolean') {
            // 数字和布尔值直接返回
            return obj;
        }

        if (Array.isArray(obj)) {
            // 数组递归处理每个元素
            const result = [];
            for (const item of obj) {
                result.push(await GlobalVariableParser.parseObjectAndReplace(item, manager, callStack, context));
            }
            return result;
        }

        if (typeof obj === 'object') {
            // 对象递归处理每个属性
            const result: any = {};
            for (const [key, value] of Object.entries(obj)) {
                // 键名也可能包含全局变量引用
                const parsedKey = await GlobalVariableParser.parseAndReplace(key, manager, callStack, context);
                result[parsedKey] = await GlobalVariableParser.parseObjectAndReplace(value, manager, callStack, context);
            }
            return result;
        }

        // 其他类型直接返回
        return obj;
    }
}

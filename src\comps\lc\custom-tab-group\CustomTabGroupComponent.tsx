/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {forwardRef, useImperativeHandle} from 'react';
import {CustomTabGroupComponentProps} from './CustomTabGroupTypes';
import {DesignerMode} from '../../../designer/DesignerType';

export interface CustomTabGroupComponentRef {
    // 暂时不需要特殊的ref方法，通过重新创建组件来更新
}

const CustomTabGroupComponent = forwardRef<CustomTabGroupComponentRef, CustomTabGroupComponentProps>((props, ref) => {
    useImperativeHandle(ref, () => ({}));

    // 检查当前模式，在预览模式下不显示控制器组件
    const isViewMode = window.LC_ENV?.mode === DesignerMode.VIEW;

    if (isViewMode) {
        // 预览模式下返回空的不可见元素
        return <div style={{ display: 'none' }} />;
    }

    // 在设计器中显示为虚线框占位符
    return (
        <div
            style={{
                width: '100%',
                height: '100%',
                border: '2px dashed #666',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999',
                fontSize: '14px',
                backgroundColor: 'rgba(255, 255, 255, 0.05)',
                borderRadius: '4px',
                gap: '8px'
            }}
        >
            <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
                自定义Tab组控制器
            </div>
            <div style={{ fontSize: '12px', textAlign: 'center', lineHeight: '1.4' }}>
                双击配置Tab项<br/>
                子Tab组件将显示在画布上
            </div>
        </div>
    );
});

CustomTabGroupComponent.displayName = 'CustomTabGroupComponent';

export default CustomTabGroupComponent;

/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractDesignerController from "../../../framework/core/AbstractDesignerController";
import {UpdateOptions} from "../../../framework/core/AbstractController";
import ComponentUtil from "../../../utils/ComponentUtil";
import ChildTabComponent, {ChildTabComponentRef} from "./ChildTabComponent";
import {ChildTabComponentProps} from "./CustomTabGroupTypes";
import ObjectUtil from "../../../utils/ObjectUtil";
import layerManager from "../../../designer/manager/LayerManager";

export class ChildTabController extends AbstractDesignerController<ChildTabComponentRef, ChildTabComponentProps> {

    async create(container: HTMLElement, config: ChildTabComponentProps): Promise<void> {
        this.config = config;
        this.container = container;
        this.instance = await ComponentUtil.createAndRender<ChildTabComponentRef>(container, ChildTabComponent, config);
    }

    destroy(): void {
        // 在销毁前，通知父Tab组移除此Tab的配置
        this.notifyParentTabGroupOnDestroy();

        this.instance = null;
        this.config = null;
        this.container = null;
    }

    /**
     * 通知父Tab组移除此Tab的配置
     */
    private notifyParentTabGroupOnDestroy(): void {
        // 如果是被父组件删除的，不需要通知父组件
        if ((this as any)._isBeingDeletedByParent) {
            return;
        }

        if (!this.config?.style?.parentId) {
            return;
        }

        const {compController} = layerManager;
        const parentController = compController[this.config.style.parentId];

        if (parentController && typeof (parentController as any).removeChildTab === 'function') {
            // 调用父控制器的移除方法
            (parentController as any).removeChildTab(this.config.base.id);
        }
    }

    getConfig(): ChildTabComponentProps | null {
        return this.config;
    }

    update(config: ChildTabComponentProps, upOp?: UpdateOptions): void {
        this.config = ObjectUtil.merge(this.config, config);

        upOp = upOp || {reRender: true};

        // 使用组件的updateConfig方法来更新状态
        if (upOp.reRender && this.instance) {
            this.instance.updateConfig(this.config);
        }
    }
}

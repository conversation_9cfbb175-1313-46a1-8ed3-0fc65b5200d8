/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.lc-login {
  color: #c7c7c7;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000000ba;
  background-image: url("./bg.png");
  background-position: -110px 162px;
  background-size: cover;

  .lc-login-container {
    z-index: 1;
    width: 390px;
    box-shadow: 3px 3px 20px #364965;
    background-color: #1e2938;
    border-radius: 5px;
    border-top: 1px solid #405878;
    border-bottom: 1px solid #1d2836;
    padding: 40px 0 40px 0;

    .login-header {
      text-align: center;
      font-size: 16px;

      .header-des {
        margin-bottom: 20px;
        color: #b5b5b5;
      }
    }

    .login-body {
      padding: 20px;

      .login-item {
        height: 50px;
        margin-bottom: 5px;

        .ant-input-affix-wrapper {
          background-color: #0c1b2b;
          border-radius: 7px;

          .ant-input-prefix {
            margin-inline-end: 6px
          }

          input::placeholder {
            font-size: 14px; /* 你想要的字体大小 */
          }

          input:-webkit-autofill,
          input:-webkit-autofill:hover,
          input:-webkit-autofill:focus,
          input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 1000px #0c1b2b inset !important; /* 背景颜色 */
            -webkit-text-fill-color: #cfcfcf !important; /* 文本颜色 */
          }
        }

        button {
          width: 100%;
          border-radius: 7px;
        }
      }

      .service-terms {
        text-align: center;
        margin-top: 15px;
        font-size: 14px;
        color: #a7a7a7;

        span {
          cursor: pointer;
          color: #2b95ff;
        }
      }
    }
  }
}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */



/**
 * 环境检测工具类
 */
export default class EnvironmentUtil {
    
    /**
     * 检查是否为开发环境
     * @returns {boolean} 是否为开发环境
     */
    public static isDevelopment(): boolean {
        // 检查 NODE_ENV 环境变量
        if (process.env.NODE_ENV === 'development') {
            return true;
        }
        
        // 检查自定义环境变量
        if (process.env.REACT_APP_ENV === 'development') {
            return true;
        }
        
        // 检查是否在本地开发服务器上运行
        const hostname = window.location.hostname;
        const port = window.location.port;
        
        // 常见的本地开发地址
        const localHosts = ['localhost', '127.0.0.1', '0.0.0.0'];
        if (localHosts.includes(hostname)) {
            return true;
        }
        
        // 常见的开发端口
        const devPorts = ['3000', '3001', '8080', '8081', '5173', '4173'];
        if (devPorts.includes(port)) {
            return true;
        }
        
        // 检查是否有开发工具标识
        if (window.location.search.includes('debug=true')) {
            return true;
        }
        
        // 检查是否有 React DevTools
        if (typeof window !== 'undefined' && (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否为生产环境
     * @returns {boolean} 是否为生产环境
     */
    public static isProduction(): boolean {
        return process.env.NODE_ENV === 'production' && !this.isDevelopment();
    }
    
    /**
     * 检查是否为测试环境
     * @returns {boolean} 是否为测试环境
     */
    public static isTest(): boolean {
        return process.env.NODE_ENV === 'test';
    }
    
    /**
     * 获取当前环境名称
     * @returns {string} 环境名称
     */
    public static getEnvironment(): string {
        if (this.isDevelopment()) {
            return 'development';
        }
        if (this.isProduction()) {
            return 'production';
        }
        if (this.isTest()) {
            return 'test';
        }
        return 'unknown';
    }
    
    /**
     * 检查是否启用调试模式
     * @returns {boolean} 是否启用调试模式
     */
    public static isDebugEnabled(): boolean {
        // 开发环境默认启用调试
        if (this.isDevelopment()) {
            return true;
        }
        
        // 检查 URL 参数
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('debug') === 'true') {
            return true;
        }
        
        // 检查 localStorage 设置
        try {
            const debugSetting = localStorage.getItem('light-chaser-debug');
            if (debugSetting === 'true') {
                return true;
            }
        } catch (error) {
            // localStorage 可能不可用
        }
        
        return false;
    }
    
    /**
     * 启用或禁用调试模式（通过 localStorage）
     * @param {boolean} enabled 是否启用
     */
    public static setDebugEnabled(enabled: boolean): void {
        try {
            if (enabled) {
                localStorage.setItem('light-chaser-debug', 'true');
            } else {
                localStorage.removeItem('light-chaser-debug');
            }
        } catch (error) {
            console.warn('无法设置调试模式:', error);
        }
    }
    
    /**
     * 获取环境信息
     * @returns {object} 环境信息对象
     */
    public static getEnvironmentInfo(): {
        environment: string;
        isDevelopment: boolean;
        isProduction: boolean;
        isTest: boolean;
        isDebugEnabled: boolean;
        hostname: string;
        port: string;
        protocol: string;
        userAgent: string;
    } {
        return {
            environment: this.getEnvironment(),
            isDevelopment: this.isDevelopment(),
            isProduction: this.isProduction(),
            isTest: this.isTest(),
            isDebugEnabled: this.isDebugEnabled(),
            hostname: window.location.hostname,
            port: window.location.port,
            protocol: window.location.protocol,
            userAgent: navigator.userAgent
        };
    }
    
    /**
     * 获取调试配置
     */
    private static getDebugConfigSafely() {
        try {
            const { getDebugConfig } = require('../config');
            return getDebugConfig();
        } catch (error) {
            // 如果配置模块还未初始化，使用默认行为
            return {
                enableConsoleDebugTools: this.isDevelopment()
            };
        }
    }

    /**
     * 初始化环境检测（在应用启动时调用）
     */
    public static initialize(): void {
        const debugConfig = this.getDebugConfigSafely();

        // 根据配置在开发模式下添加全局调试方法
        if (this.isDevelopment() && debugConfig.enableConsoleDebugTools) {
            (window as any).EnvironmentUtil = this;
            (window as any).enableDebugMode = () => this.setDebugEnabled(true);
            (window as any).disableDebugMode = () => this.setDebugEnabled(false);
        }
    }
}

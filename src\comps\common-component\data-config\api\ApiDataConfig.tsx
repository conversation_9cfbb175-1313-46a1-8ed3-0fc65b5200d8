/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractDesignerController from "../../../../framework/core/AbstractDesignerController.ts";
import {APIConfig} from "../../../../designer/DesignerType.ts";
import {useRef, useState} from "react";
import ObjectUtil from "../../../../utils/ObjectUtil.ts";
import {Control} from "../../../../json-schema/SchemaTypes.ts";
import {globalMessage} from "../../../../framework/message/GlobalMessage.tsx";
import {FieldChangeData, LCGUI} from "../../../../json-schema/LCGUI.tsx";
import FetchUtil from "../../../../utils/FetchUtil.ts";
import globalVariableManager from "../../../../designer/manager/GlobalVariableManager.ts";

export interface ApiDataConfigProps {
    controller: AbstractDesignerController;
    data: APIConfig;
}

export function ApiDataConfig(props: ApiDataConfigProps) {
    const {data, controller} = props;
    const dataRef = useRef<APIConfig>(ObjectUtil.merge({
        url: '',
        method: 'get',
        header: {},
        params: {},
        autoFlush: false,
        frequency: 5,
        filter: undefined,
    }, data));
    const apiTestResRef = useRef<string>("")
    const [count, setCount] = useState(0);

    const schema: Control = {
        type: 'grid',
        config: {gridGap: '10px'},
        children: [
            {
                key: 'url',
                type: 'input',
                label: '接口地址',
                value: dataRef.current?.url || '',
            },
            {
                key: 'method',
                label: '请求方式',
                type: 'select',
                value: dataRef.current?.method,
                config: {
                    options: [
                        {value: 'get', label: 'GET'},
                        {value: 'post', label: 'POST'},
                    ]
                }
            },
            {
                type: 'grid',
                label: '自动更新',
                config: {columns: 8},
                children: [
                    {
                        key: 'autoFlush',
                        type: 'checkbox',
                        value: !!dataRef.current?.autoFlush,
                    },
                    {
                        key: 'frequency',
                        type: 'number-input',
                        config: {
                            min: 5,
                            containerStyle: {
                                gridColumn: '2/9',
                            }
                        },
                        value: dataRef.current?.frequency || 5,
                    },
                ]
            },
            {

                type: 'card-panel',
                label: '请求头',
                tip: '请求头信息，json格式',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        id: 'header',
                        key: 'header',
                        type: 'code-editor',
                        config: {
                            height: 100,
                        },
                        value: JSON.stringify(dataRef.current?.header, null, 2) || '',
                    }
                ]
            },
            {
                type: 'card-panel',
                tip: '请求参数，json格式',
                label: '请求参数',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        id: 'params',
                        key: 'params',
                        type: 'code-editor',
                        config: {
                            height: 100,
                        },
                        value: JSON.stringify(dataRef.current?.params, null, 2) || '',
                    }
                ]
            },
            {
                type: 'card-panel',
                label: '过滤器',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        key: 'filter',
                        type: 'code-editor',
                        config: {
                            height: 200,
                            language: 'javascript'
                        },
                        value: dataRef.current?.filter || "function filter(data){\n\n\n\treturn data\n}",
                    }
                ]
            },
            {
                type: 'card-panel',
                label: '响应结果',
                config: {
                    contentStyle: {padding: 0}
                },
                children: [
                    {
                        id: 'apiTestRes',
                        type: 'code-editor',
                        config: {
                            readonly: true,
                            height: 160,
                        },
                        reRender: true,
                        value: apiTestResRef.current,
                    }
                ]
            },
            {
                type: 'grid',
                config: {
                    columns: 2,
                    gap: 8
                },
                children: [
                    {
                        id: 'testApi',
                        type: 'button',
                        config: {
                            children: '测试',
                            type: 'default'
                        }
                    },
                    {
                        id: 'saveApi',
                        type: 'button',
                        config: {
                            children: '保存',
                            type: 'primary'
                        }
                    }
                ]
            },
        ]

    }


    const validate = () => {
        if (!dataRef.current?.url) {
            globalMessage.messageApi?.error('接口地址不能为空');
            return false;
        }
        if (!dataRef.current?.method) {
            globalMessage.messageApi?.error('请求方式不能为空');
            return false;
        }
        if (typeof dataRef.current.header === 'string') {
            const header = ObjectUtil.stringToJsObj(dataRef.current.header)
            if (!header) {
                globalMessage.messageApi?.error('请求头不符合json格式');
                return false;
            } else
                dataRef.current.header = header;
        }
        if (typeof dataRef.current.params === 'string') {
            const param = ObjectUtil.stringToJsObj(dataRef.current.params)
            if (!param) {
                globalMessage.messageApi?.error('请求参数不符合json格式');
                return false;
            } else
                dataRef.current.params = param;
        }
        return true;
    }

    // 测试API接口
    const testApi = async () => {
        if (!validate()) return;

        try {
            const {params, header, url, method, filter} = dataRef.current!;

            // 解析URL中的全局变量引用
            const GlobalVariableParser = (await import('../../../../utils/GlobalVariableParser')).default;
            const parsedUrl = await GlobalVariableParser.parseAndReplace(url!, globalVariableManager, undefined, 'text');

            // 解析请求头和参数中的全局变量引用
            const parsedHeaders = await GlobalVariableParser.parseObjectAndReplace(header || {}, globalVariableManager, undefined, 'text');
            const parsedParams = await GlobalVariableParser.parseObjectAndReplace(params || {}, globalVariableManager, undefined, 'text');

            console.log('原始URL:', url);
            console.log('解析后URL:', parsedUrl);
            console.log('原始Headers:', header);
            console.log('解析后Headers:', parsedHeaders);
            console.log('原始Params:', params);
            console.log('解析后Params:', parsedParams);

            // 如果 URL 已经包含查询参数且 params 为空，则不传递 params 避免重复添加问号
            const shouldPassParams = parsedParams && Object.keys(parsedParams).length > 0;
            const res = await FetchUtil.doRequestNativeResult(
                parsedUrl,
                method!,
                parsedHeaders,
                shouldPassParams ? parsedParams : undefined
            );

            if (res) {
                let finalRes = res;
                if (filter && filter !== '') {
                    // 使用GlobalVariableManager的过滤器执行方法，确保支持 ${GV::variableName} 格式
                    finalRes = await globalVariableManager._parseAndExecuteFilter(
                        filter,
                        res,
                        'api-data-filter' // API数据过滤器的虚拟ID
                    );
                }
                apiTestResRef.current = JSON.stringify(finalRes, null, 2);
                globalMessage.messageApi?.success('API测试成功');
            } else {
                apiTestResRef.current = JSON.stringify({msg: '请求错误'}, null, 2);
                globalMessage.messageApi?.error('API请求失败');
            }
        } catch (error) {
            apiTestResRef.current = JSON.stringify({
                error: 'API测试失败',
                message: (error as Error).message
            }, null, 2);
            globalMessage.messageApi?.error(`API测试失败: ${(error as Error).message}`);
        } finally {
            setCount(count + 1);
        }
    };

    // 保存API配置
    const saveApi = async () => {
        if (!validate()) return;

        try {
            const {params, header, url, method, filter} = dataRef.current!;

            // 解析URL中的全局变量引用
            const GlobalVariableParser = (await import('../../../../utils/GlobalVariableParser')).default;
            const parsedUrl = await GlobalVariableParser.parseAndReplace(url!, globalVariableManager, undefined, 'text');

            // 解析请求头和参数中的全局变量引用
            const parsedHeaders = await GlobalVariableParser.parseObjectAndReplace(header || {}, globalVariableManager, undefined, 'text');
            const parsedParams = await GlobalVariableParser.parseObjectAndReplace(params || {}, globalVariableManager, undefined, 'text');

            // 如果 URL 已经包含查询参数且 params 为空，则不传递 params 避免重复添加问号
            const shouldPassParams = parsedParams && Object.keys(parsedParams).length > 0;
            const res = await FetchUtil.doRequestNativeResult(
                parsedUrl,
                method!,
                parsedHeaders,
                shouldPassParams ? parsedParams : undefined
            );

            if (res) {
                let finalRes = res;
                if (filter && filter !== '') {
                    // 使用GlobalVariableManager的过滤器执行方法，确保支持 ${GV::variableName} 格式
                    finalRes = await globalVariableManager._parseAndExecuteFilter(
                        filter,
                        res,
                        'api-data-filter' // API数据过滤器的虚拟ID
                    );
                }

                // 更新组件配置和数据
                controller.update({data: {apiData: dataRef.current, staticData: finalRes}}, {reRender: false});
                controller.changeData(finalRes);
                globalMessage.messageApi?.success('API配置保存成功');
            } else {
                // 即使请求失败也保存配置
                controller.update({data: {apiData: dataRef.current}}, {reRender: false});
                globalMessage.messageApi?.warning('配置项已保存，但数据未成功刷新');
            }

            // 保存后重新计算引用计数（URL和过滤器可能包含全局变量引用）
            globalVariableManager.recalculateAllReferenceCounts();
        } catch (error) {
            // 即使请求失败也保存配置
            controller.update({data: {apiData: dataRef.current}}, {reRender: false});
            globalMessage.messageApi?.error(`保存失败: ${(error as Error).message}`);

            // 保存后重新计算引用计数
            globalVariableManager.recalculateAllReferenceCounts();
        } finally {
            setCount(count + 1);
        }
    };

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {reRender, id, dataFragment} = fieldChangeData;
        if (id === 'testApi') {
            testApi();
        } else if (id === 'saveApi') {
            saveApi();
        } else if (id === 'apiTestRes') {
            return;
        } else {
            dataRef.current = ObjectUtil.merge(dataRef.current, dataFragment);
        }
        if (reRender) {
            setCount(count + 1);
        }
    }

    return (
        <LCGUI schema={schema} onFieldChange={onFieldChange}/>
    );
}
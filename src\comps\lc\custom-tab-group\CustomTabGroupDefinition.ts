/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractDesignerDefinition from "../../../framework/core/AbstractDesignerDefinition";
import {BaseInfoType, EventInfo, ActionInfo, MenuToConfigMappingType} from "../../../framework/core/AbstractDefinition";
import {ClazzTemplate} from "../../common-component/CommonTypes";
import {MenuInfo} from "../../../designer/right/MenuType";
import {CustomTabGroupController} from "./CustomTabGroupController";
import {CustomTabGroupComponentProps} from "./CustomTabGroupTypes";
import BaseInfo from "../../common-component/base-info/BaseInfo";
import CustomTabGroupConfig from "./CustomTabGroupConfig";
// 暂时使用base-text的图片作为占位符
import customTabGroupImg from '../base-text/base-text.png';

export default class CustomTabGroupDefinition extends AbstractDesignerDefinition<CustomTabGroupController, CustomTabGroupComponentProps> {
    
    getBaseInfo(): BaseInfoType {
        return {
            compName: "自定义Tab组",
            compKey: "CustomTabGroup",
            categorize: "interaction",
        };
    }

    getChartImg(): string | null {
        return customTabGroupImg;
    }

    getController(): ClazzTemplate<CustomTabGroupController> | null {
        return CustomTabGroupController;
    }

    getInitConfig(): CustomTabGroupComponentProps {
        return {
            base: {
                id: "",
                name: '自定义Tab组',
                type: 'CustomTabGroup',
            },
            style: {
                tabs: [],
                activeTabValue: null,
            },
        };
    }

    getMenuList(): Array<MenuInfo> {
        return super.getMenuList().filter((menu) => 
            menu.key !== 'data' && 
            menu.key !== 'theme' && 
            menu.key !== 'animation' && 
            menu.key !== 'filter' &&
            menu.key !== 'mapping'
        );
    }

    getMenuToConfigContentMap(): MenuToConfigMappingType {
        return {
            base: BaseInfo,
            style: CustomTabGroupConfig,
        };
    }

    getEventList(): EventInfo[] {
        return [
            ...super.getEventList(),
            {
                id: "onValueChanged",
                name: "值变化时",
            }
        ];
    }

    getActionList(): ActionInfo[] {
        return [
            ...super.getActionList(),
            {
                name: "设置激活项",
                id: "setActiveTab",
                handler: (controller: CustomTabGroupController, params?: any) => {
                    controller.execute('setActiveTab', params);
                }
            }
        ];
    }
}

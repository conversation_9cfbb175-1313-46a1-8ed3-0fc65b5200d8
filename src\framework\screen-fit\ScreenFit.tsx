/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {CSSProperties, ReactNode, useEffect, useRef, useState} from 'react'
import debounce from "lodash/debounce";
import './ScreenFit.less';
import {AdaptationType} from "../../designer/DesignerType.ts";

interface ScreenFitProps {
    children?: ReactNode
    width: number | string
    height: number | string,
    bodyOverflowHidden?: boolean
    delay?: number
    mode?: AdaptationType
    scaleChange?: (xScale: number, yScale: number) => void
}

const screenFitStyleMap: Record<string, CSSProperties> = {
    'scale': {overflow: 'hidden'},
    'full-screen': {overflow: 'hidden'},
    'full-x': {overflow: 'hidden scroll'},
    'full-y': {overflow: 'scroll hidden'},
    'fit-width': {overflow: 'hidden scroll'}
}

export default function ScreenFit(props: ScreenFitProps) {
    const {width, height, bodyOverflowHidden = true, delay = 500, scaleChange} = props
    let bodyOverflow: string
    const elRef = useRef<HTMLDivElement>(null)
    const [size, setSize] = useState({width, height, originalHeight: 0, originalWidth: 0})

    let observer: MutationObserver;

    function updateSize() {
        // fit-width 模式下不在这里设置尺寸，由 updateScale 函数处理
        if (props.mode === 'fit-width') {
            return;
        }

        if (size.width && size.height) {
            elRef.current!.style.width = `${size.width}px`
            elRef.current!.style.height = `${size.height}px`
        } else {
            elRef.current!.style.width = `${size.originalWidth}px`
            elRef.current!.style.height = `${size.originalHeight}px`
        }
    }

    function updateScale() {
        // 获取真实视口尺寸
        const currentWidth = window.innerWidth
        const currentHeight = window.innerHeight
        // 获取大屏最终的宽高
        const realWidth = size.width || size.originalWidth
        const realHeight = size.height || size.originalHeight
        // 计算缩放比例
        const widthScale = currentWidth / +realWidth
        const heightScale = currentHeight / +realHeight

        switch (props.mode) {
            case 'full-screen':
                // 若要铺满全屏，则按照各自比例缩放
                elRef.current!.style.transform = `scale(${widthScale},${heightScale})`
                scaleChange && scaleChange(widthScale, heightScale)
                break;
            case 'full-x':
                elRef.current!.style.transform = `scale(${widthScale},${widthScale})`
                scaleChange && scaleChange(widthScale, widthScale)
                break;
            case 'full-y':
                elRef.current!.style.transform = `scale(${heightScale},${heightScale})`
                scaleChange && scaleChange(heightScale, heightScale)
                break;
            case 'fit-width':
                // 适配宽度：按宽度比例缩放，容器高度为缩放后的实际高度
                const scaleRatio = currentWidth / +realWidth;
                const scaledHeight = +realHeight * scaleRatio;

                // 设置容器尺寸为缩放后的实际尺寸
                elRef.current!.style.width = `${currentWidth}px`
                elRef.current!.style.height = `${scaledHeight}px`
                elRef.current!.style.margin = '0'

                // 对内容进行缩放
                elRef.current!.style.transform = `scale(${scaleRatio})`

                scaleChange && scaleChange(scaleRatio, scaleRatio)
                break;
            default:
                // 按照宽高最小比例进行缩放
                const scale = Math.min(widthScale, heightScale)
                elRef.current!.style.transform = `scale(${scale},${scale})`;
                const domWidth = elRef.current!.clientWidth
                const domHeight = elRef.current!.clientHeight
                const mx = Math.max((currentWidth - domWidth * scale) / 2, 0)
                const my = Math.max((currentHeight - domHeight * scale) / 2, 0)
                elRef.current!.style.margin = `${my}px ${mx}px`
                scaleChange && scaleChange(scale, scale)
                break;
        }
    }

    const onResize = debounce(() => {
        if (!elRef.current) return;
        updateSize()
        updateScale()
    }, delay)

    function initState() {
        if (bodyOverflowHidden) {
            bodyOverflow = document.body.style.overflow
            document.body.style.overflow = 'hidden'
        }
        observer = new MutationObserver(() => onResize());
        observer.observe(elRef.current!, {
            attributes: true,
            attributeFilter: ['style'],
            attributeOldValue: true
        })
        setSize({
            ...size,
            originalWidth: window.screen.width,
            originalHeight: window.screen.height
        })
        updateSize();
        updateScale();
        window.addEventListener('resize', onResize);
    }

    useEffect(() => {
        initState()
        return () => {
            observer.disconnect();
            window.removeEventListener('resize', onResize);
            if (bodyOverflowHidden)
                document.body.style.overflow = bodyOverflow;
        }
    }, [])

    const scrollStyle = screenFitStyleMap[props.mode || 'scale']

    return (
        <div style={{...scrollStyle}}
             className={'react-screen-box'}>
            <div className={'screen-wrapper'}
                 ref={elRef}>
                {props.children}
            </div>
        </div>
    )
}
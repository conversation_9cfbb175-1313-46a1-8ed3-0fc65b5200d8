/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {ConfigType} from "../../../designer/right/ConfigContent.tsx";
import {AntdCartesianCoordinateSys} from "../../antd-common/config/AntdFragment.tsx";
import {ColorAttr, LineOptions} from "@antv/g2plot";
import AntdRadarController from "./AntdRadarController.ts";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI.tsx";
import {Control} from "../../../json-schema/SchemaTypes.ts";
import React from "react";
import AntdCommonUtil from "../../antd-common/AntdCommonUtil.ts";
import ColorUtil from "../../../utils/ColorUtil.ts";

export default function AntdRadarStyleConfig(props: ConfigType<AntdRadarController>) {
    const {controller} = props;
    const config = controller.getConfig()?.style;

    const lineCoordinateSysChange = (config: LineOptions) => {
        controller.update({style: config});
    }

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataFragment} = fieldChangeData;

        // 处理辅助点颜色
        if (id === 'pointFill') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {point: {...config?.point, color: gradientCss}}});
            } else {
                // 处理普通颜色
                controller.update({style: {point: {...config?.point, color: data as string}}});
            }
        }
        // 处理辅助点边框颜色
        else if (id === 'pointStroke') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {point: {...config?.point, style: {...config?.point?.style, stroke: gradientCss}}}});
            } else {
                // 处理普通颜色
                controller.update({style: {point: {...config?.point, style: {...config?.point?.style, stroke: data as string}}}});
            }
        }
        // 处理面积边框颜色
        else if (id === 'lineStyleStroke') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {lineStyle: {...config?.lineStyle, stroke: gradientCss}}});
            } else {
                // 处理普通颜色
                controller.update({style: {lineStyle: {...config?.lineStyle, stroke: data as string}}});
            }
        }
        // 处理雷达面颜色
        else if (id === 'areaFill') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {area: {...config?.area, style: {...config?.area?.style, fill: gradientCss}}}});
            } else {
                // 处理普通颜色
                controller.update({style: {area: {...config?.area, style: {...config?.area?.style, fill: data as string}}}});
            }
        } else {
            controller.update(dataFragment);
        }
    }

    const schema: Control = {
        type: 'accordion',
        label: '图形',
        key: 'style',
        config: {
            titleStyle: {height: 49}
        },
        children: [
            {
                type: 'sub-accordion',
                label: '基础',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                type: 'number-input',
                                label: '半径',
                                key: 'radius',
                                value: config?.radius,
                                config: {
                                    min: 0.1,
                                    max: 1,
                                    step: 0.1
                                }
                            },
                            {
                                type: 'switch',
                                label: '平滑',
                                key: 'smooth',
                                value: config?.smooth
                            }
                        ],
                    }
                ]
            },
            {
                type: 'sub-accordion',
                label: '辅助点',
                key: 'point',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                type: 'number-input',
                                label: '大小',
                                key: 'size',
                                value: config?.point?.size
                            },
                            {
                                type: 'select',
                                label: '形状',
                                key: 'shape',
                                value: config?.point?.shape,
                                config: {
                                    options: [
                                        {label: '圆', value: 'circle'},
                                        {label: '钻石', value: 'diamond'},
                                        {label: '三角', value: 'triangle'},
                                    ]
                                }
                            },
                            {
                                key: 'style',
                                children: [
                                    {
                                        id: 'pointFill',
                                        type: 'enhanced-color-picker',
                                        label: '颜色',
                                        key: 'fill',
                                        value: config?.point?.color,
                                        config: {showText: true}
                                    },
                                    {
                                        id: 'pointStroke',
                                        type: 'enhanced-color-picker',
                                        label: '边框',
                                        key: 'stroke',
                                        value: (config?.point?.style as any)?.stroke,
                                        config: {showText: true}
                                    },
                                    {
                                        type: 'number-input',
                                        label: '边框宽度',
                                        key: 'lineWidth',
                                        value: (config?.point?.style as any)?.lineWidth,
                                        config: {
                                            min: 0,
                                            max: 10,
                                            step: 1
                                        }
                                    }
                                ]
                            }
                        ],
                    }
                ]
            },
            {
                type: 'sub-accordion',
                label: '面积边框',
                key: 'lineStyle',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                id: 'lineStyleStroke',
                                type: 'enhanced-color-picker',
                                label: '颜色',
                                key: 'stroke',
                                value: (config?.lineStyle as any)?.stroke,
                                config: {showText: true}
                            },
                            {
                                type: 'number-input',
                                label: '宽度',
                                key: 'lineWidth',
                                value: (config?.lineStyle as any)?.lineWidth,
                                config: {
                                    min: 0,
                                    max: 10,
                                    step: 1
                                }
                            },
                            {
                                type: 'number-input',
                                label: '透明度',
                                key: 'opacity',
                                value: (config?.lineStyle as any)?.opacity,
                                config: {
                                    min: 0,
                                    max: 1,
                                    step: 0.1
                                }
                            }
                        ],
                    }
                ]
            },
            {
                type: 'sub-accordion',
                label: '雷达面',
                key: 'area',
                children: [
                    {
                        type: 'grid',
                        key: 'style',
                        children: [
                            {
                                id: 'areaFill',
                                type: 'enhanced-color-picker',
                                label: '颜色',
                                key: 'fill',
                                value: (config?.area?.style as any)?.fill,
                                config: {showText: true}
                            },
                            {
                                type: 'number-input',
                                label: '透明度',
                                key: 'fillOpacity',
                                value: (config?.area?.style as any)?.fillOpacity,
                                config: {
                                    min: 0,
                                    max: 1,
                                    step: 0.1
                                }
                            }
                        ],
                    }
                ]
            },
        ]
    }

    return (
        <>
            <LCGUI schema={schema} onFieldChange={onFieldChange}/>
            <AntdCartesianCoordinateSys onChange={lineCoordinateSysChange} config={controller.getConfig()?.style}/>
        </>
    );
}

export const AntdRadarFieldMapping: React.FC<ConfigType<AntdRadarController>> = ({controller}) => {
    const config = controller.getConfig()?.style;
    const options = AntdCommonUtil.getDataFieldOptions(controller);
    const schema: Control = {
        type: 'grid',
        key: 'style',
        children: [
            {
                type: 'select',
                label: 'x轴字段',
                key: 'xField',
                value: config?.xField,
                config: {
                    options,
                }
            },
            {
                type: 'select',
                label: 'y轴字段',
                key: 'yField',
                value: config?.yField,
                config: {
                    options,
                }
            },
        ]
    }

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {dataFragment} = fieldChangeData;
        controller.update(dataFragment);
    }

    return <div style={{padding: 10}}><LCGUI schema={schema} onFieldChange={onFieldChange}/></div>
}
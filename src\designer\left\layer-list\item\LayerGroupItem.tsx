/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {BaseLayer} from "./BaseLayer";
import {Edit, FolderClose, FolderOpen, Lock, PreviewClose, PreviewOpen, Unlock} from "@icon-park/react";

export default class LayerGroupItem extends BaseLayer {
    render() {
        const {children} = this.props;
        const {hide, lock, showContent, selected, name, inputMode} = this.state;
        const { isOver, canDrop, dropPosition } = this.props;

        // 计算拖拽预览样式
        const getDropIndicatorStyle = () => {
            if (!isOver || !canDrop || !dropPosition) return {};

            const baseStyle = {
                position: 'absolute' as const,
                left: 0,
                right: 0,
                height: '2px',
                backgroundColor: '#1890ff',
                zIndex: 1000,
            };

            if (dropPosition === 'top') {
                return { ...baseStyle, top: '-1px' };
            } else {
                return { ...baseStyle, bottom: '-1px' };
            }
        };

        return (
            <div className={'layer-group'}>
                <div
                    className={`group-header ${selected ? "layer-selected" : hide
                        ? "layer-hide" : lock ? "layer-lock" : ""} ${this.props.isDragging ? "layer-dragging" : ""}`}
                    onDoubleClick={this.activeComponentConfig}
                    onClick={(e) => {
                        // 调用onSelected方法，但不阻止事件冒泡
                        this.onSelected(e);
                    }}
                    ref={this.props.dragRef}
                    style={{
                        cursor: lock || hide ? 'default' : 'move',
                        position: 'relative'
                    }}
                >
                    {/* 拖拽预览指示线 */}
                    {isOver && canDrop && dropPosition && (
                        <div style={getDropIndicatorStyle()} />
                    )}
                    <div className={'group-left'}>
                        <div className={'group-icon'} onClick={(e) => {
                            e.stopPropagation();
                            this.setState({showContent: !showContent});
                        }}>
                            {showContent ? <FolderOpen size={14}/> : <FolderClose size={14}/>}
                        </div>
                        <div className={'group-name'}>{inputMode ?
                            <input type="text" defaultValue={name} autoFocus={true} onChange={this.changeLayerName}
                                   ref={ref => ref?.select()}
                                   onKeyDown={(e) => {
                                       if (e.code === "Enter")
                                           this.closeInput();
                                   }}
                                   onBlur={this.closeInput}/> : name}
                        </div>
                    </div>
                    <div className={'group-operators'}>
                        <div className={'group-operator'} onClick={this.openInput}>
                            <Edit size={14}/>
                        </div>
                        <div className={'group-operator'} onClick={this.toggleHide}>
                            {hide ? <PreviewClose size={14}/> : <PreviewOpen size={14}/>}
                        </div>
                        <div className={'group-operator'} onClick={this.toggleLock}>
                            {lock ? <Lock size={14}/> : <Unlock size={14}/>}
                        </div>
                    </div>
                </div>
                {showContent && <div className={'group-content'}>
                    {children}
                </div>}
            </div>
        );
    }
}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import MonacoEditor, {MonacoEditorProps, MonacoEditorRef} from "./MonacoEditor.tsx";
import {useRef} from "react";
import {Button, Modal} from "antd";
import {Code} from "@icon-park/react";

export interface FullEditorProps extends MonacoEditorProps {
    onClose?: () => void;
}

export default function FullEditor(props: FullEditorProps) {
    const {value, defaultValue} = props;
    const {onClose, onChange, ...restProps} = props;
    const valueRef = useRef(value ?? defaultValue);
    const monacoEditorRef = useRef<MonacoEditorRef>(null);

    const _onChange = (value?: string) => {
        valueRef.current = value || "";
    }

    const _onSave = () => {
        onChange && onChange(valueRef.current);
        onClose && onClose();
    }

    const handleFormat = () => {
        monacoEditorRef.current?.formatDocument();
    }

    const _editProps = {...restProps, label: undefined};

    return (
        <Modal title={'代码编辑'} open={true} width={1000} onCancel={onClose}
               footer={[
                   <Button key={'format'} style={{width: 70, height: 30}} onClick={handleFormat} icon={<Code />}>格式化</Button>,
                   <Button key={'save'} style={{width: 70, height: 30}} onClick={_onSave} type="primary">保存</Button>,
                   <Button key={'cancel'} style={{width: 70, height: 30}} onClick={onClose}>取消</Button>
               ]}>
            <MonacoEditor ref={monacoEditorRef} onChange={_onChange} {..._editProps} quickSuggestions={true} height={600}/>
        </Modal>
    )
}
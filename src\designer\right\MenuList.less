/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

@import "../../designer/style/DesignerStyle.less";

.lc-config-menu {
  height: 100%;
  width: 35px;

  .menu-list {
    width: 35px;
    height: 100%;
    background-color: #1f1f1f;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #bababa;

    .menu-item-active {
      color: #2cbdff;
    }

    .menu-item {
      &:hover {
        transition: all 0.5s;
        color: #2cbdff;
      }

      width: 100%;
      display: flex;
      cursor: pointer;
      padding: 10px 5px;

      font-size: 12px;
      align-items: center;
      writing-mode: vertical-lr;

      .item-icon {
        font-size: 16px;
        margin-bottom: 5px;
        position: relative;
      }
    }
  }
}
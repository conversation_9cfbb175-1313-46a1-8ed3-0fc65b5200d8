.custom-carousel-container {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .carousel-content-wrapper {
    // 默认样式，会被特定模式覆盖
  }

  // 连续滚动模式
  .continuous-mode {
    animation-name: scroll-up;
    animation-duration: var(--animation-duration, 10s);
    animation-timing-function: linear;
    animation-iteration-count: infinite;
  }
  
  // 步进滚动模式的transform由JS控制
  .step-mode {
    transition: transform 0.5s ease-in-out;
  }
}

// 连续滚动动画
@keyframes scroll-up {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(var(--scroll-height, -50%));
  }
}
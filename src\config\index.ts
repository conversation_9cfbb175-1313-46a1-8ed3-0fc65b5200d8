
/**
 * 页面打开方式枚举
 */
export enum PageOpenMode {
    /** 当前页面打开 */
    CURRENT_PAGE = 'current',
    /** 新标签页打开 */
    NEW_TAB = 'new_tab'
}

/**
 * 项目操作配置
 */
export interface ProjectOperationConfig {
    /** 编辑项目的打开方式 */
    editMode: PageOpenMode;
    /** 预览项目的打开方式 */
    previewMode: PageOpenMode;
    /** 是否在操作后刷新项目列表 */
    refreshAfterOperation: boolean;
}

/**
 * 调试配置
 */
export interface DebugConfig {
    /** 是否启用全局变量调试器 */
    enableGlobalVariableDebugger: boolean;
    /** 是否启用控制台调试工具 */
    enableConsoleDebugTools: boolean;
}

/**
 * 应用配置接口
 */
export interface AppConfig {
    /** 项目操作配置 */
    projectOperation: ProjectOperationConfig;
    /** 调试配置 */
    debug: DebugConfig;
}

/**
 * 默认应用配置
 */
export const APP_CONFIG: AppConfig = {
    projectOperation: {
        editMode: PageOpenMode.CURRENT_PAGE,
        previewMode: PageOpenMode.NEW_TAB,
        refreshAfterOperation: true
    },
    debug: {
        enableGlobalVariableDebugger: true,
        enableConsoleDebugTools: true
    }
};

/**
 * 获取项目操作配置
 */
export const getProjectOperationConfig = () => APP_CONFIG.projectOperation;

/**
 * 获取调试配置
 */
export const getDebugConfig = () => APP_CONFIG.debug;

/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractConvert from "../../../framework/convert/AbstractConvert";
import { ChildTabComponentProps } from "./ChildTabTypes";
import imageSourceCache from "../../../framework/cache/ImageSourceCache";
import { IImageData } from "../base-image/BaseImageComponent";

/**
 * 子Tab图片转换器，用于处理背景图片资源的转换
 * 保证项目在导出/导入时背景图片能够正确显示
 */
export default class ChildTabConvert extends AbstractConvert<ChildTabComponentProps> {
    getKey(): string {
        return "ChildTab";
    }

    /**
     * 项目导出时调用，处理背景图片相关的数据
     */
    convert(data: ChildTabComponentProps): void {
        // 本地图片资源已通过ImageUpload组件上传到资源库，不需要额外处理
    }

    /**
     * 项目导入时调用，恢复背景图片URL
     */
    async convertBack(data: ChildTabComponentProps): Promise<void> {
        // 处理激活状态下的背景图片
        if (data.style?.activeStyle?.backgroundImage?.type === 'local' && data.style?.activeStyle?.backgroundImage?.hash) {
            const imageInfo: IImageData = await imageSourceCache.getCache(data.style.activeStyle.backgroundImage.hash);
            if (imageInfo) {
                data.style.activeStyle.backgroundImage.localUrl = imageInfo.url;
            }
        }

        // 处理未激活状态下的背景图片
        if (data.style?.inactiveStyle?.backgroundImage?.type === 'local' && data.style?.inactiveStyle?.backgroundImage?.hash) {
            const imageInfo: IImageData = await imageSourceCache.getCache(data.style.inactiveStyle.backgroundImage.hash);
            if (imageInfo) {
                data.style.inactiveStyle.backgroundImage.localUrl = imageInfo.url;
            }
        }
    }
} 
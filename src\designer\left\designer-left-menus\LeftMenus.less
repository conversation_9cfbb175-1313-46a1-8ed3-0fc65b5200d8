/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.designer-left-menus {
  width: 60px;
  border-right: 1px solid #404040;

  .menu-item {
    width: 60px;
    height: 63px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #9c9c9cd6;
    transition: color 0.5s, background-color 0.5s, border-right-color 0.5s;

    .menu-item-icon {
      font-size: 20px;
      padding: 3px;
    }

    .menu-item-content {
      padding: 2px;
      font-size: 12px;
    }
  }

  .menu-item:hover {
    color: #ebebeb;
    background-color: rgba(188, 188, 188, 0.27);
  }

  .menu-item-active {
    color: #0080ff;
  }
}



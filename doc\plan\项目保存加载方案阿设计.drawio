<mxfile host="app.diagrams.net" modified="2023-07-05T14:29:56.467Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/114.0.1823.67" etag="Ztrhnw45iHMoKqXav-hd" version="21.5.0" type="device">
  <diagram id="IWjkEfxruRDeSBaM1mXx" name="第 1 页">
    <mxGraphModel dx="1535" dy="983" grid="0" gridSize="10" guides="0" tooltips="0" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-1" value="设计器初始化过程" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="30" y="68" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3VFxZQKgpR7OsNjRWWTR-2" target="3VFxZQKgpR7OsNjRWWTR-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-2" value="开始" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="110" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-25" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3VFxZQKgpR7OsNjRWWTR-5" target="3VFxZQKgpR7OsNjRWWTR-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-5" value="组件自动化扫描bootCore" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="170" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-17" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3VFxZQKgpR7OsNjRWWTR-9" target="3VFxZQKgpR7OsNjRWWTR-16" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-9" value="初始化设计器store" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="250" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-14" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3VFxZQKgpR7OsNjRWWTR-11" target="3VFxZQKgpR7OsNjRWWTR-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-11" value="初始化左侧组件库" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="410" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-20" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3VFxZQKgpR7OsNjRWWTR-13" target="3VFxZQKgpR7OsNjRWWTR-19" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-13" value="初始化右侧配置组件store" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="490" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-18" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3VFxZQKgpR7OsNjRWWTR-16" target="3VFxZQKgpR7OsNjRWWTR-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-16" value="初始化头部组件库" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="330" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-24" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3VFxZQKgpR7OsNjRWWTR-19" target="3VFxZQKgpR7OsNjRWWTR-23" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-19" value="初始化底部统计信息" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="560" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-23" value="结束" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="640" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-27" value="" style="shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;labelPosition=left;verticalLabelPosition=middle;align=right;verticalAlign=middle;rotation=-180;" parent="1" vertex="1">
          <mxGeometry x="180" y="350" width="20" height="230" as="geometry" />
        </mxCell>
        <mxCell id="3VFxZQKgpR7OsNjRWWTR-28" value="这个初始化过程可以异步并行执行" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="210" y="450" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zQ0bkYoNBiz1xQpFheJm-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="zQ0bkYoNBiz1xQpFheJm-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1279" y="17" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1331" y="222" />
              <mxPoint x="1331" y="18" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zQ0bkYoNBiz1xQpFheJm-1" value="" style="rounded=1;whiteSpace=wrap;html=1;arcSize=4;" vertex="1" parent="1">
          <mxGeometry x="1155" y="115" width="138" height="147" as="geometry" />
        </mxCell>
        <mxCell id="zQ0bkYoNBiz1xQpFheJm-2" value="LocalStorage(本地数据库)" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="1149" y="78" width="157" height="26" as="geometry" />
        </mxCell>
        <mxCell id="zQ0bkYoNBiz1xQpFheJm-3" value="project data(对象)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="1164" y="124" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zQ0bkYoNBiz1xQpFheJm-4" value="project info(数组)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1164" y="194" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zQ0bkYoNBiz1xQpFheJm-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="zQ0bkYoNBiz1xQpFheJm-6" target="zQ0bkYoNBiz1xQpFheJm-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1103" y="340" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1103" y="192" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zQ0bkYoNBiz1xQpFheJm-6" value="设计器" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="1162" y="-11" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zQ0bkYoNBiz1xQpFheJm-12" value="保存/ 更新" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="1022" y="98" width="73" height="26" as="geometry" />
        </mxCell>
        <mxCell id="zQ0bkYoNBiz1xQpFheJm-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="zQ0bkYoNBiz1xQpFheJm-13">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1226" y="267.8706149397226" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zQ0bkYoNBiz1xQpFheJm-13" value="列表展示" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1166" y="299" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

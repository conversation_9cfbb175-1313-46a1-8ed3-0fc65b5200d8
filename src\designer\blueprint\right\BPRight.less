/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.bp-right {
  height: 100%;
  width: 350px;
  background-color: #1a1a1a;
}

.node-config-empty-info {
  color: #999;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-left: 1px solid #2e2e2e;
}

.bp-node-config {

  border-left: 1px solid #333;

  .bp-node-config-header {
    width: 100%;
    padding: 10px 15px;
    border-bottom: 1px solid #333;

    .bp-node-config-name {
      color: #d9d9d9;
      font-size: 14px;
      margin-bottom: 5px;
    }

    .bp-node-config-info {
      color: #999;
      font-size: 12px;
    }
  }

  .bp-ap-info-list {

    .bp-ap-info-item:hover {
      background-color: #444444;
    }

    .bp-ap-info-item {
      font-size: 12px;
      background-color: #333333;
      padding: 5px;
      display: flex;
      margin-bottom: 6px;
      border-radius: 5px;
      align-items: center;
      transition: background-color 0.2s;


      .type-input {
        background-color: #7a4671;
      }

      .type-output {
        background-color: #535785;
      }

      .bp-ap-info-type {
        color: #ffffffcc;
        padding: 0 10px;
        border-radius: 3px;
        margin-right: 5px;
      }

      .bp-ap-info-name {
        color: #adadad;
      }
    }

  }

  .bp-node-config-detail {
    .accordion-header {
      padding: 15px;
      border-bottom: 1px solid #333;
    }

    .lc-accordion-body {
      padding: 0;
    }
  }
}
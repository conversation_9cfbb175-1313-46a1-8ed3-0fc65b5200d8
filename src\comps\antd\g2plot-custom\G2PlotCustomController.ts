/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {ComponentBaseProps} from "../../common-component/CommonTypes.ts";
import * as G2Plot from "@antv/g2plot";
import {Plot} from "@antv/g2plot";
import {UpdateOptions} from "../../../framework/core/AbstractController";
import {AntdBaseDesignerController} from "../../antd-common/AntdBaseDesignerController";
import ObjectUtil from "../../../utils/ObjectUtil.ts";
import globalVariableManager from "../../../designer/manager/GlobalVariableManager.ts";
import GlobalVariableParser from "../../../utils/GlobalVariableParser.ts";

export interface G2PlotCustomStyle {
    customCode: string;
    // 支持动态样式配置，这些配置会传递给图表实例
    [key: string]: any;
}

export interface G2PlotCustomProps extends ComponentBaseProps {
    style?: G2PlotCustomStyle;
}

export default class G2PlotCustomController extends AntdBaseDesignerController<Plot<any>, G2PlotCustomProps> {

    private globalVariableSubscriptions: Array<{variableId: string, subscriberId: string}> = [];

    async create(container: HTMLElement, config: G2PlotCustomProps): Promise<void> {
        this.container = container;
        this.config = config;
        const customCode = config.style?.customCode;
        if (!customCode || customCode === "")
            return;

        // 创建图表实例
        await this.createChartInstance(customCode);

        // 订阅全局变量变化
        this.subscribeToGlobalVariables(customCode);

        //注册蓝图-暂不处理
        // this.registerEvent();
    }

    /**
     * 创建图表实例
     */
    private async createChartInstance(customCode: string): Promise<void> {
        try {
            // 解析自定义代码中的 ${GV::variableName} 引用
            const parsedCode = await GlobalVariableParser.parseAndReplace(
                customCode,
                globalVariableManager,
                undefined,
                'javascript'
            );

            // 创建全局变量访问对象
            const globalVars = {
                get: (name: string) => globalVariableManager.getVariableValueByName(name)
            };

            // 创建动态样式配置对象（排除customCode）
            const dynamicStyleConfig = { ...this.config?.style };
            delete dynamicStyleConfig.customCode;

            // 执行自定义代码
            const func = eval(`(${parsedCode})`);
            if (typeof func !== 'function')
                return;

            // 传入参数：容器、G2Plot库、数据、全局变量、动态样式配置
            this.instance = func(this.container, G2Plot, this.config?.data?.staticData, globalVars, dynamicStyleConfig);
        } catch (error) {
            console.error('G2Plot自定义组件创建失败:', error);
        }
    }

    /**
     * 订阅全局变量变化
     */
    private subscribeToGlobalVariables(customCode: string): void {
        if (!customCode) return;

        // 解析代码中引用的全局变量
        const referencedVariables = this.extractGlobalVariableReferences(customCode);

        // 订阅这些变量的变化
        referencedVariables.forEach(variableName => {
            const variableId = this.findVariableIdByName(variableName);
            if (variableId) {
                const componentId = this.config?.base?.id || `g2plot-custom-${Date.now()}`;
                const subscriberId = `${componentId}-${variableId}`;

                globalVariableManager.subscribe(variableId, subscriberId, () => {
                    // 全局变量变化时重新创建图表
                    this.recreateChart();
                });

                this.globalVariableSubscriptions.push({variableId, subscriberId});
            }
        });
    }



    /**
     * 提取代码中引用的全局变量名称
     */
    private extractGlobalVariableReferences(code: string): string[] {
        const references: string[] = [];

        // 匹配 ${GV::variableName} 格式
        const templateRegex = /\$\{GV::([^}]+)\}/g;
        let match;
        while ((match = templateRegex.exec(code)) !== null) {
            references.push(match[1]);
        }

        // 匹配 globalVars.get('variableName') 格式
        const apiRegex = /globalVars\.get\(['"]([^'"]+)['"]\)/g;
        while ((match = apiRegex.exec(code)) !== null) {
            references.push(match[1]);
        }

        return [...new Set(references)]; // 去重
    }

    /**
     * 根据变量名查找变量ID
     */
    private findVariableIdByName(variableName: string): string | null {
        for (const [id, definition] of globalVariableManager.definitions) {
            if (definition.name === variableName) {
                return id;
            }
        }
        return null;
    }

    /**
     * 重新创建图表
     */
    private async recreateChart(): Promise<void> {
        const customCode = this.config?.style?.customCode;
        if (!customCode) return;

        // 销毁现有实例
        try {
            this.instance?.destroy();
        } catch (e) {
            // 清理容器
            while (this.container?.firstChild) {
                this.container.removeChild(this.container.firstChild);
            }
        }

        // 重新创建
        await this.createChartInstance(customCode);
    }

    destroy(): void {
        // 取消全局变量订阅
        this.globalVariableSubscriptions.forEach(({variableId, subscriberId}) => {
            globalVariableManager.unsubscribe(variableId, subscriberId);
        });
        this.globalVariableSubscriptions = [];

        // 销毁图表实例
        this.instance?.destroy();
        this.instance = null;
        this.config = null;
        this.interval && clearInterval(this.interval);
    }

    getConfig(): G2PlotCustomProps | null {
        return this.config;
    }


    changeData(data: any) {
        this.instance?.changeData(data);
    }

    async update(config: G2PlotCustomProps, upOp?: UpdateOptions): Promise<void> {
        // 合并最新的配置
        const oldConfig = { ...this.config };
        this.config = ObjectUtil.merge(this.config, config);
        upOp = upOp || {reRender: true};

        if (upOp.reRender) {
            const customCode = this.config?.style?.customCode;
            const oldCustomCode = oldConfig?.style?.customCode;

            // 检查是否只是样式配置更新（不是customCode更新）
            const isOnlyStyleUpdate = customCode === oldCustomCode &&
                                    customCode &&
                                    config.style &&
                                    !config.style.customCode;

            if (isOnlyStyleUpdate && this.instance) {
                // 只更新样式，不重新创建图表
                try {
                    // 创建动态样式配置对象（排除customCode）
                    const dynamicStyleConfig = { ...this.config?.style };
                    delete dynamicStyleConfig.customCode;

                    // 如果图表实例有update方法，使用它
                    if (typeof this.instance.update === 'function') {
                        this.instance.update(dynamicStyleConfig);
                    } else {
                        // 否则重新创建图表
                        await this.recreateChart();
                    }
                    return;
                } catch (error) {
                    console.warn('样式更新失败，将重新创建图表:', error);
                    // 如果样式更新失败，回退到重新创建
                }
            }

            if (!customCode || customCode === "") {
                // 取消现有订阅
                this.globalVariableSubscriptions.forEach(({variableId, subscriberId}) => {
                    globalVariableManager.unsubscribe(variableId, subscriberId);
                });
                this.globalVariableSubscriptions = [];
                return;
            }

            // 取消现有的全局变量订阅
            this.globalVariableSubscriptions.forEach(({variableId, subscriberId}) => {
                globalVariableManager.unsubscribe(variableId, subscriberId);
            });
            this.globalVariableSubscriptions = [];

            // 销毁之前的图表实例
            try {
                this.instance?.destroy();
            } catch (e) {
                while (this.container?.firstChild) {
                    this.container.removeChild(this.container.firstChild);
                }
            }

            // 重新创建图表实例和订阅
            await this.createChartInstance(customCode);
            this.subscribeToGlobalVariables(customCode);

            // 触发全局变量引用计数重新计算
            globalVariableManager.recalculateAllReferenceCounts();
        }
    }

}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {Component} from 'react';
import {AntdCartesianCoordinateSys} from "../config/AntdFragment";
import {Area, AreaOptions, ColorAttr, ShapeStyle} from "@antv/g2plot";
import AbstractController from "../../../framework/core/AbstractController";
import AntdCommonAreaController, {AntdAreaProps} from "./AntdCommonAreaController";
import {Control} from "../../../json-schema/SchemaTypes";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI";
import AntdCommonUtil from "../AntdCommonUtil";
import {AntdLegend} from "../config/legend/AntdLegend";
import {ShapeAttrs} from "@antv/g-base";
import {ConfigType} from "../../../designer/right/ConfigContent";
import ColorUtil from "../../../utils/ColorUtil";

class AntdAreaCommonStyleConfig extends Component<ConfigType<AntdCommonAreaController>> {

    areaCoordinateSysChange = (config: AreaOptions) => {
        const controller: AbstractController<Area, AntdAreaProps> = this.props.controller;
        controller.update({style: config});
    }

    render() {
        const {controller} = this.props;
        const config = controller.getConfig()?.style;
        return (
            <>
                <AntdCommonAreaGraphics controller={controller}/>
                <AntdLegend controller={controller}/>
                <AntdCartesianCoordinateSys onChange={this.areaCoordinateSysChange} config={config}/>
            </>
        );
    }
}

export {AntdAreaCommonStyleConfig};

export const AntdCommonAreaGraphics = (props: ConfigType<AntdCommonAreaController>) => {
    const {controller} = props;
    const config = controller.getConfig()?.style;

    const _onChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataFragment} = fieldChangeData;

        // 处理数据面颜色
        if (id === 'areaColor') {
            if (data && Array.isArray(data)) {
                // 多色模式
                // 检查数组中是否有渐变对象
                const hasGradient = data.some(color =>
                    typeof color === 'object' && color !== null && 'type' in color &&
                    (color.type === 'linear-gradient' || color.type === 'radial-gradient')
                );

                if (hasGradient) {
                    // 如果数组中有渐变对象，我们需要将它们转换为渐变字符串
                    const processedColors = data.map(color => {
                        if (typeof color === 'object' && color !== null && 'type' in color &&
                            (color.type === 'linear-gradient' || color.type === 'radial-gradient')) {
                            return ColorUtil.gradientToCss(color);
                        }
                        return color;
                    });
                    controller.update({style: {color: processedColors as ColorAttr}});
                } else {
                    // 如果数组中没有渐变对象，直接使用原始数组
                    controller.update({style: {color: data as ColorAttr}});
                }
            } else if (data && typeof data === 'object' && data !== null && 'type' in data &&
                      (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {color: gradientCss as any}});
            } else {
                // 处理普通颜色
                controller.update({style: {color: data as any}});
            }
        }
        // 处理数据线颜色
        else if (id === 'lineColor') {
            if (data && Array.isArray(data)) {
                // 多色模式
                // 检查数组中是否有渐变对象
                const hasGradient = data.some(color =>
                    typeof color === 'object' && color !== null && 'type' in color &&
                    (color.type === 'linear-gradient' || color.type === 'radial-gradient')
                );

                if (hasGradient) {
                    // 如果数组中有渐变对象，我们需要将它们转换为渐变字符串
                    const processedColors = data.map(color => {
                        if (typeof color === 'object' && color !== null && 'type' in color &&
                            (color.type === 'linear-gradient' || color.type === 'radial-gradient')) {
                            return ColorUtil.gradientToCss(color);
                        }
                        return color;
                    });
                    controller.update({style: {line: {...config?.line, color: processedColors as ColorAttr}}});
                } else {
                    // 如果数组中没有渐变对象，直接使用原始数组
                    controller.update({style: {line: {...config?.line, color: data as ColorAttr}}});
                }
            } else if (data && typeof data === 'object' && data !== null && 'type' in data &&
                      (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {line: {...config?.line, color: gradientCss as any}}});
            } else {
                // 处理普通颜色
                controller.update({style: {line: {...config?.line, color: data as any}}});
            }
        }
        // 处理数据点颜色
        else if (id === 'pointColor') {
            if (data && Array.isArray(data)) {
                // 多色模式
                // 检查数组中是否有渐变对象
                const hasGradient = data.some(color =>
                    typeof color === 'object' && color !== null && 'type' in color &&
                    (color.type === 'linear-gradient' || color.type === 'radial-gradient')
                );

                if (hasGradient) {
                    // 如果数组中有渐变对象，我们需要将它们转换为渐变字符串
                    const processedColors = data.map(color => {
                        if (typeof color === 'object' && color !== null && 'type' in color &&
                            (color.type === 'linear-gradient' || color.type === 'radial-gradient')) {
                            return ColorUtil.gradientToCss(color);
                        }
                        return color;
                    });
                    controller.update({style: {point: {...config?.point, color: processedColors as ColorAttr}}});
                } else {
                    // 如果数组中没有渐变对象，直接使用原始数组
                    controller.update({style: {point: {...config?.point, color: data as ColorAttr}}});
                }
            } else if (data && typeof data === 'object' && data !== null && 'type' in data &&
                      (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {point: {...config?.point, color: gradientCss as any}}});
            } else {
                // 处理普通颜色
                controller.update({style: {point: {...config?.point, color: data as any}}});
            }
        }
        // 处理数据点描边颜色
        else if (id === 'pointStroke') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {point: {...config?.point, style: {...(config?.point?.style as any), stroke: gradientCss as any}}}});
            } else {
                // 处理普通颜色
                controller.update({style: {point: {...config?.point, style: {...(config?.point?.style as any), stroke: data as any}}}});
            }
        }
        else {
            controller.update(dataFragment);
        }
    }

    const schema: Control = {
        type: 'accordion',
        label: '图形',
        key: 'style',
        config: {
            bodyStyle: {
                marginTop: 10
            }
        },
        children: [
            {
                type: 'sub-accordion',
                label: '数据点',
                key: 'point',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                key: 'size',
                                type: 'number-input',
                                label: '尺寸',
                                value: config?.point?.size || 0,
                                config: {
                                    min: 0,
                                    max: 100
                                }
                            },
                            {
                                key: 'shape',
                                type: 'select',
                                label: '形状',
                                value: config?.point?.shape || 'circle',
                                config: {
                                    options: [
                                        {value: 'circle', label: '圈形'},
                                        {value: 'square', label: '方形'},
                                        {value: 'bowtie', label: '领结'},
                                        {value: 'diamond', label: '钻石'},
                                        {value: 'hexagon', label: '六角形'},
                                        {value: 'triangle', label: '三角形'}]
                                }
                            },
                            {
                                key: 'style',
                                children: [
                                    {
                                        key: 'lineWidth',
                                        type: 'number-input',
                                        label: '描边宽',
                                        value: (config?.point?.style as ShapeStyle)?.lineWidth,
                                        config: {
                                            min: 0
                                        }
                                    },
                                    {
                                        id: 'pointStroke',
                                        key: 'stroke',
                                        type: 'enhanced-color-mode',
                                        label: '描边色',
                                        value: (config?.point?.style as ShapeStyle)?.stroke,
                                    },
                                ]
                            },
                            {
                                id: 'pointColor',
                                key: 'color',
                                type: 'enhanced-color-mode',
                                label: '颜色',
                                value: config?.point?.color || '#1c1c1c',
                            },
                        ]
                    }
                ]
            },
            {
                type: 'sub-accordion',
                label: '数据线',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                key: 'smooth',
                                type: 'switch',
                                label: '平滑',
                                value: config?.smooth || false,
                            },
                            {
                                key: 'line',
                                children: [
                                    {
                                        key: 'style',
                                        children: [
                                            {
                                                key: 'lineWidth',
                                                type: 'number-input',
                                                label: '宽度',
                                                value: (config?.line?.style as ShapeAttrs)?.lineWidth || 0,
                                                config: {
                                                    min: 0,
                                                    max: 100
                                                }
                                            },
                                        ]
                                    },
                                    {
                                        id: 'lineColor',
                                        key: 'color',
                                        type: 'enhanced-color-mode',
                                        label: '颜色',
                                        value: config?.line?.color as string || '#fff',
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                type: 'sub-accordion',
                label: '数据面',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                id: 'areaColor',
                                type: 'enhanced-color-mode',
                                label: '颜色',
                                key: 'color',
                                value: config?.color || '#1c1c1c',
                            }
                        ]
                    }
                ]
            }
        ]
    }

    return (
        <LCGUI schema={schema} onFieldChange={_onChange}/>
    )
}

export const AntdAreaCommonFieldMapping: React.FC<ConfigType> = (props) => {
    const {controller} = props;
    const config = controller.config.style;
    const options = AntdCommonUtil.getDataFieldOptions(controller);
    const schema: Control = {
        key: 'style',
        type: 'grid',
        children: [
            {
                key: 'xField',
                type: 'select',
                label: 'X字段',
                value: config.xField,
                config: {
                    options,
                }
            },
            {
                key: 'yField',
                type: 'select',
                label: 'Y字段',
                value: config.yField,
                config: {
                    options,
                }
            },
            {
                key: 'seriesField',
                type: 'select',
                label: '分组字段',
                value: config.seriesField,
                config: {
                    options,
                }
            }
        ]
    }

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        controller.update(fieldChangeData.dataFragment);
    }

    return <div style={{padding: 10}}><LCGUI schema={schema} onFieldChange={onFieldChange}/></div>
}
/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {Suspense, useRef} from "react";
import {Control} from "../../SchemaTypes";
import './ControlGroup.less';
import {FieldChangeData, LCGUI} from "../../LCGUI";
import Loading from "../loading/Loading";
import LCGUIUtil from "../../LCGUIUtil";
import ObjectUtil from "../../../utils/ObjectUtil";
import cloneDeep from "lodash/cloneDeep";
import {Down, Right} from "@icon-park/react";

export interface ControlGroupProps {
    label?: string;
    itemName?: string;
    template?: Control;
    defaultValue?: object[];
    value?: object[];  // 添加受控模式支持
    onChange?: (value: unknown) => void;

    // 操作接口
    onAdd?: () => void;
    onDelete?: () => void;
    showAddButton?: boolean;
    showDeleteButton?: boolean;
    customOperations?: React.ReactNode;
}

const ControlGroup = (props: ControlGroupProps) => {
    const {
        label,
        defaultValue = [],
        value,
        template,
        itemName,
        onAdd,
        onDelete,
        showAddButton = true,
        showDeleteButton = true,
        customOperations
    } = props;

    const [toggle, setToggle] = React.useState<boolean>(false);
    // 支持受控和非受控模式
    const controlled = value !== undefined;
    const dataRef = useRef(controlled ? value : defaultValue);
    const [schema, setSchema] = React.useState<Control>({children: []});

    // 重新构建schema的通用方法
    const rebuildSchema = React.useCallback(() => {
        const currentData = controlled ? value : dataRef.current;
        const newSchema: Control = {children: []};
        currentData.forEach((data, idx) => {
            newSchema.children?.push({
                type: 'accordion',
                key: idx + '',
                label: (itemName || '系列') + (idx + 1),
                children: [{...LCGUIUtil.schemaStructureAssignment(data, cloneDeep(template!))}]
            });
        });
        setSchema(newSchema);
    }, [template, itemName, controlled, value]);

    // 监听外部value变化，同步到内部状态
    React.useEffect(() => {
        if (controlled && value) {
            dataRef.current = value;
        }
    }, [controlled, value]);

    // 初始化schema
    React.useEffect(() => {
        rebuildSchema();
    }, [rebuildSchema]);

    // 处理添加按钮点击
    const handleAdd = () => {
        onAdd && onAdd();
    };

    // 处理删除按钮点击
    const handleDelete = () => {
        onDelete && onDelete();
    };



    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {dataFragment} = fieldChangeData;
        const index = Number(Object.keys(dataFragment)[0]);

        // 在受控模式下，不直接修改dataRef，而是通过onChange通知外部
        if (controlled) {
            const newData = [...value];
            newData[index] = ObjectUtil.merge(newData[index], dataFragment[index as keyof object]);
            const {onChange} = props;
            onChange && onChange(newData);
        } else {
            dataRef.current[index] = ObjectUtil.merge(dataRef.current[index], dataFragment[index as keyof object]);
            // 重新构建schema以确保rules能够使用最新的值
            rebuildSchema();
            const {onChange} = props;
            onChange && onChange(dataRef.current);
        }
    }

    // 渲染操作区域
    const renderOperations = () => {
        if (customOperations) {
            return customOperations;
        }

        return (
            <>
                {showDeleteButton && (
                    <span className={'operate-icon'} onClick={handleDelete}>
                        <svg width="16" height="16" viewBox="0 0 48 48" fill="none">
                            <path d="M9 10V44H39V10H9Z" stroke="currentColor" strokeWidth="4" strokeLinejoin="round"/>
                            <path d="M20 20V33" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M28 20V33" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M4 10H44" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M16 10L19.289 4H28.7L32 10H16Z" stroke="currentColor" strokeWidth="4" strokeLinejoin="round"/>
                        </svg>
                    </span>
                )}
                {showAddButton && (
                    <span className={'operate-icon'} onClick={handleAdd}>
                        <svg width="16" height="16" viewBox="0 0 48 48" fill="none">
                            <path d="M24 44C35.0457 44 44 35.0457 44 24C44 12.9543 35.0457 4 24 4C12.9543 4 4 12.9543 4 24C4 35.0457 12.9543 44 24 44Z" stroke="currentColor" strokeWidth="4" strokeLinejoin="round"/>
                            <path d="M24 16V32" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M16 24H32" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                    </span>
                )}
                <span className={'operate-icon toggle-icon'} onClick={() => setToggle(!toggle)}>
                    {toggle ?
                        <Down size={16}/> :
                        <Right size={16}/>}
                </span>
            </>
        );
    };

    return (
        <div className="control-group">
            <div className={'control-group-header'}>
                <div className={'cgh-label'}><span>{label}</span></div>
                <div className={'cgh-operate'}>
                    {renderOperations()}
                </div>
            </div>
            {toggle && <div className={'control-group-body'} style={{display: toggle ? 'block' : 'none'}}>
                <Suspense fallback={<Loading/>}>
                    <LCGUI schema={schema} onFieldChange={onFieldChange}/>
                </Suspense>
            </div>}
        </div>
    )
}

export default ControlGroup;
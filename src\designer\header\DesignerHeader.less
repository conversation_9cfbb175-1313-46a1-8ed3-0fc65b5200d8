/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

@titleColor: #00d6ff;
@menuColor: #b7b1b1;

.designer-header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #bababa;
  font-size: 14px;
  border-bottom: 1px solid #404040;

  .header-left {
    .header-title {
      font-size: 16px;
      font-weight: 500;
      padding-left: 15px;
    }
  }

  .header-center, .header-right {
    display: flex;
    align-items: center;
  }

  .header-item {
    display: flex;
    align-items: center;
    padding: 10px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      color: #0095db;
    }

    &:active {
      color: #2cbdff;
    }

    span {
      padding: 2px;
    }
  }
}
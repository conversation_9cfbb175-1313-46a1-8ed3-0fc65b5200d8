/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

// 导出组件定义，这样系统就能自动扫描到这些组件
export {default as CustomTabGroupDefinition} from './CustomTabGroupDefinition';
export {default as ChildTabDefinition} from './ChildTabDefinition';

// 导出类型定义
export * from './CustomTabGroupTypes';

// 导出组件
export {default as CustomTabGroupComponent} from './CustomTabGroupComponent';
export {default as ChildTabComponent} from './ChildTabComponent';

// 导出控制器
export {CustomTabGroupController} from './CustomTabGroupController';
export {ChildTabController} from './ChildTabController';

// 导出配置组件
export {default as CustomTabGroupConfig} from './CustomTabGroupConfig';
export {default as ChildTabConfig} from './ChildTabConfig';

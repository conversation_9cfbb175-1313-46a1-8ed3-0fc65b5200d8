{"name": "light-chaser", "private": true, "version": "0.0.8", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "check": "npx tsc --noEmit --watch", "preview": "vite preview"}, "dependencies": {"@antv/g-base": "^0.5.15", "@antv/g2plot": "^2.4.31", "@babel/standalone": "^7.27.6", "@icon-park/react": "^1.4.2", "@monaco-editor/react": "^4.6.0", "@scena/react-ruler": "^0.17.1", "@xyflow/react": "^12.7.0", "antd": "^5.20.0", "countup.js": "^2.8.2", "echarts": "^5.6.0", "flv.js": "^1.6.2", "hls.js": "^1.5.11", "localforage": "^1.10.0", "lodash": "^4.17.21", "mobx": "^6.7.0", "mobx-react": "^7.6.0", "monaco-editor": "^0.44.0", "nanoid": "3", "odometer_countup": "^1.0.4", "react": "~18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "~18.2.0", "react-moveable": "^0.54.1", "react-router-dom": "^6.21.0", "react-selecto": "^1.22.6", "react-slick": "^0.30.3", "react-timer-hook": "^3.0.7", "slick-carousel": "^1.8.1"}, "devDependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@amap/amap-jsapi-types": "^0.0.15", "@types/lodash": "^4.14.178", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "less": "^3.11.1", "postcss-loader": "3.0.0", "react-refresh": "^0.8.3", "style-loader": "1.3.0", "typescript": "^5.0.2", "vite": "^5.0.7"}}
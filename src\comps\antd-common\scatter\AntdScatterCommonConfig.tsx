/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {Component} from 'react';
import {AntdCartesianCoordinateSys} from "../config/AntdFragment";
import {Color<PERSON>ttr, Scatter, ScatterOptions} from "@antv/g2plot";
import AbstractController from "../../../framework/core/AbstractController";
import AntdCommonScatterController, {AntdScatterProps} from "./AntdCommonScatterController";
import {WritableScatterOptions} from "../types";
import {AntdBaseDesignerController} from "../AntdBaseDesignerController";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI";
import {Control} from "../../../json-schema/SchemaTypes";
import AntdCommonUtil from "../AntdCommonUtil";
import {AntdLegend} from "../config/legend/AntdLegend";
import {ShapeAttrs} from "@antv/g-base";
import {ConfigType} from "../../../designer/right/ConfigContent";
import ColorUtil from "../../../utils/ColorUtil";

class AntdScatterCommonStyleConfig extends Component<ConfigType> {
    scatterCoordinateSysChange = (config: ScatterOptions) => {
        const controller = this.props.controller as AntdCommonScatterController;
        controller.update({style: config});
    }

    scatterGraphicsChange = (config: ScatterOptions) => {
        const controller: AbstractController<Scatter, AntdScatterProps> = this.props.controller;
        controller.update({style: config});
    }

    render() {
        const {controller} = this.props;
        const config: ScatterOptions = controller.getConfig().style;
        return (
            <>
                <AntdCommonScatterGraphics config={config} onChange={this.scatterGraphicsChange}/>
                <AntdLegend controller={controller}/>
                <AntdCartesianCoordinateSys onChange={this.scatterCoordinateSysChange} config={config}/>
            </>
        );
    }
}

export {AntdScatterCommonStyleConfig};


export interface AntdCommonScatterGraphicsProps {
    config?: WritableScatterOptions;

    onChange(config: WritableScatterOptions): void;
}

export const AntdCommonScatterGraphics: React.FC<AntdCommonScatterGraphicsProps> = ({config, onChange}) => {

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataFragment} = fieldChangeData;

        // 处理散点颜色
        if (id === 'scatterColor') {
            if (data && Array.isArray(data)) {
                // 多色模式
                // 检查数组中是否有渐变对象
                const hasGradient = data.some(color =>
                    typeof color === 'object' && color !== null && 'type' in color &&
                    (color.type === 'linear-gradient' || color.type === 'radial-gradient')
                );

                if (hasGradient) {
                    // 如果数组中有渐变对象，我们需要将它们转换为渐变字符串
                    const processedColors = data.map(color => {
                        if (typeof color === 'object' && color !== null && 'type' in color &&
                            (color.type === 'linear-gradient' || color.type === 'radial-gradient')) {
                            return ColorUtil.gradientToCss(color);
                        }
                        return color;
                    });
                    onChange({color: processedColors as ColorAttr});
                } else {
                    // 如果数组中没有渐变对象，直接使用原始数组
                    onChange({color: data as ColorAttr});
                }
            } else if (data && typeof data === 'object' && data !== null && 'type' in data &&
                      (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                onChange({color: gradientCss as any});
            } else {
                // 处理普通颜色
                onChange({color: data as any});
            }
        }
        // 处理散点描边颜色
        else if (id === 'pointStroke') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                onChange({pointStyle: {...config?.pointStyle, stroke: gradientCss as any}});
            } else {
                // 处理普通颜色
                onChange({pointStyle: {...config?.pointStyle, stroke: data as any}});
            }
        }
        else {
            onChange(dataFragment);
        }
    }

    const schema: Control = {
        type: 'accordion',
        label: '图形',
        children: [
            {
                type: 'grid',
                children: [
                    {
                        key: 'size',
                        type: 'number-input',
                        label: '尺寸',
                        value: config?.size,
                        config: {
                            min: 0,
                            max: 100,
                        }
                    },
                    {
                        key: 'shape',
                        type: 'select',
                        label: '形状',
                        value: config?.shape,
                        config: {
                            options: [
                                {value: 'circle', label: '圈形'},
                                {value: 'square', label: '方形'},
                                {value: 'bowtie', label: '领结'},
                                {value: 'diamond', label: '钻石'},
                                {value: 'hexagon', label: '六角形'},
                                {value: 'triangle', label: '三角形'}]
                        }
                    },
                    {
                        key: 'pointStyle',
                        children: [
                            {
                                key: 'lineWidth',
                                type: 'number-input',
                                label: '线宽',
                                value: (config?.pointStyle as ShapeAttrs)?.lineWidth,
                                config: {
                                    min: 0,
                                    max: 10,
                                }
                            },
                            {
                                id: 'pointStroke',
                                key: 'stroke',
                                type: 'enhanced-color-mode',
                                label: '描边色',
                                value: (config?.pointStyle as ShapeAttrs)?.stroke,
                            }
                        ]
                    },
                    {
                        id: 'scatterColor',
                        key: 'color',
                        type: 'enhanced-color-mode',
                        label: '颜色',
                        value: config?.color,
                    },
                ]
            }
        ]
    }

    return (
        <LCGUI schema={schema} onFieldChange={onFieldChange}/>
    )
}

export const AntdScatterFieldMapping: React.FC<ConfigType<AntdBaseDesignerController>> = ({controller}) => {

    const config = controller.getConfig()?.style;

    const options = AntdCommonUtil.getDataFieldOptions(controller);
    const schema: Control = {
        type: 'grid',
        key: 'style',
        children: [
            {
                key: 'xField',
                type: 'select',
                label: 'X字段',
                value: config?.xField,
                config: {
                    options,
                }
            },
            {
                key: 'colorField',
                type: 'select',
                label: '颜色字段',
                value: config?.colorField,
                config: {
                    options,
                }
            },
            {
                key: 'yField',
                type: 'select',
                label: 'Y字段',
                value: config?.yField,
                config: {
                    options,
                }
            },
            {
                key: 'sizeField',
                type: 'select',
                label: '尺寸字段',
                value: config?.sizeField,
                config: {
                    options,
                }
            }
        ]
    }

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        controller.update(fieldChangeData.dataFragment);
    }

    return <div style={{padding: 10}}><LCGUI schema={schema} onFieldChange={onFieldChange}/></div>
}
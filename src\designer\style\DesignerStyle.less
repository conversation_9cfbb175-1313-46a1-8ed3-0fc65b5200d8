/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

@lc-config-title: #e4f4ff;
@lc-config-sub-title: #d1dfe9;
@lc-config-label: #b6b8bb;
@lc-config-value: #c6c9cd;

@headBgColor: #1f1f1f;
@headItemColor: #fff;

@hover-bg-color: #fff;
@hover-color: #111216;
@hover-border-color: #111216;

@leftBgColor: #1f1f1f;
@leftItemColor: #fff;
@leftItemHoverColor: #fff;
@leftItemBorderColor: #fff;

@compListBgColor: #1c1f22;
@compListTitleBgColor: #26292e;
@compListTitleColor: #111216;
@compListItemColor: #fff;
@compListItemBgColor: #4a4a4a;
@compListItemTitleColor: #424242;

@rightMenuListBgColor: #1a1a1a;
@rightMenuListItemColor: #b9b9b9;

@rightConfigPanelBgColor: #1f1f1f;
@rightConfigPanelTitleBgColor: #26292e;
@rightConfigPanelTitleColor: #eaeef8;

@rightConfigItemTitleColor: #fff;
@rightConfigItemSubTitleColor: #fff;
@rightConfigItemLabelColor: #fff;

@footer-bg-color: #1f1f1f;
@footer-item-color: #c4c4c4;

@lc-font-sm: 12px;
@lc-font-ml: 14px;
@lc-font-lg: 16px;


@headerHeight: 50px;
@leftWidth: 60px;
@rightWidth: 35px;
@contentWidth: calc(100% - (@leftWidth + @rightWidth));
@contentHeight: calc(100% - (@headerHeight + @footHeight));
@footHeight: 40px;
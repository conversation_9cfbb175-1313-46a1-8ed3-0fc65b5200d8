/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import { defineConfig, loadEnv } from 'vite'
import { resolve } from 'path'
import viteBaseConfig from "./vite.base.config";
import viteDevConfig from "./vite.dev.config";
import viteProdConfig from "./vite.prod.config";

const viteConfig = {
    'build': () => ({...viteBaseConfig, ...viteProdConfig}),
    'serve': () => ({...viteBaseConfig, ...viteDevConfig}),
}

export default defineConfig(({ command, mode }) => {
    // 根据当前工作目录中的 `mode` 加载 .env 文件
    // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀。
    const env = loadEnv(mode, process.cwd(), '')

    // 获取基本配置
    const baseConfig = viteConfig[command]();

    // 合并环境变量配置
    return {
        ...baseConfig,
        // 在客户端代码中启用导入.env变量
        define: {
            ...baseConfig.define,
            'process.env': env
        },
        
        // 开发服务器配置
        server: {
            host: '0.0.0.0', // 允许外部IP访问
            proxy: {
                '^/(api|static)/.*': {
                    target: 'http://***********:9009',
                    changeOrigin: true,
                },
                '^/images/.*': {
                    target: 'http://***********:9009',
                    changeOrigin: true,
                }
            }
        },

        // CSS配置
        css: {
            preprocessorOptions: {
                //对css预处理器默认配置的覆盖
            },
            devSourcemap: true,
            postcss: {}
        },

        // 解析配置
        resolve: {
            ...baseConfig.resolve,
            alias: {
                ...baseConfig.resolve?.alias,
                '@': resolve(__dirname, 'src'),
            },
        },

        // 构建配置
        build: {
            // 产物目录
            outDir: 'dist',
            // 启用/禁用 CSS 代码拆分
            cssCodeSplit: true,
            // 生成 source map
            sourcemap: false,
            // 自定义底层的 Rollup 打包配置
            rollupOptions: {
                // 确保外部化处理那些不想打包进库的依赖
            }
        }
    }
})

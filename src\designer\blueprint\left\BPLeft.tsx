/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {useEffect, useRef} from "react";
import './BPLeft.less';
import bluePrintManager from "../manager/BluePrintManager.ts";
import bpLeftStore from "./BPLeftStore";
import {observer} from "mobx-react";
import layerManager from "../../manager/LayerManager.ts";
import IdGenerate from "../../../utils/IdGenerate";
import DragAddProvider from "../../../framework/drag-scale/DragAddProvider";
import Input from "../../../json-schema/ui/input/Input.tsx";
import {AddSubset, ApplicationOne, BytedanceMiniApp, CardTwo, MindmapMap, Calculator, Timer} from "@icon-park/react";
import globalVariableManager from "../../manager/GlobalVariableManager.ts";
import editorDesignerLoader from "../../loader/EditorDesignerLoader.ts";

const BPLeft: React.FC = () => {
    return (
        <div className={'bp-left'}>
            <BPNodeSortList/>
            <BPNodeList/>
        </div>
    )
}
export default BPLeft;

export const BPNodeSortList = observer(() => {
    const {activeMenu} = bpLeftStore;
    const nodeSortList = [
        {
            icon: <ApplicationOne/>,
            label: '图层节点',
            key: 'layer'
        },
        {
            icon: <MindmapMap/>,
            label: '逻辑节点',
            key: 'logical'
        },
        {
            icon: <Calculator/>,
            label: '全局变量',
            key: 'global'
        },
        // {
        //     icon: <Filter/>,
        //     label: '过滤器',
        //     key: 'filter'
        // }
    ]
    return (
        <div className={'bp-node-sort-list'}>
            {
                nodeSortList.map((item, index) => {
                    return (
                        <div className={`bp-left-item ${activeMenu === item.key ? "bp-left-item-active" : ""}`}
                             key={index} onClick={() => {
                            bpLeftStore.setActiveMenu(item.key)
                        }}>
                            <div className={'bp-item-icon'}>{item.icon}</div>
                            <div className={'bp-item-label'}>{item.label}</div>
                        </div>
                    )
                })
            }
        </div>
    )
})

//拖拽开始
const dragStart = (event: DragEvent) => {
    if ((event.target as HTMLElement).classList.contains('bp-drag-node')) {
        const element = event.target as HTMLElement;
        // 设置拖拽数据
        event.dataTransfer?.setData('nodeId', element.getAttribute('data-id')!);
        event.dataTransfer?.setData('type', element.getAttribute('data-type')!);
    }
}
//拖拽覆盖
const dragover = (event: DragEvent) => {
    event.preventDefault(); // 阻止默认行为以允许拖放
}
//释放拖拽元素
const drop = (event: DragEvent) => {
    event.preventDefault();
    let nodeId = event.dataTransfer?.getData('nodeId');
    const type = event.dataTransfer?.getData('type');
    const {bpDragContentRef, canvasScale} = bluePrintManager;
    const contentPos = bpDragContentRef?.getBoundingClientRect();
    //获取鼠标位置
    const position = {
        x: (event.clientX - (contentPos?.x || 0)) / canvasScale,
        y: (event.clientY - (contentPos?.y || 0)) / canvasScale
    };
    if (type === 'layer-node') {
        const {setUsedLayerNodes} = bpLeftStore;
        setUsedLayerNodes(nodeId!, true);
    } else if (type === 'global-variable-node') {
        const {setUsedGlobalVariableNodes} = bpLeftStore;
        setUsedGlobalVariableNodes(nodeId!, true);
        // 增加全局变量的引用计数
        globalVariableManager.incrementReferenceCount(nodeId!);
        // 全局变量节点使用变量ID作为节点ID，不需要重新生成
    } else {
        //非图层节点和全局变量节点，需要单独生成一个唯一节点id
        nodeId = IdGenerate.generateId();
    }
    const {addBPNodeLayout} = bluePrintManager;
    addBPNodeLayout({id: nodeId, type, position});
}

export const BPNodeList = observer(() => {
    const {activeMenu, setSearchValue} = bpLeftStore;
    const NodeList = nodeListMapping[activeMenu];
    const dragAddProvider = useRef<DragAddProvider | null>(null);

    useEffect(() => {
        dragAddProvider.current = new DragAddProvider(
            document.getElementById("bp-node-draggable")!,
            document.getElementById("bp-ds-container")!,
            dragStart,
            dragover,
            drop
        );

        return () => dragAddProvider.current?.destroy();
    }, [activeMenu])
    return (
        <div className={'bp-node-list'}>
            <div className={'bp-node-list-header'}>
                <div className={'bp-node-list-search'}>
                    <Input placeholder={'搜索节点'} containerStyle={{width: '100%'}}
                           onChange={(value) => setSearchValue(value)}/>
                </div>
            </div>
            <div className={'bp-node-list-body'}>
                <div className={'bp-node-list-container'} id={'bp-node-draggable'} style={{overflow: "scroll"}}>
                    {NodeList && <NodeList/>}
                </div>
            </div>
        </div>
    )
})

export const BPLayerNodeList = observer(() => {
    const {layerConfigs} = layerManager;
    const {usedLayerNodes, searchValue} = bpLeftStore;
    let layerIdList = layerConfigs ? Object.keys(layerConfigs) : [];

    // 过滤掉不应该在蓝图中显示的组件
    layerIdList = layerIdList.filter((key) => {
        const item = layerConfigs[key];
        const {definitionMap} = editorDesignerLoader;
        const definition = definitionMap[item.type!];
        // 检查组件定义是否支持在蓝图中显示
        return definition && definition.isBlueprintVisible !== false;
    });

    if (searchValue !== '') {
        layerIdList = layerIdList.filter((key) => {
            const item = layerConfigs[key];
            return item.name?.indexOf(bpLeftStore.searchValue) !== -1
        })
    }
    return (
        <>
            {
                layerIdList.map((key, index) => {
                    const item = layerConfigs[key];
                    const used = usedLayerNodes[key];
                    return (
                        <div className={`bp-node-list-item bp-drag-node ${used ? 'bp-node-list-item-used' : ''}`}
                             data-id={item.id}
                             data-type={'layer-node'}
                             draggable={!used} key={index}>
                            <div className={'bpn-li-icon'}><CardTwo/></div>
                            <div className={'bpn-li-label'}>{item.name}</div>
                        </div>
                    )
                })
            }
        </>
    )
})

export const BPLogicalNodeList = observer(() => {

    const logicalNodeList = [
        {name: '条件判断', icon: AddSubset, type: 'condition-node'},
        {name: '逻辑处理', icon: BytedanceMiniApp, type: 'logical-process-node'},
        {name: '定时器', icon: Timer, type: 'timer-node'},
    ].filter((item) => item.name.indexOf(bpLeftStore.searchValue) !== -1)

    return (
        <>
            {
                logicalNodeList.map((item, index) => {
                    return (
                        <div className={`bp-node-list-item bp-drag-node`}
                             data-type={item.type}
                             draggable={true} key={index}>
                            <div className={'bpn-li-icon'}>
                                <item.icon/>
                            </div>
                            <div className={'bpn-li-label'}>{item.name}</div>
                        </div>
                    )
                })
            }
        </>
    )
})

export const BPGlobalVariableNodeList = observer(() => {
    const {usedGlobalVariableNodes, searchValue} = bpLeftStore;

    // 获取所有全局变量定义
    const allVariables = globalVariableManager.getAllVariableDefinitions();

    // 根据搜索值过滤变量
    const filteredVariables = allVariables.filter((variable) =>
        variable.name.indexOf(searchValue) !== -1
    );

    return (
        <>
            {
                filteredVariables.map((variable, index) => {
                    const used = usedGlobalVariableNodes[variable.id];
                    return (
                        <div className={`bp-node-list-item bp-drag-node ${used ? 'bp-node-list-item-used' : ''}`}
                             data-id={variable.id}
                             data-type={'global-variable-node'}
                             draggable={!used}
                             key={index}>
                            <div className={'bpn-li-icon'}>
                                <Calculator/>
                            </div>
                            <div className={'bpn-li-label'}>{variable.name}</div>
                        </div>
                    )
                })
            }
        </>
    )
})

// 保持向后兼容
export const BPGlobalVariablesNodeList = BPGlobalVariableNodeList;

export const BPFilterNodeList = () => {
    return (
        <div>开发中...</div>
    )

}

export const nodeListMapping: { [key: string]: React.FC } = {
    'layer': BPLayerNodeList,
    'logical': BPLogicalNodeList,
    'global': BPGlobalVariableNodeList,
    'filter': BPFilterNodeList
}

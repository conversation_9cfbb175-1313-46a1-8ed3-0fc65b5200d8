/**
 * 组织架构图组件
 * 基于ReactFlow实现
 */

import React, {ForwardedRef, useEffect, useImperativeHandle, useRef, useState, useCallback} from 'react';
import {ComponentBaseProps} from "../../common-component/CommonTypes.ts";
import {DesignerMode} from '../../../designer/DesignerType';
import {
    ReactFlow,
    Node,
    Edge,
    useNodesState,
    useEdgesState,
    ConnectionMode,
    ReactFlowProvider,
    useReactFlow,
    NodeTypes,
    Handle,
    Position
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
// 隐藏ReactFlow右下角标识的CSS
import './OrgChartComponent.css';
// 导入背景图片
import fromBg from './from.png';
import toBg from './to.png';
// 导入缩放观察者
import AbstractScaleObserver from "../../../framework/core/AbstractScaleObserver.ts";
import ScaleAction from "../../../framework/core/ScaleAction.ts";
import canvasManager from "../../../designer/header/items/canvas/CanvasManager.ts";

// ReactFlow缩放观察者类 - 通过CSS样式隔离外部缩放影响
class ReactFlowScaleObserver extends AbstractScaleObserver {
    private containerElement: HTMLElement | null = null;
    private isAdjusting = false;
    private currentScale = { x: 1, y: 1 };
    private originalStyle: string = '';

    constructor(containerElement: HTMLElement) {
        super();
        this.containerElement = containerElement;
        // 保存原始样式
        this.originalStyle = containerElement.style.cssText;
        ScaleAction.add(this);
    }

    doScale(xScale: number, yScale: number): void {
        if (!this.containerElement || this.isAdjusting) return;

        this.isAdjusting = true;
        this.currentScale = { x: xScale, y: yScale };

        try {
            // 获取当前适配模式
            const adaptationType = canvasManager.canvasConfig.adaptationType;

            if (adaptationType === 'scale') {
                // 自适应模式：使用统一缩放比例，保持比例一致
                const uniformScale = Math.min(xScale, yScale);
                const inverseScale = 1 / uniformScale;

                this.containerElement.style.transform = `scale(${inverseScale})`;
                this.containerElement.style.transformOrigin = '0 0';

                // 不调整容器尺寸，让内容保持在可视区域内
                this.containerElement.style.width = '100%';
                this.containerElement.style.height = '100%';

            } else if (adaptationType === 'full-y') {
                // 撑满高度模式：只对Y轴进行补偿
                const inverseYScale = 1 / yScale;

                this.containerElement.style.transform = `scaleY(${inverseYScale})`;
                this.containerElement.style.transformOrigin = '0 0';
                this.containerElement.style.width = '100%';
                this.containerElement.style.height = `${100 * yScale}%`;
            }

        } catch (error) {
            console.warn('ReactFlow scale adjustment failed:', error);
        } finally {
            // 延迟重置标志位
            setTimeout(() => {
                this.isAdjusting = false;
            }, 50);
        }
    }

    destroy() {
        // 恢复原始样式
        if (this.containerElement) {
            this.containerElement.style.cssText = this.originalStyle;
        }

        // 从ScaleAction中移除观察者
        const index = ScaleAction.observers.indexOf(this);
        if (index > -1) {
            ScaleAction.observers.splice(index, 1);
        }
        this.containerElement = null;
    }
}

// 自定义From节点组件
const CustomFromNode: React.FC<{ data: any }> = ({ data }) => {
    const { label, totalCount } = data;

    return (
        <div style={{
            position: 'relative',
            width: 138,
            height: 40, // 增加容器高度以容纳文字和背景图
            backgroundImage: `url(${fromBg})`,
            backgroundSize: '138px 21px', // 固定背景图尺寸
            backgroundPosition: 'center bottom', // 水平居中，垂直底部
            backgroundRepeat: 'no-repeat'
        }}>
            {/* 人数标签 */}
            {totalCount > 0 && (
                <div style={{
                    position: 'absolute',
                    top: -15,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    backgroundColor: 'transparent',
                    color: '#333',
                    fontSize: 12,
                    fontWeight: 500,
                    padding: '4px 12px',
                    borderRadius: '10px',
                    whiteSpace: 'nowrap',
                    zIndex: 10
                }}>
                    {totalCount}人
                </div>
            )}
            {/* 节点文字 */}
            <div style={{
                width: '100%',
                height: '100%',
                color: '#333',
                fontSize: 20,
                fontWeight: 500,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                paddingBottom: '10px' // 向上偏移一点，避免与背景图重叠
            }}>
                {label}
            </div>
            {/* 输出连接点 */}
            <Handle
                type="source"
                position={Position.Bottom}
                id="source"
                style={{ background: '#1890ff', border: 'none' }}
            />
        </div>
    );
};

// 自定义To节点组件
const CustomToNode: React.FC<{ data: any }> = ({ data }) => {
    const { label } = data;

    return (
        <div style={{
            position: 'relative',
            width: 138,
            height: 40, // 增加容器高度以容纳文字和背景图
            backgroundImage: `url(${toBg})`,
            backgroundSize: '138px 21px', // 固定背景图尺寸
            backgroundPosition: 'center bottom', // 水平居中，垂直底部
            backgroundRepeat: 'no-repeat'
        }}>
            {/* 节点文字 */}
            <div style={{
                width: '100%',
                height: '100%',
                color: '#333',
                fontSize: 20,
                fontWeight: 500,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                paddingBottom: '8px' // 向上偏移一点，避免与背景图重叠
            }}>
                {label}
            </div>
            {/* 输入连接点 */}
            <Handle
                type="target"
                position={Position.Top}
                id="target"
                style={{ background: '#fa8c16', border: 'none' }}
            />
        </div>
    );
};

// 定义节点类型（在组件外部，避免重新创建）
const nodeTypes: NodeTypes = {
    customFrom: CustomFromNode,
    customTo: CustomToNode,
};

// 硬编码配置常量 - 方便统一修改
const HARD_CODED_CONFIG = {
    // 缩放配置
    DEFAULT_ZOOM: 1,
    MIN_ZOOM: 0.1,
    MAX_ZOOM: 3,
    ZOOM_ON_DOUBLE_CLICK: false,
    PAN_ON_SCROLL: false,

    // 连接线配置
    EDGE_TYPE: 'default' as const,
    EDGE_COLOR: '#C8DEFF',
    EDGE_WIDTH: 1,
    EDGE_STYLE: 'solid' as const,
    EDGE_ANIMATED: true,
    SHOW_EDGE_LABELS: true, // 改为true，显示连接线标签

    // 节点样式配置
    NODE_WIDTH: 138,  // 改为图片尺寸
    NODE_HEIGHT: 21,  // 改为图片尺寸
    NODE_PADDING: 20, // 节点间距
    FROM_NODE_COLOR: 'transparent', // 改为透明，使用背景图
    TO_NODE_COLOR: 'transparent',   // 改为透明，使用背景图
    NODE_TEXT_COLOR: '#333',        // 改为深色文字
    NODE_FONT_SIZE: 12,
    NODE_FONT_WEIGHT: 500,

    // 布局配置
    FROM_ROW_Y: 50,    // from节点的Y坐标
    TO_ROW_Y: 150,     // to节点的Y坐标
    NODE_SPACING_X: 180, // 节点X轴间距

    // 背景颜色（硬编码）
    BACKGROUND_COLOR: 'transparent',
};

// 节点数据接口 - 去掉x,y坐标，改为自动布局
export interface OrgNodeData {
    id: string;
    type: 'from' | 'to';
    label: string;
    connections?: OrgEdgeData[];
}

// 连接线数据接口 - 支持人数显示
export interface OrgEdgeData {
    target: string;
    count?: number;  // 人数
    label?: string;  // 自定义标签（可选）
}

// 组件样式配置接口 - 简化版，只保留必要配置
export interface OrgChartComponentStyle {
    // 基础交互配置
    zoomOnScroll?: boolean;    // 滚轮缩放
    panOnDrag?: boolean;       // 拖拽平移
    nodeDraggable?: boolean;   // 节点可拖拽

    // 视图状态保存（内部使用，不在配置面板显示）
    viewport?: { x: number; y: number; zoom: number };
    nodePositions?: { id: string; x: number; y: number }[];
}

export interface OrgChartComponentProps extends ComponentBaseProps {
    style?: OrgChartComponentStyle;
    data?: {
        sourceType?: string;
        staticData?: OrgNodeData[];
    };
}

export interface OrgChartComponentRef {
    updateConfig: (newConfig: OrgChartComponentProps) => void;
    updateData: (newData: OrgNodeData[]) => void;
    setEventHandler: (eventMap: Record<string, Function>) => void;
    getCurrentConfig: () => OrgChartComponentProps;
}

// 内部ReactFlow组件，用于使用useReactFlow hook
const InnerReactFlow: React.FC<{
    localConfig: OrgChartComponentProps;
    nodes: Node[];
    edges: Edge[];
    nodeTypes: NodeTypes;
    handleNodesChange: any;
    onEdgesChange: any;
    onNodeClick: any;
    onNodeDoubleClick: any;
    onViewportChange: (viewport: { x: number; y: number; zoom: number }) => void;
    isSettingViewport: React.MutableRefObject<boolean>;
    containerRef: React.RefObject<HTMLDivElement>;
}> = ({ localConfig, nodes, edges, nodeTypes, handleNodesChange, onEdgesChange, onNodeClick, onNodeDoubleClick, onViewportChange, isSettingViewport, containerRef }) => {
    const reactFlowInstance = useReactFlow();
    const style = localConfig?.style || {};
    const scaleObserverRef = useRef<ReactFlowScaleObserver | null>(null);

    // 检查当前模式
    const isDesignerMode = window.LC_ENV?.mode === DesignerMode.EDIT;

    // 在/designer模式下，需要特殊处理panOnDrag
    // 因为组件本身的拖拽会与ReactFlow的拖拽冲突
    const effectivePanOnDrag = isDesignerMode
        ? (style?.panOnDrag !== false) // 在designer模式下，默认启用拖拽平移
        : (style?.panOnDrag !== false); // 在view模式下，使用配置值



    // 初始化viewport设置和缩放观察者
    useEffect(() => {
        if (reactFlowInstance) {
            isSettingViewport.current = true; // 设置标志位

            if (style.viewport) {
                // 恢复保存的viewport状态
                reactFlowInstance.setViewport(style.viewport);
            } else {
                // 使用硬编码的默认缩放
                reactFlowInstance.setViewport({ x: 0, y: 0, zoom: HARD_CODED_CONFIG.DEFAULT_ZOOM });
            }

            // 延迟重置标志位，确保setViewport完成
            setTimeout(() => {
                isSettingViewport.current = false;
            }, 100);

            // 只在/view模式下且特定适配模式下初始化缩放观察者
            if (!isDesignerMode && containerRef.current) {
                // 检查当前的适配模式，只在可能出现错位的模式下启用
                const adaptationType = canvasManager.canvasConfig.adaptationType;
                const needsScaleObserver = adaptationType === 'full-y' || adaptationType === 'scale';

                // 清理之前的观察者
                if (scaleObserverRef.current) {
                    scaleObserverRef.current.destroy();
                    scaleObserverRef.current = null;
                }

                // 只在需要的适配模式下创建缩放观察者
                if (needsScaleObserver) {
                    scaleObserverRef.current = new ReactFlowScaleObserver(containerRef.current);
                }
            }
        }

        // 清理函数
        return () => {
            if (scaleObserverRef.current) {
                scaleObserverRef.current.destroy();
                scaleObserverRef.current = null;
            }
        };
    }, [reactFlowInstance, isDesignerMode]); // 依赖reactFlowInstance和模式

    // 移除defaultZoom配置处理，使用硬编码配置

    return (
        <ReactFlow
            nodes={nodes}
            edges={edges}
            nodeTypes={nodeTypes}
            onNodesChange={handleNodesChange}
            onEdgesChange={onEdgesChange}
            onNodeClick={onNodeClick}
            onNodeDoubleClick={onNodeDoubleClick}
            onViewportChange={onViewportChange}
            zoomOnScroll={style?.zoomOnScroll !== false}
            zoomOnPinch={true} // 硬编码：总是启用触摸缩放
            zoomOnDoubleClick={HARD_CODED_CONFIG.ZOOM_ON_DOUBLE_CLICK}
            panOnScroll={HARD_CODED_CONFIG.PAN_ON_SCROLL}
            panOnDrag={effectivePanOnDrag}
            minZoom={HARD_CODED_CONFIG.MIN_ZOOM}
            maxZoom={HARD_CODED_CONFIG.MAX_ZOOM}
            connectionMode={ConnectionMode.Loose}
            connectOnClick={false} // 禁用点击连接
            nodesDraggable={style.nodeDraggable !== false}
            nodesConnectable={false} // 禁用节点连接
            elementsSelectable={false} // 禁用元素选择
            fitView={false} // 禁用自动fitView，使用手动控制
            style={{
                width: '100%',
                height: '100%',
                backgroundColor: HARD_CODED_CONFIG.BACKGROUND_COLOR
            }}
        >
        </ReactFlow>
    );
};

const OrgChartComponent = React.forwardRef<OrgChartComponentRef, OrgChartComponentProps>((props, ref: ForwardedRef<OrgChartComponentRef>) => {
    const [localConfig, setLocalConfig] = useState<OrgChartComponentProps>({...props});
    const eventHandlerMap = useRef<Record<string, Function>>({});
    const isSettingViewport = useRef(false); // 标志位，避免无限循环
    const latestConfigRef = useRef<OrgChartComponentProps>({...props}); // 保存最新配置的ref
    const containerRef = useRef<HTMLDivElement>(null); // 容器ref，用于缩放观察者

    // ReactFlow 状态
    const [nodes, setNodes, onNodesChange] = useNodesState([] as Node[]);
    const [edges, setEdges, onEdgesChange] = useEdgesState([] as Edge[]);

    // 包装onNodesChange来保存节点位置变化
    const handleNodesChange = useCallback((changes: any[]) => {
        onNodesChange(changes);

        // 检查是否有位置变化
        const hasPositionChange = changes.some(change => change.type === 'position' && change.dragging === false);
        if (hasPositionChange) {
            // 延迟保存，确保nodes状态已更新
            setTimeout(() => {
                setNodes(currentNodes => {
                    // 将当前节点位置保存到配置中
                    const nodePositions = currentNodes.map(node => ({
                        id: node.id,
                        x: node.position.x,
                        y: node.position.y
                    }));

                    setLocalConfig(prev => {
                        const newConfig = {
                            ...prev,
                            style: {
                                ...prev?.style,
                                nodePositions
                            }
                        };

                        // 立即更新ref，确保getCurrentConfig能获取到最新配置
                        latestConfigRef.current = newConfig;

                        return newConfig;
                    });

                    return currentNodes;
                });
            }, 100);
        }
    }, [onNodesChange, setNodes]);

    // viewport状态保存处理
    const handleViewportChange = useCallback((viewport: { x: number; y: number; zoom: number }) => {
        // 如果是程序设置的viewport，不保存
        if (isSettingViewport.current) {
            return;
        }

        // 防抖保存用户的viewport状态
        setTimeout(() => {
            setLocalConfig(prev => {
                const newConfig = {
                    ...prev,
                    style: {
                        ...prev?.style,
                        viewport
                    }
                };

                // 立即更新ref，确保getCurrentConfig能获取到最新配置
                latestConfigRef.current = newConfig;

                return newConfig;
            });
        }, 500); // 500ms防抖
    }, []);







    // 转换数据为ReactFlow格式
    const convertDataToFlow = useCallback((data: OrgNodeData[]) => {
        if (!data || !Array.isArray(data)) {
            return { nodes: [], edges: [] };
        }

        const style = localConfig?.style || {};

        // 分离from和to节点
        const fromNodes = data.filter(node => node.type === 'from');
        const toNodes = data.filter(node => node.type === 'to');

        // 创建节点 - 自动布局
        const flowNodes: Node[] = [];

        // 布局from节点（第一排）
        fromNodes.forEach((nodeData, index) => {
            // 计算该节点连接的人数总和
            const totalCount = nodeData.connections?.reduce((sum, connection) => {
                const count = typeof connection === 'string' ? 0 : (connection.count || 0);
                return sum + count;
            }, 0) || 0;

            // 检查是否有保存的节点位置
            const savedPosition = style.nodePositions?.find(pos => pos.id === nodeData.id);
            const position = savedPosition
                ? { x: savedPosition.x, y: savedPosition.y }
                : {
                    x: index * HARD_CODED_CONFIG.NODE_SPACING_X + HARD_CODED_CONFIG.NODE_PADDING,
                    y: HARD_CODED_CONFIG.FROM_ROW_Y
                };

            // 添加from节点（使用自定义节点类型）
            flowNodes.push({
                id: nodeData.id,
                type: 'customFrom',
                position,
                draggable: style.nodeDraggable !== false,
                data: {
                    label: nodeData.label,
                    totalCount: totalCount,
                    nodeType: nodeData.type,
                    originalData: nodeData
                }
            });
        });

        // 布局to节点（第二排）
        toNodes.forEach((nodeData, index) => {
            // 检查是否有保存的节点位置
            const savedPosition = style.nodePositions?.find(pos => pos.id === nodeData.id);
            const position = savedPosition
                ? { x: savedPosition.x, y: savedPosition.y }
                : {
                    x: index * HARD_CODED_CONFIG.NODE_SPACING_X + HARD_CODED_CONFIG.NODE_PADDING,
                    y: HARD_CODED_CONFIG.TO_ROW_Y
                };

            flowNodes.push({
                id: nodeData.id,
                type: 'customTo',
                position,
                draggable: style.nodeDraggable !== false,
                data: {
                    label: nodeData.label,
                    nodeType: nodeData.type,
                    originalData: nodeData
                }
            });
        });

        // 创建连接线
        const flowEdges: Edge[] = [];
        // 用于跟踪每个目标节点的连接数，以便计算标签偏移
        const targetConnectionCount: Record<string, number> = {};

        data.forEach((nodeData) => {
            if (nodeData.connections && Array.isArray(nodeData.connections)) {
                nodeData.connections.forEach((connection) => {
                    // 兼容旧格式（string）和新格式（OrgEdgeData）
                    const targetId = typeof connection === 'string' ? connection : connection.target;
                    const edgeLabel = typeof connection === 'string' ? undefined : connection.label;
                    const edgeCount = typeof connection === 'string' ? undefined : connection.count;

                    // 计算标签偏移，避免重叠
                    if (!targetConnectionCount[targetId]) {
                        targetConnectionCount[targetId] = 0;
                    }
                    const connectionOrder = targetConnectionCount[targetId];
                    targetConnectionCount[targetId]++;

                    // 使用硬编码的连接线样式，根据连接顺序调整样式
                    const edgeStyle: any = {
                        stroke: HARD_CODED_CONFIG.EDGE_COLOR,
                        strokeWidth: HARD_CODED_CONFIG.EDGE_WIDTH,
                        // 通过调整strokeDashoffset来让标签错开
                        strokeDashoffset: connectionOrder * 10
                    };

                    // 硬编码的标签显示逻辑
                    let displayLabel = undefined;
                    if (HARD_CODED_CONFIG.SHOW_EDGE_LABELS) {
                        if (edgeLabel) {
                            // 使用自定义标签
                            displayLabel = edgeLabel;
                        } else if (edgeCount !== undefined) {
                            // 显示人数信息
                            displayLabel = `${edgeCount}人`;
                        } else {
                            // 默认显示连接关系
                            displayLabel = `${nodeData.label} → ${targetId}`;
                        }
                    }

                    // 计算标签偏移量，避免重叠
                    const labelOffsetX = (connectionOrder - 1) * 10; // 水平偏移
                    const labelOffsetY = connectionOrder * 5; // 垂直偏移

                    flowEdges.push({
                        id: `${nodeData.id}-${targetId}`,
                        source: nodeData.id,
                        target: targetId,
                        type: HARD_CODED_CONFIG.EDGE_TYPE,
                        style: edgeStyle,
                        animated: HARD_CODED_CONFIG.EDGE_ANIMATED,
                        label: displayLabel,
                        labelStyle: {
                            fill: '#333',
                            fontWeight: 500,
                            fontSize: 12,
                            background: 'transparent',
                            backgroundColor: 'transparent',
                            transform: `translate(${labelOffsetX}px, ${labelOffsetY}px)` // 应用偏移
                        },
                        labelBgStyle: {
                            fill: 'transparent',
                            fillOpacity: 0
                        },
                        labelBgPadding: [2, 4],
                        labelBgBorderRadius: 2,
                        data: {
                            count: edgeCount,
                            label: edgeLabel,
                            connectionOrder: connectionOrder,
                            labelOffsetX: labelOffsetX,
                            labelOffsetY: labelOffsetY
                        }
                    });
                });
            }
        });

        return { nodes: flowNodes, edges: flowEdges };
    }, [localConfig?.style]);

    // 初始化和更新数据
    useEffect(() => {
        const data = localConfig?.data?.staticData || [];
        const { nodes: flowNodes, edges: flowEdges } = convertDataToFlow(data);
        setNodes(flowNodes);
        setEdges(flowEdges);
    }, [localConfig?.data?.staticData]);

    // 简化的style配置变化处理 - 只处理nodeDraggable变化，保持节点位置
    const prevStyleRef = useRef(localConfig?.style);
    useEffect(() => {
        const currentStyle = localConfig?.style;
        const prevStyle = prevStyleRef.current;

        if (!currentStyle || !prevStyle) {
            prevStyleRef.current = currentStyle;
            return;
        }

        // 只有nodeDraggable配置变化时才需要更新nodes的draggable属性
        if (currentStyle.nodeDraggable !== prevStyle.nodeDraggable) {
            setNodes(currentNodes =>
                currentNodes.map(node => ({
                    ...node,
                    draggable: currentStyle.nodeDraggable !== false
                }))
            );
        }

        prevStyleRef.current = currentStyle;
    }, [localConfig?.style]);

    // 节点点击事件
    const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
        if ('nodeClick' in eventHandlerMap.current) {
            eventHandlerMap.current['nodeClick'](node.data.originalData);
        }
    }, []);

    // 节点双击事件
    const onNodeDoubleClick = useCallback((event: React.MouseEvent, node: Node) => {
        if ('nodeDoubleClick' in eventHandlerMap.current) {
            eventHandlerMap.current['nodeDoubleClick'](node.data.originalData);
        }
    }, []);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
        updateConfig: (newConfig) => {
            setLocalConfig(prevConfig => {
                const merged = {...prevConfig, ...newConfig};

                // 立即更新ref
                latestConfigRef.current = merged;

                // 简化的style配置处理 - 只处理nodeDraggable，保持节点位置
                if (newConfig.style && newConfig.style.nodeDraggable !== undefined) {
                    setNodes(currentNodes =>
                        currentNodes.map(node => ({
                            ...node,
                            draggable: newConfig.style!.nodeDraggable !== false
                        }))
                    );
                }

                return merged;
            });
        },
        updateData: (newData) => {
            const { nodes: flowNodes, edges: flowEdges } = convertDataToFlow(newData);
            setNodes(flowNodes);
            setEdges(flowEdges);
        },
        setEventHandler: (eventMap) => eventHandlerMap.current = eventMap,
        getCurrentConfig: () => {
            return latestConfigRef.current;
        },
    }));

    // 检查当前模式，在/designer模式下需要特殊处理
    const isDesignerMode = window.LC_ENV?.mode === DesignerMode.EDIT;

    // 在/designer模式下，如果启用了拖拽平移，需要确保容器不阻止事件
    const containerStyle: React.CSSProperties = {
        width: '100%',
        height: '100%',
        border: isDesignerMode ? '1px solid #ccc' : 'none'
    };

    // 在/designer模式下，如果启用了拖拽平移，确保事件能正确传递
    if (isDesignerMode && localConfig?.style?.panOnDrag !== false) {
        // 在designer模式下，让ReactFlow的拖拽平移能够正常工作
        containerStyle.pointerEvents = 'auto';
    }

    return (
        <div ref={containerRef} style={containerStyle}>
            <ReactFlowProvider>
                <InnerReactFlow
                    localConfig={localConfig}
                    nodes={nodes}
                    edges={edges}
                    nodeTypes={nodeTypes}
                    handleNodesChange={handleNodesChange}
                    onEdgesChange={onEdgesChange}
                    onNodeClick={onNodeClick}
                    onNodeDoubleClick={onNodeDoubleClick}
                    onViewportChange={handleViewportChange}
                    isSettingViewport={isSettingViewport}
                    containerRef={containerRef}
                />
            </ReactFlowProvider>
        </div>
    );
});

export default OrgChartComponent;

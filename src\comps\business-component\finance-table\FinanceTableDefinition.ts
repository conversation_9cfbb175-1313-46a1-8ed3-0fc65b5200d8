/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {BaseInfoType, EventInfo, MenuToConfigMappingType} from "../../../framework/core/AbstractDefinition.ts";
import {ClazzTemplate} from "../../common-component/CommonTypes.ts";
import {MenuInfo} from "../../../designer/right/MenuType.ts";
import financeTableImg from './finance-table.png';
import {FinanceTableController} from "./FinanceTableController.ts";
import {FinanceTableComponentProps} from "./FinanceTableComponent.tsx";
import {FinanceTableStyleConfig} from "./FinanceTableConfig.tsx";
import AbstractDesignerDefinition from "../../../framework/core/AbstractDesignerDefinition.ts";

export default class FinanceTableDefinition extends AbstractDesignerDefinition<FinanceTableController, FinanceTableComponentProps> {
    getBaseInfo(): BaseInfoType {
        return {
            compName: "财务数据表格",
            compKey: "LcFinanceTable",
            categorize: "business",
        };
    }

    getChartImg(): string | null {
        return financeTableImg;
    }

    getController(): ClazzTemplate<FinanceTableController> | null {
        return FinanceTableController;
    }

    getInitConfig(): FinanceTableComponentProps {
        const sampleData = {
            header: ["项目", "2025预算", "1季度完成", "4月完成", "5月完成", "6月完成", "累计完成", "累计完成率"],
            body: [
                ["营业收入", '-', '-', '-', '-', '-', '-', "-"],
                ["营业成本", '-', '-', '-', '-', '-', '-', "-"],
                ["毛利润", '-', '-', '-', '-', '-', '-', "-"],
                ["销售费用", '-', '-', '-', '-', '-', '-', "-"],
                ["管理费用", '-', '-', '-', '-', '-', '-', "-"],
                ["财务费用", '-', '-', '-', '-', '-', '-', "-"],
                ["净利润", '-', '-', '-', '-', '-', '-', "-"]
            ]
        };

        return {
            base: {
                id: "",
                name: '财务数据表格',
                type: 'LcFinanceTable',
            },
            filter: {
                enable: false,
                blur: 0,
                brightness: 1,
                contrast: 1,
                opacity: 1,
                saturate: 1,
                hueRotate: 0
            },
            // 标准数据配置格式
            data: {
                sourceType: 'static',
                staticData: sampleData
            },
        };
    }

    getMenuList(): Array<MenuInfo> {
        // 保留基础、样式、数据、滤镜菜单，移除主题菜单
        return super.getMenuList().filter((item: MenuInfo) => item.key !== 'theme');
    }

    getMenuToConfigContentMap(): MenuToConfigMappingType {
        const menuMapping = super.getMenuToConfigContentMap();
        menuMapping['style'] = FinanceTableStyleConfig;
        // 确保数据配置存在（父类已包含，这里只是确认）
        // menuMapping['data'] 已由父类提供
        return menuMapping;
    }

    getEventList(): Array<EventInfo> {
        return [
            ...super.getEventList(),
            {
                id: "dataChange",
                name: "数据变更时",
            }
        ]
    }
}
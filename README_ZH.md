# LIGHT CHASER

<p>
    <img alt="" src="https://img.shields.io/badge/version-v1.5.0-blue">
    <img alt="" src="https://img.shields.io/badge/license-MIT-08CE5D?logoColor=08CE5D">
    <img alt="" src="https://img.shields.io/badge/TypeScript-blue">
    <img alt="" src="https://img.shields.io/badge/React-61daeb?logoColor=08CE5D">
    <img alt="" src="https://img.shields.io/badge/Vite-purple">
    <img alt="" src="https://img.shields.io/badge/Mobx-FFEB0B">
</p>

<p>数据可视化工具</p>

light chaser 是一款开源免费的数据可视化设计工具，基于 React18、Vite5、TypeScript5 技术栈实现。
通过它，你可以简单快速地制作数据可视化相关内容。它可用于大屏幕数据 可视化展示、数据报告、数据分析等场景。

light chaser 专为数据可视化而设计，并将不断发展和完善！

开发文档：https://xiaopujun.github.io/light-chaser-doc

在线体验：https://xiaopujun.github.io/light-chaser-app

Pro在线体验：http://www.lcdesigner.cn/home/<USER>

开源后端：https://github.com/xiaopujun/light-chaser-server

# 软件特点

- 拖拽式设计：所有组件坐标、尺寸均支持拖拽式操作
- 性能优秀：面向对象式管理组件状态，所有组件可独立渲染。可承载1000+组件在画布上的同时渲染
- 扩展性强：支持自定义G2Plot系列图表、Echarts系列图表。支持以服务器组件的方式扩展自己的业务组件或者二开组件
- 数据源丰富：支持MySQL、Oracle、PostgreSQL、SQLServer等关系型数据库、API、公共API、WebSocket、MQTT等多种数据源
- 事件交互简单：支持蓝图节点系统，通过线段链接各组件之间的交互，配合各种功能性节点，数据联动无比简单
- 平台无关：支持Window、Linux、Mac等所有支持现代浏览器的操作系统

# 功能介绍（包含Pro版本功能）

- 项目管理：支持项目快速创建、增删改、克隆、封面
- 数据源管理：支持多种关系型数据库、API、WebSocket、MQTT等数据源
- 用户管理（Pro）：支持用户角色管理
- 服务器组件（Pro）：支持以服务器组件的方式扩展自己的业务组件，服务器组件使用专门脚手架开发
- 组件库：支持50+组件及G2Plot、Echarts系列图表库的自定义组件，支持高德地图自定义组件
- 资源库：支持图片资源复用
- 图层管理：支持图层顺序，层级管理，编组、解组、拖拽移入、移出分组
- 画布设置：支持画布自适应（等比缩放、宽度撑满、高度撑满等），画布尺寸调整，组件移动、缩放栅格化。
- 主题设置：支持主题自定义、支持单个组件主题、全局主题
- 导入导出：支持直接导出项目数据json文件
- 预览/发布：支持项目预览、覆盖发布、增量发布、加密发布
- 画布吸附：支持组件拖拽固定距离吸附、引导线、座标尺寸实时更新显示
- 快捷键：支持win/mac系统的35+快捷键操作，提升操作效率
- 蓝图编辑器：支持蓝图节点系统，支持图层节点、全局变量节点，逻辑操作、条件判断、全局节点等多种功能节点
- 全局变量：数据源支持绑定全局变量，动态修改api参数、支持主动渲染实时更新组件数据状态

# 示例

#### 主编辑器

![示例1](https://s2.loli.net/2024/09/21/U2Ni3pfaE1rJVAM.png)

#### 可视化事件编辑器

![示例2](https://s2.loli.net/2024/09/26/pitkUF2GogRYnxO.jpg)

#### 设计完成图1

![示例3](https://i.072333.xyz/file/802e2d2b4d95fa32fae48.png)

#### 设计完成图2

![示例4](https://i.072333.xyz/file/51819086932cb1b559a15.png)

# 部署和使用

LIGHT CHASER 支持常规部署和docker部署，请参考以下连接：

部署教程：https://xiaopujun.github.io/light-chaser-doc/#/deploy/deploy_open

# License

[MIT](LICENSE) © xiaopujun

补充条款（与补充条款产生冲突的以补充条款为准）

1. 本项目面向个人和企业用户在本协议允许的范围内免费使用或商用
2. 项目作者保留对本项目的所有知识产权，个人或企业在使用本项目二次开发后的软件可以进行本协议允许范围内的商业化使用，但不得以本项目的源代码进行版权声明或确权
3. 个人或企业使用本项目或者基于本项目二次开发的软件进行商业使用的范围仅限于个人或企业自身的业务，不得直接将本项目构建的软件、项目源码、基于本项目的衍生品、二进制文件、文档、图片等直接进行出售。若需商业化，请联系作者获取授权。
4. 本项目已申请软件著作权，我们尊重开源产品的理念，同时呼吁用户尊重作者所付出的劳动成果。 
5. 对于因使用本软件而产生的一切法律纠纷和责任，均与作者无关，用户需自行承担相应后果。

感谢为本项目做出贡献的每一个人！

# 社区 & 可持续发展

如果你对LIGHT CHASER项目感兴趣，欢迎加入社区群聊，反馈问题。帮助我们改进LIGHT CHASER。

<div style="display: flex">
    <img style="width: 300px" alt="group" src="https://s2.loli.net/2025/05/14/qiuY7TAS3xB4DF9.jpg">
</div>

LIGHT CHASER 现已推出Pro版本。如果您有捐赠，商业合作想法，请赞助我，我将送出LIGHT CHASER Pro版本作为感谢。

现在你已经可以通过http://www.lcdesigner.cn/   账：ee0274ed 密：ee0274ed 快速体验Pro的所有功能

感谢每一位项目贡献者、捐赠者和赞助商

# 联系作者

<div style="display: flex">
    <div  style="width: 50%"><img alt="group" src="https://i.072333.xyz/file/110e9602ef12a7d93bff0.jpg"></div>
</div>


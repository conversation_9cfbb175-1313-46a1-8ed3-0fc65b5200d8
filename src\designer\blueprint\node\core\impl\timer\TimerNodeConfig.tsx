/*
 * @Author: <EMAIL>
 * @Date: 2024-05-26 12:00:00
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-05-26 12:00:00
 * @Description: 定时器蓝图节点配置组件
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {useState, useEffect} from 'react';
import {Typography, Switch, InputNumber, Divider} from 'antd';
import {BPRightConfigProps} from '../../../../right/BPRight';
import {TimerNodeConfigType} from './BPTimerNodeController';

const {Text, Title} = Typography;

export const TimerNodeConfig: React.FC<BPRightConfigProps> = ({controller}) => {
    const [config, setConfig] = useState<TimerNodeConfigType>({
        isLoop: true,
        interval: 1000,
        autoStart: false
    });

    // 获取节点配置
    useEffect(() => {
        if (controller) {
            const currentConfig = controller.getConfig() as TimerNodeConfigType;
            if (currentConfig) {
                setConfig(currentConfig);
            }
        }
    }, [controller]);

    // 更新配置
    const updateConfig = (newConfig: Partial<TimerNodeConfigType>) => {
        const updatedConfig = {...config, ...newConfig};
        setConfig(updatedConfig);

        if (controller) {
            controller.update(updatedConfig);
        }
    };



    return (
        <div style={{padding: '16px'}}>
            <Title level={5}>定时器节点配置</Title>

            {/* 自动启动设置 */}
            <div style={{marginBottom: '16px', display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                <div>
                    <Text strong>自动启动：</Text>
                    <div style={{marginTop: '2px'}}>
                        <Text type="secondary" style={{fontSize: '11px'}}>
                            开启后无需外部触发，节点创建时自动开始执行
                        </Text>
                    </div>
                </div>
                <Switch
                    checked={config.autoStart}
                    onChange={(checked) => updateConfig({autoStart: checked})}
                />
            </div>

            <Divider />

            {/* 循环设置 */}
            <div style={{marginBottom: '16px', display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
                <Text strong>循环：</Text>
                <Switch
                    checked={config.isLoop}
                    onChange={(checked) => updateConfig({isLoop: checked})}
                />
            </div>

            {/* 时间间隔设置 */}
            <div style={{marginBottom: '16px'}}>
                <div style={{marginBottom: '8px'}}>
                    <Text strong>时间间隔：</Text>
                </div>
                <InputNumber
                    value={config.interval}
                    onChange={(value) => updateConfig({interval: value || 1000})}
                    min={100}
                    max={3600000}
                    step={100}
                    addonAfter="毫秒"
                    style={{width: '100%'}}
                />
                <div style={{marginTop: '4px'}}>
                    <Text type="secondary" style={{fontSize: '12px'}}>
                        范围：100ms - 3600000ms (1小时)
                    </Text>
                </div>
            </div>

            <Divider />

            {/* 锚点说明 */}
            <div style={{padding: '12px', borderRadius: '4px', border: '1px solid #eee'}}>
                <Title level={5}>锚点说明</Title>
                <div style={{marginBottom: '8px'}}>
                    <Text strong>输入锚点：</Text>
                </div>
                <div style={{marginLeft: '16px', marginBottom: '4px'}}>
                    <Text>• <Text code>启动</Text>：启动定时器</Text>
                </div>
                <div style={{marginLeft: '16px', marginBottom: '8px'}}>
                    <Text>• <Text code>停止</Text>：停止定时器</Text>
                </div>
                
                <div style={{marginBottom: '8px'}}>
                    <Text strong>输出锚点：</Text>
                </div>
                <div style={{marginLeft: '16px', marginBottom: '8px'}}>
                    <Text>• <Text code>定时执行</Text>：按设定间隔触发执行</Text>
                    <div style={{marginLeft: '16px', marginTop: '2px'}}>
                        <Text type="secondary" style={{fontSize: '11px'}}>
                            输出信息：{`{nodeId, timestamp, interval}`}
                        </Text>
                    </div>
                </div>
                
                <div style={{marginTop: '8px', padding: '8px', borderRadius: '4px'}}>
                    <Text type="secondary" style={{fontSize: '12px'}}>
                        💡 <Text strong>使用提示：</Text><br/>
                        • <Text strong>自动启动</Text>：开启后节点创建时自动开始执行，无需外部触发<br/>
                        • <Text strong>手动启动</Text>：关闭自动启动，通过连接"启动"锚点来控制<br/>
                        • <Text strong>循环模式</Text>：按间隔重复执行<br/>
                        • <Text strong>单次模式</Text>：只执行一次后停止
                    </Text>
                </div>
            </div>
        </div>
    );
};

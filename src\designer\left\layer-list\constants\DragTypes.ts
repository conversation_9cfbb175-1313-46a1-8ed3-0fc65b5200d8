/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

/**
 * 图层拖拽类型常量
 */
export const LayerDragTypes = {
  LAYER: 'layer',
};

/**
 * 图层拖拽项接口
 */
export interface LayerDragItem {
  id: string;
  type: string;
  index: number;
  parentId?: string;
}

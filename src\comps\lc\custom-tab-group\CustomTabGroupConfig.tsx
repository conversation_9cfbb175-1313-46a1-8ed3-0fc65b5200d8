/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, {useState} from 'react';
import {Button, Input, List, Space, Select, Popconfirm} from 'antd';
import {Plus, Delete} from '@icon-park/react';
import {ConfigType} from "../../../designer/right/ConfigContent";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI";
import {Control} from "../../../json-schema/SchemaTypes";
import {CustomTabGroupComponentProps, TabConfigItem} from "./CustomTabGroupTypes";
import {CustomTabGroupController} from "./CustomTabGroupController";
import IdGenerate from "../../../utils/IdGenerate";

const CustomTabGroupConfig: React.FC<ConfigType<CustomTabGroupController>> = ({controller}) => {
    const config = controller.getConfig();
    const [tabs, setTabs] = useState<TabConfigItem[]>(config?.style?.tabs || []);
    const [activeTabValue, setActiveTabValue] = useState(config?.style?.activeTabValue);

    const handleTabsChange = (newTabs: TabConfigItem[]) => {
        setTabs(newTabs);
        const updateData = {
            style: {
                tabs: newTabs
            }
        };

        controller.update(updateData);
    };

    const handleActiveTabValueChange = (value: any) => {
        setActiveTabValue(value);
        const updateData = {
            style: {
                activeTabValue: value
            }
        };
        controller.update(updateData);
    };

    const addTab = () => {
        const newTab: TabConfigItem = {
            id: IdGenerate.generateId(),
            name: `Tab ${tabs.length + 1}`,
            value: `tab_${tabs.length + 1}`,
            childTabId: '', // 将在Controller中设置
        };

        const newTabs = [...tabs, newTab];
        handleTabsChange(newTabs);
    };

    const deleteTab = (tabId: string) => {
        const deletedTab = tabs.find(tab => tab.id === tabId);
        const newTabs = tabs.filter(tab => tab.id !== tabId);

        handleTabsChange(newTabs);

        // 如果删除的是当前激活的Tab，清空激活值
        if (deletedTab && deletedTab.value === activeTabValue) {
            handleActiveTabValueChange(null);
        }
    };

    const updateTab = (tabId: string, field: keyof TabConfigItem, value: any) => {
        const newTabs = tabs.map(tab => 
            tab.id === tabId ? { ...tab, [field]: value } : tab
        );
        handleTabsChange(newTabs);
    };

    const activeTabOptions = tabs.map(tab => ({
        label: tab.name,
        value: tab.value,
    }));

    return (
        <div style={{ padding: 16 }}>
            <div style={{ marginBottom: 16 }}>
                <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    marginBottom: 12 
                }}>
                    <span style={{ fontWeight: 'bold', color: '#fff' }}>Tab列表管理</span>
                    <Button
                        type="primary"
                        size="small"
                        icon={<Plus />}
                        onClick={addTab}
                    >
                        新增Tab
                    </Button>
                </div>
                
                <List
                    size="small"
                    dataSource={tabs}
                    renderItem={(tab) => (
                        <List.Item
                            style={{ 
                                background: '#1f1f1f', 
                                marginBottom: 8, 
                                padding: 12,
                                borderRadius: 4,
                                border: '1px solid #333'
                            }}
                        >
                            <div style={{ width: '100%' }}>
                                <Space direction="vertical" style={{ width: '100%' }}>
                                    <div style={{ display: 'flex', gap: 8 }}>
                                        <Input
                                            placeholder="显示名称"
                                            value={tab.name}
                                            onChange={(e) => updateTab(tab.id, 'name', e.target.value)}
                                            style={{ flex: 1 }}
                                        />
                                        <Popconfirm
                                            title="确定删除这个Tab吗？"
                                            onConfirm={() => deleteTab(tab.id)}
                                            okText="确定"
                                            cancelText="取消"
                                        >
                                            <Button
                                                type="text"
                                                danger
                                                icon={<Delete />}
                                                size="small"
                                            />
                                        </Popconfirm>
                                    </div>
                                    <Input
                                        placeholder="值（用于蓝图交互）"
                                        value={tab.value}
                                        onChange={(e) => updateTab(tab.id, 'value', e.target.value)}
                                    />
                                </Space>
                            </div>
                        </List.Item>
                    )}
                    locale={{ emptyText: '暂无Tab，点击"新增Tab"添加' }}
                />
            </div>

            <div>
                <div style={{ marginBottom: 8, fontWeight: 'bold', color: '#fff' }}>
                    初始激活项设置
                </div>
                <Select
                    placeholder="选择默认激活的Tab"
                    value={activeTabValue}
                    onChange={handleActiveTabValueChange}
                    options={activeTabOptions}
                    style={{ width: '100%' }}
                    allowClear
                />
            </div>
        </div>
    );
};

export default CustomTabGroupConfig;

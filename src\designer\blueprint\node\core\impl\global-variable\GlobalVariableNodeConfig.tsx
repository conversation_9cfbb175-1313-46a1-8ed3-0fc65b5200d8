/*
 * @Author: <EMAIL>
 * @Date: 2024-05-26 10:00:00
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-05-26 10:00:00
 * @Description: 全局变量蓝图节点配置组件
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React from 'react';
import {Button, Typography} from 'antd';
import globalVariableManager from '../../../../../manager/GlobalVariableManager.ts';
import designerLeftStore from '../../../../../left/DesignerLeftStore.ts';

const {Text, Title} = Typography;

interface GlobalVariableNodeConfigProps {
    nodeId: string;
}

export const GlobalVariableNodeConfig: React.FC<GlobalVariableNodeConfigProps> = ({nodeId}) => {
    // 获取全局变量定义
    const variableDefinition = globalVariableManager.getVariableDefinition(nodeId);

    if (!variableDefinition) {
        return (
            <div style={{padding: '16px'}}>
                <Title level={5}>全局变量节点配置</Title>
                <Text type="danger">未找到对应的全局变量定义</Text>
            </div>
        );
    }

    const handleEditVariable = () => {
        // 打开全局变量编辑面板
        globalVariableManager.setEditingDefinition({...variableDefinition});
        globalVariableManager.setPanelVisible(true);
        // 切换到全局变量面板
        designerLeftStore.setActiveMenu('global-variables');
    };

    return (
        <div style={{padding: '16px'}}>
            <Title level={5}>全局变量节点配置</Title>
            
            <div style={{marginBottom: '12px'}}>
                <Text strong>变量名称：</Text>
                <Text>{variableDefinition.name}</Text>
            </div>
            
            <div style={{marginBottom: '12px'}}>
                <Text strong>数据来源：</Text>
                <Text>{
                    variableDefinition.sourceType === 'static' ? '静态数据' :
                    variableDefinition.sourceType === 'api' ? 'API接口' :
                    variableDefinition.sourceType === 'database' ? '数据库' : '未知'
                }</Text>
            </div>
            
            <div style={{marginBottom: '12px'}}>
                <Text strong>主动渲染：</Text>
                <Text>{variableDefinition.isActiveRendering ? '开启' : '关闭'}</Text>
            </div>
            
            {variableDefinition.description && (
                <div style={{marginBottom: '12px'}}>
                    <Text strong>描述：</Text>
                    <Text>{variableDefinition.description}</Text>
                </div>
            )}
            
            <div style={{marginTop: '16px'}}>
                <Button type="primary" onClick={handleEditVariable}>
                    编辑全局变量
                </Button>
            </div>
            
            <div style={{marginTop: '16px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '4px'}}>
                <Title level={5}>锚点说明</Title>
                <div style={{marginBottom: '8px'}}>
                    <Text strong>输入锚点：</Text>
                </div>
                <div style={{marginLeft: '16px', marginBottom: '8px'}}>
                    <Text>• <Text code>设置值</Text>：直接传入新的数据值来更新全局变量</Text>
                    <div style={{marginLeft: '16px', marginTop: '2px'}}>
                        <Text type="secondary" style={{fontSize: '11px'}}>
                            简单直接，不需要执行流控制，有数据传入就立即更新
                        </Text>
                    </div>
                </div>

                <div style={{marginBottom: '8px'}}>
                    <Text strong>输出锚点：</Text>
                </div>
                <div style={{marginLeft: '16px', marginBottom: '4px'}}>
                    <Text>• <Text code>值变化时</Text>：当变量值发生变化时自动触发</Text>
                    <div style={{marginLeft: '16px', marginTop: '2px'}}>
                        <Text type="secondary" style={{fontSize: '11px'}}>
                            输出变化信息：{`{variableId, newValue, timestamp}`}
                        </Text>
                    </div>
                </div>
                <div style={{marginLeft: '16px', marginBottom: '8px'}}>
                    <Text>• <Text code>当前值</Text>：按需获取变量的当前值</Text>
                    <div style={{marginLeft: '16px', marginTop: '2px'}}>
                        <Text type="secondary" style={{fontSize: '11px'}}>
                            输出变量的实际值，可随时获取最新值
                        </Text>
                    </div>
                </div>

                <div style={{marginTop: '8px', padding: '8px', backgroundColor: '#e6f7ff', borderRadius: '4px'}}>
                    <Text type="secondary" style={{fontSize: '12px'}}>
                        💡 <Text strong>设计理念：</Text><br/>
                        • 全局变量节点专注于数据存取，不需要复杂的执行流控制<br/>
                        • "设置值"：简单直接的数据输入<br/>
                        • "值变化时"：响应变化事件<br/>
                        • "当前值"：获取数据内容
                    </Text>
                </div>
            </div>
        </div>
    );
};

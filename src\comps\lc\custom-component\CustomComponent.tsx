/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React, { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { ComponentBaseProps } from "../../common-component/CommonTypes";
import './CustomComponent.less';
import globalVariableManager from '../../../designer/manager/GlobalVariableManager';
import CustomComponentTransformer from './CustomComponentTransformer';
import { DesignerMode, ThemeItemType } from '../../../designer/DesignerType';

// 自定义组件Style接口
export interface CustomComponentStyle {
    background?: string;
    borderRadius?: number;
    padding?: number;
    [key: string]: any;
}

// 自定义代码配置接口
export interface CustomCodeConfig {
    code: string;       // 自定义代码内容
    isPreview: boolean; // 是否预览
    previewTimestamp?: number; // 预览时间戳，用于强制重新渲染
}

// 自定义组件Props接口
export interface CustomComponentProps extends ComponentBaseProps {
    style?: CustomComponentStyle;
    customCode?: CustomCodeConfig;
    theme?: ThemeItemType; // 添加主题支持
}

// 自定义组件Ref接口
export interface CustomComponentRef {
    updateConfig: (config: CustomComponentProps) => void;
    setEventHandler: (eventHandler: { [key: string]: () => void }) => void;
}

/**
 * 自定义组件
 * 允许用户通过编写JSX代码创建组件，支持命名空间隔离使用Antd组件
 */
const CustomComponent = forwardRef((props: CustomComponentProps, ref: ForwardedRef<CustomComponentRef>) => {
    // 检查是否为预览模式
    const isViewMode = () => {
        return window.LC_ENV?.mode === DesignerMode.VIEW;
    };

    // 在预览模式下自动设置为预览状态
    const getInitialConfig = () => {
        const initialConfig = { ...props };
        if (isViewMode() && initialConfig.customCode) {
            initialConfig.customCode.isPreview = true;
        }
        return initialConfig;
    };

    const [config, setConfig] = useState<CustomComponentProps>(getInitialConfig());
    const [renderedContent, setRenderedContent] = useState<React.ReactNode>(null);
    const [error, setError] = useState<string | null>(null);
    const [eventHandler, setEventHandlerState] = useState<{ [key: string]: () => void }>({});

    // 尝试执行用户自定义代码
    const executeCustomCode = () => {
        const { customCode, data, theme } = config;
        if (!customCode || !customCode.code) {
            setError("未提供自定义代码");
            return;
        }

        try {
            // 解析静态数据
            let parsedData;
            if (typeof data?.staticData === 'string') {
                try {
                    parsedData = JSON.parse(data.staticData);
                } catch (e) {
                    parsedData = data?.staticData;
                }
            } else {
                parsedData = data?.staticData;
            }

            // 使用CustomComponentTransformer执行代码
            const globalVarsApi = {
                get: (name: string) => globalVariableManager.getVariableValueByName(name)
            };

            const result = CustomComponentTransformer.executeComponentCode(
                customCode.code,
                parsedData,
                globalVarsApi,
                theme // 传递主题信息
            );

            // 更新渲染内容
            setRenderedContent(result);
            setError(null);
        } catch (err) {
            console.error("自定义代码执行错误:", err);
            setError(`执行错误: ${err instanceof Error ? err.message : String(err)}`);
            setRenderedContent(null);
        }
    };

    // 当预览状态切换或数据变化时重新执行代码
    useEffect(() => {
        if (config.customCode?.isPreview) {
            // 进入预览状态时，总是执行代码
            executeCustomCode();
        } else {
            setRenderedContent(null);
            setError(null);
        }
    }, [
        config.customCode?.isPreview,
        config.customCode?.previewTimestamp,
        config.data?.staticData // 监听数据变化
    ]); // 监听预览状态、时间戳和数据变化

    // 更新配置
    useImperativeHandle(ref, () => ({
        updateConfig: (newConfig: CustomComponentProps) => {
            // 在预览模式下自动设置为预览状态
            if (isViewMode() && newConfig.customCode) {
                newConfig.customCode.isPreview = true;
            }
            setConfig({ ...newConfig });
        },
        setEventHandler: (handler: { [key: string]: () => void }) => {
            setEventHandlerState(handler);
        }
    }));

    // 点击处理
    const handleClick = () => {
        if (eventHandler.click) {
            eventHandler.click();
        }
    };

    // 组件样式
    const componentStyle: React.CSSProperties = {
        width: '100%',
        height: '100%',
        overflow: 'auto',
        position: 'relative',
    };

    return (
        <div className="custom-component" style={componentStyle} onClick={handleClick}>
            {/* 当不预览或执行出错时显示占位内容 */}
            {(!config.customCode?.isPreview || error) && (
                <div className="custom-component-placeholder">
                    <div className="custom-component-title">自定义组件</div>
                    {error && <div className="custom-component-error">{error}</div>}
                    {!config.customCode?.isPreview && !error && (
                        <div className="custom-component-hint">点击预览按钮查看渲染结果</div>
                    )}
                </div>
            )}

            {/* 预览状态且无错误时显示渲染内容 */}
            {config.customCode?.isPreview && !error && (
                <div className="custom-component-content" style={{ width: '100%', height: '100%' }}>
                    {renderedContent}
                </div>
            )}
        </div>
    );
});

export default CustomComponent; 
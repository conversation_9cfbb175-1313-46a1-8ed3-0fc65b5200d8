# 全局变量过滤器中使用 ${GV::variableName} 格式的示例

## 概述

现在您可以在全局变量的过滤器中使用两种方式来引用其他全局变量：

1. **${GV::variableName}** 格式 - 新增支持
2. **globalVars.get('variableName')** 格式 - 原有支持

## 使用示例

### 示例1：基本引用

假设您有两个全局变量：
- `baseUrl`: 值为 `"https://api.example.com"`
- `apiPath`: 值为 `"/users"`

在 `apiPath` 的过滤器中，您可以这样使用：

```javascript
// 方式1：使用 ${GV::baseUrl} 格式
return "${GV::baseUrl}" + data;

// 方式2：使用 globalVars.get() 格式
return globalVars.get('baseUrl') + data;

// 两种方式的结果都是：https://api.example.com/users
```

### 示例2：数据处理和计算

假设您有以下全局变量：
- `multiplier`: 值为 `2`
- `offset`: 值为 `10`
- `rawData`: 值为 `[1, 2, 3, 4, 5]`

在 `processedData` 的过滤器中：

```javascript
// 使用 ${GV::multiplier} 和 ${GV::offset} 处理数据
const multiplier = ${GV::multiplier};
const offset = ${GV::offset};

return data.map(item => item * multiplier + offset);
// 结果：[12, 14, 16, 18, 20]
```

### 示例3：条件逻辑

假设您有以下全局变量：
- `userRole`: 值为 `"admin"`
- `userData`: 包含用户数据的对象

在数据过滤器中根据用户角色过滤数据：

```javascript
const userRole = "${GV::userRole}";

if (userRole === "admin") {
    // 管理员可以看到所有数据
    return data;
} else if (userRole === "user") {
    // 普通用户只能看到部分数据
    return data.filter(item => item.public === true);
} else {
    // 未登录用户看不到任何数据
    return [];
}
```

### 示例4：字符串模板

假设您有以下全局变量：
- `userName`: 值为 `"张三"`
- `department`: 值为 `"技术部"`

在消息模板的过滤器中：

```javascript
const userName = "${GV::userName}";
const department = "${GV::department}";

return `欢迎 ${userName} 来自 ${department}！今天的数据是：${JSON.stringify(data)}`;
```

### 示例5：复杂数据转换

假设您有以下全局变量：
- `currency`: 值为 `"CNY"`
- `exchangeRate`: 值为 `6.8`
- `salesData`: 包含销售数据

在价格转换的过滤器中：

```javascript
const currency = "${GV::currency}";
const exchangeRate = ${GV::exchangeRate};

return data.map(item => ({
    ...item,
    price: currency === "USD" ? item.price : item.price * exchangeRate,
    currency: currency
}));
```

## 注意事项

### 1. 数据类型处理

- **字符串变量**：`"${GV::variableName}"` - 需要加引号
- **数字变量**：`${GV::variableName}` - 不需要加引号
- **对象/数组变量**：通过 `globalVars.get('variableName')` 获取更安全

### 2. 错误处理

如果引用的全局变量不存在或值为 undefined，`${GV::variableName}` 会被替换为 `undefined`：

```javascript
// 安全的引用方式
const value = globalVars.get('mayNotExist') || 'defaultValue';

// 或者使用条件检查
const refValue = "${GV::mayNotExist}";
if (refValue !== "undefined") {
    // 使用 refValue
}
```

### 3. 循环引用检测

系统会自动检测和防止循环引用：
- 变量A引用变量B
- 变量B引用变量A

这种情况下会抛出错误，请避免创建循环引用。

### 4. 性能考虑

- `${GV::variableName}` 格式在过滤器执行前进行文本替换
- `globalVars.get('variableName')` 在运行时动态获取
- 对于频繁执行的过滤器，建议使用 `${GV::variableName}` 格式以获得更好的性能

## 调试技巧

### 1. 使用调试器

使用全局变量调试器来测试过滤器：
1. 选择要调试的全局变量
2. 查看当前值和引用计数
3. 修改值并观察过滤器执行结果

### 2. 控制台输出

在过滤器中添加调试输出：

```javascript
const refValue = "${GV::debugVar}";
console.log('引用的变量值:', refValue);
console.log('原始数据:', data);

// 处理逻辑...
const result = processData(data, refValue);

console.log('处理结果:', result);
return result;
```

### 3. 错误捕获

```javascript
try {
    const refValue = ${GV::someVar};
    return processData(data, refValue);
} catch (error) {
    console.error('过滤器执行错误:', error);
    return data; // 返回原始数据
}
```

## 最佳实践

1. **明确数据类型**：在使用 `${GV::variableName}` 时，确保了解引用变量的数据类型
2. **避免循环引用**：设计全局变量依赖关系时避免循环
3. **错误处理**：总是考虑引用的变量可能不存在的情况
4. **性能优化**：对于复杂的引用关系，考虑使用缓存
5. **文档记录**：为复杂的过滤器逻辑添加注释说明

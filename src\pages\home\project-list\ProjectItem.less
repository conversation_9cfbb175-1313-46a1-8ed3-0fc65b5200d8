/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.project-list-item {
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 8px;
  color: #9a9a9a;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .project-item-cover {
    margin: 10px 10px 0;
    aspect-ratio: 16/9;
    position: relative;
    background-size: 100% 100%;
    overflow: hidden;
    border-radius: 3px;


    .operate-icon-list {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      opacity: 0;

      .operate-icon {
        font-size: 16px;
        padding: 10px;
        color: #c0c0c0;
      }

      .operate-icon:hover {
        color: #2079ff;
        cursor: pointer;
      }
    }

    .operate-icon-list:hover {
      background: rgba(0, 0, 0, 0.53);
      opacity: 1;
    }

  }

  .project-item-content {
    display: flex;
    align-items: center;
    transition: all 0.3s;
    padding: 0 10px;

    .project-name {

      .project-rename-input {
        padding: 3px 0;
      }

      .project-name-content {
        padding: 10px 0;
        font-size: 16px;
      }
    }

    .rename-icon {
      opacity: 0;
      cursor: pointer;
      position: relative;
      top: 1px;
    }

    .rename-icon:hover {
      color: #2079ff;
    }


  }

  .project-item-content:hover {

    .rename-icon {
      opacity: 1;
    }
  }


}

.project-list-item:hover {
  box-shadow: 0 0 20px rgba(38, 69, 121, 0.68);
  border: 1px solid rgba(20, 119, 178, 0.65);
  transform: translateY(-7px);
}
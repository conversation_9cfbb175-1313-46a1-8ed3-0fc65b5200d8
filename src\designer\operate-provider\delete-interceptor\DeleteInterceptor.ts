/*
 * @Author: <EMAIL>
 * @Date: 2024-01-01 00:00:00
 * @LastEditTime: 2024-01-01 00:00:00
 * @LastEditors: <EMAIL>
 * @Description: 删除拦截器，用于在删除操作前处理特殊逻辑
 * @FilePath: \light-chaser\src\designer\operate-provider\delete-interceptor\DeleteInterceptor.ts
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

/**
 * 删除拦截器接口
 */
export interface IDeleteInterceptor {
    /**
     * 组件类型
     */
    componentType: string;
    
    /**
     * 拦截删除操作，返回需要额外删除的组件ID列表
     * @param componentId 要删除的组件ID
     * @param targetIds 当前删除列表
     * @returns 需要额外删除的组件ID列表
     */
    interceptDelete(componentId: string, targetIds: string[]): string[];
}

/**
 * 删除拦截器管理器
 */
class DeleteInterceptorManager {
    private interceptors: Map<string, IDeleteInterceptor> = new Map();

    /**
     * 注册删除拦截器
     */
    register(interceptor: IDeleteInterceptor): void {
        this.interceptors.set(interceptor.componentType, interceptor);
    }

    /**
     * 注销删除拦截器
     */
    unregister(componentType: string): void {
        this.interceptors.delete(componentType);
    }

    /**
     * 处理删除拦截
     * @param targetIds 当前删除列表
     * @returns 包含额外删除项的新删除列表
     */
    processDeleteIntercept(targetIds: string[]): string[] {
        const additionalTargetIds: string[] = [];
        const {layerConfigs} = layerManager;

        targetIds.forEach(id => {
            const layer = layerConfigs[id];
            if (layer && this.interceptors.has(layer.type)) {
                const interceptor = this.interceptors.get(layer.type)!;
                const extraIds = interceptor.interceptDelete(id, targetIds);
                
                // 过滤掉已经在删除列表中的ID
                const newIds = extraIds.filter(extraId => 
                    !targetIds.includes(extraId) && !additionalTargetIds.includes(extraId)
                );
                
                if (newIds.length > 0) {
                    additionalTargetIds.push(...newIds);
                }
            }
        });

        return additionalTargetIds.length > 0 ? [...targetIds, ...additionalTargetIds] : targetIds;
    }
}

// 导入layerManager（避免循环依赖）
import layerManager from "../../manager/LayerManager";

const deleteInterceptorManager = new DeleteInterceptorManager();
export default deleteInterceptorManager;

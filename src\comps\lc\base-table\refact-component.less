/* The original styles are mostly fine. The key is to remove any react-slick specific overrides if they existed.
   The provided LESS file is already well-structured for this refactoring. No significant changes are needed,
   as the layout is based on CSS Grid, and the new SeamlessCarousel works well with it.
   The class names `base-table-container`, `base-table-header-div`, `base-table-body-div`, etc., are preserved.
*/

@tdRowHeight: 0px;

.base-table-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  
  /* 表头区域 */
  .base-table-header-div {
    width: 100%;
    z-index: 2;
    background-color: inherit;
    flex-shrink: 0; /* Prevent header from shrinking */
    
    .base-table-row-div {
      width: 100%;
      display: grid;
      height: 100%;
    }
    
    .base-table-cell-div {
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin: 0;
      padding: 0 8px;
      display: flex;
      align-items: center;
      height: 100%;
    }
  }
  
  /* 表体区域 */
  .base-table-body-div {
    flex: 1;
    width: 100%;
    overflow: hidden; /* Important for carousel and static view */
    position: relative;
    
    .base-table-body-content {
        // This container is for static view only
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .base-table-row-div {
      width: 100%;
      display: grid;
      box-sizing: border-box;
    }
    
    .base-table-cell-div {
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin: 0;
      padding: 0 8px;
      display: flex;
      align-items: center;
      height: 100%;

      /* 自定义模式下不应用默认对齐，让内容自己控制 */
      &.custom-mode {
        justify-content: unset !important;

        > * {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  /* Shared text alignment classes */
  .text-align-left {
    justify-content: flex-start;
  }
  
  .text-align-center {
    justify-content: center;
  }
  
  .text-align-right {
    justify-content: flex-end;
  }
}
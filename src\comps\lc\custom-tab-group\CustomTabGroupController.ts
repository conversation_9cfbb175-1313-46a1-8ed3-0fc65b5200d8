/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import AbstractDesignerController from "../../../framework/core/AbstractDesignerController";
import {UpdateOptions} from "../../../framework/core/AbstractController";
import ComponentUtil from "../../../utils/ComponentUtil";
import CustomTabGroupComponent, {CustomTabGroupComponentRef} from "./CustomTabGroupComponent";
import {CustomTabGroupComponentProps, TabConfigItem} from "./CustomTabGroupTypes";
import ObjectUtil from "../../../utils/ObjectUtil";
import layerManager from "../../../designer/manager/LayerManager";
import historyRecordOperateProxy from "../../../designer/operate-provider/undo-redo/HistoryRecordOperateProxy";
import eventOperateStore from "../../../designer/operate-provider/EventOperateStore";
import IdGenerate from "../../../utils/IdGenerate";
import {ILayerItem} from "../../../designer/DesignerType";
import BPExecutor from "../../../designer/blueprint/core/BPExecutor";
import {runInAction} from 'mobx';
import deleteInterceptorManager, {IDeleteInterceptor} from "../../../designer/operate-provider/delete-interceptor/DeleteInterceptor";

export class CustomTabGroupController extends AbstractDesignerController<CustomTabGroupComponentRef, CustomTabGroupComponentProps> {

    async create(container: HTMLElement, config: CustomTabGroupComponentProps): Promise<void> {
        this.config = config;
        this.container = container;
        this.instance = await ComponentUtil.createAndRender<CustomTabGroupComponentRef>(container, CustomTabGroupComponent, config);

        // 注册删除拦截器
        this.registerDeleteInterceptor();
    }

    /**
     * 注册删除拦截器
     */
    private registerDeleteInterceptor(): void {
        const interceptor: IDeleteInterceptor = {
            componentType: 'CustomTabGroup',
            interceptDelete: (componentId: string, targetIds: string[]): string[] => {
                const {compController} = layerManager;
                const controller = compController[componentId];

                if (controller && controller.config?.style?.tabs) {
                    const childTabIds = controller.config.style.tabs
                        .map((tab: any) => tab.childTabId)
                        .filter((childId: string) => childId && childId !== '' && !targetIds.includes(childId));

                    if (childTabIds.length > 0) {
                        return childTabIds;
                    }
                }

                return [];
            }
        };

        deleteInterceptorManager.register(interceptor);
    }

    destroy(): void {
        // 注销删除拦截器
        deleteInterceptorManager.unregister('CustomTabGroup');

        // 子Tab的删除现在由删除拦截器在删除流程开始前处理
        // 这里只需要清理自己的资源

        this.instance = null;
        this.config = null;
        this.container = null;
    }

    getConfig(): CustomTabGroupComponentProps | null {
        return this.config;
    }

    update(config: CustomTabGroupComponentProps, upOp?: UpdateOptions): void {
        const oldConfig = this.config;

        // 先保存旧的tabs数组用于比较
        const oldTabs = oldConfig?.style?.tabs || [];

        // 合并配置
        this.config = ObjectUtil.merge(this.config, config);

        // 获取新的tabs数组
        const newTabs = this.config.style?.tabs || [];

        // 核心逻辑：对比新旧tabs数组，动态增删ChildTab组件
        this.syncChildTabs(oldTabs, newTabs);

        upOp = upOp || {reRender: true};
        // CustomTabGroup是占位符组件，不需要复杂的更新逻辑
        // Tab数量的变化会通过子Tab组件的创建/删除来体现
    }

    /**
     * 同步子Tab组件
     */
    private syncChildTabs(oldTabs: TabConfigItem[], newTabs: TabConfigItem[]): void {
        // 找出需要删除的Tab
        const tabsToDelete = oldTabs.filter(oldTab =>
            !newTabs.find(newTab => newTab.id === oldTab.id)
        );

        // 找出需要添加的Tab
        const tabsToAdd = newTabs.filter(newTab =>
            !oldTabs.find(oldTab => oldTab.id === newTab.id)
        );

        // 删除不再需要的子Tab组件
        tabsToDelete.forEach(tab => {
            if (tab.childTabId) {
                this.deleteChildTab(tab.childTabId);
            }
        });

        // 添加新的子Tab组件
        tabsToAdd.forEach(tab => {
            this.createChildTab(tab);
        });

        // 更新所有子Tab的配置（包括名称、值、激活状态）
        this.updateAllChildTabsConfig();
    }

    /**
     * 创建子Tab组件
     */
    private createChildTab(tabConfig: TabConfigItem): void {
        const childTabId = IdGenerate.generateId();

        // 计算子Tab的位置，避免重叠
        const existingTabs = this.config?.style?.tabs || [];
        const tabIndex = existingTabs.findIndex(tab => tab.id === tabConfig.id);
        const xOffset = tabIndex * 130; // 每个Tab间隔130px

        // 创建子Tab的图层配置
        const childTabLayer: ILayerItem = {
            name: tabConfig.name,
            type: 'ChildTab',
            x: 100 + xOffset, // 根据索引计算位置
            y: 100,
            id: childTabId,
            lock: false,
            hide: false,
            width: 120,
            height: 40,
            // 暂时不设置pid，避免复杂的父子关系处理
        };

        // 更新tabConfig中的childTabId
        tabConfig.childTabId = childTabId;

        // 设置子Tab的初始配置
        const {elemConfigs} = layerManager;

        if (elemConfigs) {
            const childConfig = {
                base: {
                    id: childTabId,
                    name: tabConfig.name,
                    type: 'ChildTab',
                },
                style: {
                    parentId: this.config!.base.id,
                    value: tabConfig.value,
                    isActive: tabConfig.value === this.config!.style.activeTabValue,
                    activeStyle: {
                        background: '#1890ff',
                        color: '#ffffff',
                        borderColor: '#1890ff',
                        borderWidth: 1,
                        borderRadius: 4,
                        fontSize: 14,
                        fontWeight: 'normal',
                        padding: '8px 16px',
                        textAlign: 'center' as const,
                    },
                    inactiveStyle: {
                        background: '#f5f5f5',
                        color: '#666666',
                        borderColor: '#d9d9d9',
                        borderWidth: 1,
                        borderRadius: 4,
                        fontSize: 14,
                        fontWeight: 'normal',
                        padding: '8px 16px',
                        textAlign: 'center' as const,
                    }
                }
            };

            elemConfigs[childTabId] = childConfig;
        }

        // 使用historyRecordOperateProxy正确添加组件，但不记录历史
        const {setAddRecordCompId} = eventOperateStore;
        const originalAddRecordCompId = eventOperateStore.addRecordCompId;

        // 临时设置为null，避免记录历史
        setAddRecordCompId(null);

        // 使用正确的添加方法
        historyRecordOperateProxy.doAdd(childTabLayer);

        // 恢复原始状态
        setAddRecordCompId(originalAddRecordCompId);
    }



    /**
     * 删除子Tab组件 - 使用正确的图层链表删除逻辑
     */
    private deleteChildTab(childTabId: string): void {
        const {layerConfigs, elemConfigs, compController} = layerManager;

        if (!layerConfigs[childTabId]) {
            return; // 图层不存在，直接返回
        }

        // 先标记这个子Tab正在被父组件删除，避免重复调用removeChildTab
        const childController = compController[childTabId];
        if (childController) {
            (childController as any)._isBeingDeletedByParent = true;
        }

        // 删除配置
        if (elemConfigs && elemConfigs[childTabId]) {
            delete elemConfigs[childTabId];
        }

        // 使用正确的图层链表删除逻辑（参考HistoryRecordOperateProxy.doDelete）
        runInAction(() => {
            const layer = layerConfigs[childTabId];
            const prevLayer = layerConfigs[layer.prev!];
            const nextLayer = layerConfigs[layer.next!];

            // 更新头尾指针
            if (layer.id === layerManager.layerHeader) {
                layerManager.layerHeader = layer.next;
            }
            if (layer.id === layerManager.layerTail) {
                layerManager.layerTail = layer.prev;
            }

            // 更新前后节点的指针
            if (prevLayer) {
                prevLayer.next = layer.next;
            }
            if (nextLayer) {
                nextLayer.prev = layer.prev;
            }

            // 处理父子关系
            if (layer.pid) {
                const parentLayer = layerConfigs[layer.pid];
                if (parentLayer) {
                    if (parentLayer.childHeader === childTabId) {
                        parentLayer.childHeader = layer.next;
                    }
                    if (parentLayer.childTail === childTabId) {
                        parentLayer.childTail = layer.prev;
                    }
                }
            }

            // 最后删除图层和控制器
            layerManager.delItem([childTabId]);
        });
    }

    /**
     * 更新所有子Tab的配置（包括名称、值、激活状态）
     */
    private updateAllChildTabsConfig(): void {
        if (!this.config?.style?.tabs) return;

        this.config.style.tabs.forEach(tab => {
            if (tab.childTabId) {
                const {compController, layerConfigs} = layerManager;
                const childController = compController[tab.childTabId];
                if (childController) {
                    const childConfig = childController.getConfig();
                    if (childConfig) {
                        // 更新基础信息
                        childConfig.base.name = tab.name;
                        childConfig.style.value = tab.value;
                        childConfig.style.isActive = tab.value === this.config!.style.activeTabValue;

                        // 同步更新图层配置中的名称
                        if (layerConfigs[tab.childTabId]) {
                            layerConfigs[tab.childTabId].name = tab.name;
                        }

                        childController.update(childConfig);
                    }
                }
            }
        });
    }

    /**
     * 更新所有子Tab的激活状态（保留此方法用于单独的激活状态更新）
     */
    private updateChildTabsActiveState(): void {
        this.updateAllChildTabsConfig();
    }

    /**
     * 设置激活的Tab
     */
    public setActiveTab(value: any): void {
        if (this.config) {
            this.config.style.activeTabValue = value;
            this.updateChildTabsActiveState();
        }
    }

    /**
     * 处理子Tab点击事件
     */
    public handleChildClick(childValue: any): void {
        this.setActiveTab(childValue);
        // 触发蓝图输出锚点
        BPExecutor.triggerComponentEvent(this.config!.base.id, 'onValueChanged', childValue);
    }

    /**
     * 蓝图执行器，处理输入锚点
     */
    public execute(actionId: string, params: any): void {
        if (actionId === 'setActiveTab') {
            this.setActiveTab(params);
            // 触发蓝图输出锚点，保持与点击交互的一致性
            if (this.config?.base?.id) {
                BPExecutor.triggerComponentEvent(this.config.base.id, 'onValueChanged', params);
            }
        }
    }

    /**
     * 移除子Tab（当子Tab在画布中被直接删除时调用）
     */
    public removeChildTab(childTabId: string): void {
        if (!this.config?.style?.tabs) {
            return;
        }


        // 找到对应的Tab配置并移除
        const updatedTabs = this.config.style.tabs.filter(tab => tab.childTabId !== childTabId);

        if (updatedTabs.length !== this.config.style.tabs.length) {
            // 更新配置
            this.config.style.tabs = updatedTabs;

            // 如果删除的是当前激活的Tab，清空激活值
            const deletedTab = this.config.style.tabs.find(tab => tab.childTabId === childTabId);
            if (deletedTab && deletedTab.value === this.config.style.activeTabValue) {
                this.config.style.activeTabValue = null;
            }

        }
    }
}

/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React from "react";
import {BaseInfoType, MenuToConfigMappingType} from "../../../framework/core/AbstractDefinition";
import {MenuInfo} from "../../../designer/right/MenuType";
import {ClazzTemplate} from "../../common-component/CommonTypes.ts";
import pieImg from './g2-plot-custom.png';
import G2PlotCustomController, {G2PlotCustomProps} from "./G2PlotCustomController.ts";
import {AntdCommonDefinition} from "../../antd-common/AntdCommonDefinition";
import {Data, Optimize, SettingOne} from "@icon-park/react";

const G2PlotCustomConfig = React.lazy(() => import("./G2PlotCustomConfig.tsx"));
const BaseInfo = React.lazy(() => import("../../common-component/base-info/BaseInfo"));
const DataConfig = React.lazy(() => import("../../common-component/data-config/DataConfig"));

class G2PlotCustomDefinition extends AntdCommonDefinition<G2PlotCustomController, G2PlotCustomProps> {

    getController(): ClazzTemplate<G2PlotCustomController> | null {
        return G2PlotCustomController;
    }

    getMenuList(): Array<MenuInfo> {
        return [
            {
                icon: SettingOne,
                name: '基础',
                key: 'base',
            },
            {
                icon: Optimize,
                name: '自定义',
                key: 'custom',
            },
            {
                icon: Data,
                name: '数据',
                key: 'data',
            }
        ];
    }

    getMenuToConfigContentMap(): MenuToConfigMappingType {
        return {
            base: BaseInfo,
            data: DataConfig,
            custom: G2PlotCustomConfig,
        };
    }

    getBaseInfo(): BaseInfoType {
        return {
            compName: "G2Plot自定义图表",
            compKey: "G2PlotCustom",
            categorize: "other",
        };
    }

    getChartImg(): string | null {
        return pieImg;
    }

    getInitConfig(): G2PlotCustomProps {

        return {
            base: {
                id: "",
                name: 'G2Plot自定义图表',
                type: 'G2PlotCustom',
            },
            style: {
                customCode: "function renderG2Plot(container, G2Plot, data, globalVars, dynamicStyle) {\n" +
                    "    // G2Plot自定义图表示例 - 支持全局变量和动态样式\n" +
                    "    // 参数说明:\n" +
                    "    // - container: 图表容器\n" +
                    "    // - G2Plot: G2Plot库对象\n" +
                    "    // - data: 静态数据或组件数据\n" +
                    "    // - globalVars: 全局变量访问对象\n" +
                    "    // - dynamicStyle: 蓝图传入的动态样式配置\n" +
                    "    \n" +
                    "    // 全局变量使用示例:\n" +
                    "    // 方式1: 使用globalVars.get()方法\n" +
                    "    // const threshold = globalVars.get('threshold') || 50;\n" +
                    "    // const chartTitle = globalVars.get('chartTitle') || '默认标题';\n" +
                    "    // \n" +
                    "    // 方式2: 使用${GV::variableName}模板语法(在字符串中)\n" +
                    "    // 注意: 模板语法会在代码执行前被替换\n" +
                    "    \n" +
                    "    // 动态样式使用示例:\n" +
                    "    // 蓝图可以通过'更新组件样式'传入样式配置\n" +
                    "    // 例如: {style: {xAxis: {label: {style: {fill: '#red'}}}}} \n" +
                    "    \n" +
                    "    // 示例: 根据全局变量动态调整图表配置\n" +
                    "    const lineWidth = globalVars.get('lineWidth') || 4;\n" +
                    "    const fontSize = globalVars.get('fontSize') || 10;\n" +
                    "    const textColor = globalVars.get('textColor') || '#fff';\n" +
                    "    \n" +
                    "    // 合并动态样式配置\n" +
                    "    const baseConfig = {\n" +
                    "        data: data,\n" +
                    "        layout: 'vertical',\n" +
                    "        xField: 'country',\n" +
                    "        yField: ['2016年耕地总面积', '2016年转基因种植面积'],\n" +
                    "        legend: false,\n" +
                    "        appendPadding: [20, 0],\n" +
                    "        barStyle: {\n" +
                    "            lineWidth: lineWidth // 使用全局变量\n" +
                    "        },\n" +
                    "        yAxis: {\n" +
                    "            '2016年耕地总面积': {\n" +
                    "                grid: null,\n" +
                    "                label: {\n" +
                    "                    style: {\n" +
                    "                        fill: textColor, // 使用全局变量\n" +
                    "                        fontSize: fontSize, // 使用全局变量\n" +
                    "                    },\n" +
                    "                },\n" +
                    "            },\n" +
                    "            '2016年转基因种植面积': {\n" +
                    "                grid: null,\n" +
                    "                label: {\n" +
                    "                    style: {\n" +
                    "                        fill: textColor, // 使用全局变量\n" +
                    "                        fontSize: fontSize, // 使用全局变量\n" +
                    "                    },\n" +
                    "                },\n" +
                    "            },\n" +
                    "        },\n" +
                    "        xAxis: {\n" +
                    "            grid: null,\n" +
                    "            label: {\n" +
                    "                style: {\n" +
                    "                    fill: textColor, // 使用全局变量\n" +
                    "                    fontSize: fontSize, // 使用全局变量\n" +
                    "                },\n" +
                    "            },\n" +
                    "            line: {\n" +
                    "                style: {\n" +
                    "                    stroke: \"#6f6f6f91\",\n" +
                    "                    lineWidth: 1,\n" +
                    "                },\n" +
                    "            },\n" +
                    "            tickLine: null,\n" +
                    "            subTickLine: null,\n" +
                    "            title: null,\n" +
                    "        },\n" +
                    "        label: {\n" +
                    "            position: 'top',\n" +
                    "            style: {\n" +
                    "                fill: textColor // 使用全局变量\n" +
                    "            }\n" +
                    "        },\n" +
                    "    };\n" +
                    "    \n" +
                    "    // 深度合并动态样式配置\n" +
                    "    const finalConfig = dynamicStyle ? \n" +
                    "        Object.assign({}, baseConfig, dynamicStyle) : baseConfig;\n" +
                    "    \n" +
                    "    const g2plot = new G2Plot.BidirectionalBar(container, finalConfig);\n" +
                    "    g2plot.render();\n" +
                    "    return g2plot;\n" +
                    "}",
            },
            data: {
                sourceType: 'static',
                staticData: [
                    {country: '乌拉圭', '2016年耕地总面积': 13.4, '2016年转基因种植面积': 12.3},
                    {country: '巴拉圭', '2016年耕地总面积': 14.4, '2016年转基因种植面积': 6.3},
                    {country: '南非', '2016年耕地总面积': 18.4, '2016年转基因种植面积': 8.3},
                    {country: '巴基斯坦', '2016年耕地总面积': 34.4, '2016年转基因种植面积': 13.8},
                    {country: '阿根廷', '2016年耕地总面积': 44.4, '2016年转基因种植面积': 19.5},
                    {country: '巴西', '2016年耕地总面积': 24.4, '2016年转基因种植面积': 18.8},
                    {country: '加拿大', '2016年耕地总面积': 54.4, '2016年转基因种植面积': 24.7},
                    {country: '中国', '2016年耕地总面积': 104.4, '2016年转基因种植面积': 5.3},
                    {country: '美国', '2016年耕地总面积': 165.2, '2016年转基因种植面积': 72.9},
                ],
            },
        };
    }
}

export default G2PlotCustomDefinition;

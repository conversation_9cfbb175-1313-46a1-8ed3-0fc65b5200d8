/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import { useRef, useState } from 'react';
import './EnhancedColorMode.less';
import Select from "../select/Select";
import ColorsPicker from "../colors-picker/ColorsPicker";
import EnhancedColorPicker, { ColorValue } from "../enhanced-color-picker/EnhancedColorPicker";
import { UIContainer, UIContainerProps } from "../ui-container/UIContainer";
import ColorUtil from '../../../utils/ColorUtil';

export type EnhancedColorModeType = 'single' | 'multi';

export interface EnhancedColorModeValue {
  mode: EnhancedColorModeType;
  color: ColorValue | ColorValue[];
}

export interface EnhancedColorModeProps extends UIContainerProps {
  value?: ColorValue | ColorValue[];
  defaultValue?: ColorValue | ColorValue[];
  onChange?: (value: ColorValue | ColorValue[]) => void;
}

export default function EnhancedColorMode(props: EnhancedColorModeProps) {
  const { value, defaultValue, onChange, ...containerProps } = props;
  const controlled = value !== undefined && defaultValue === undefined;
  const [stateValue, setStateValue] = useState(controlled ? value : defaultValue);
  const [mode, setMode] = useState<EnhancedColorModeType>(Array.isArray(controlled ? value : defaultValue) ? 'multi' : 'single');
  const finalValue = controlled ? value : stateValue;
  const _singleValueRef = useRef<ColorValue>('#252525');
  const _multiValueRef = useRef<ColorValue[]>(['#252525']);

  const modeChange = (_mode: string) => {
    let tempValue: ColorValue | ColorValue[] = _singleValueRef.current;
    if (_mode === 'multi')
      tempValue = _multiValueRef.current;
    if (_mode === 'single')
      tempValue = _singleValueRef.current;
    onChange && onChange(tempValue);
    if (!controlled) {
      setStateValue(tempValue);
      setMode(_mode as EnhancedColorModeType);
    }
  }

  const colorChange = (value: ColorValue | ColorValue[]) => {
    // 保存当前值的引用，以便在模式切换时恢复
    if (!Array.isArray(value)) {
      _singleValueRef.current = value;
    } else {
      _multiValueRef.current = value;
    }

    onChange && onChange(value);
    if (!controlled)
      setStateValue(value);
  }

  // 不再需要单独的预览函数，因为 EnhancedColorPicker 已经内置了预览功能

  return (
    <UIContainer {...containerProps}>
      <div className="lc-enhanced-color-mode">
        <div className="mode-select" style={{ width: 80 }}>
          <Select
            value={mode}
            onChange={(mode: string) => modeChange(mode as EnhancedColorModeType)}
            options={[
              { value: 'single', label: '单色' },
              { value: 'multi', label: '多色' },
            ]}
          />
        </div>
        {
          mode === 'single' &&
          <EnhancedColorPicker
            value={finalValue as ColorValue}
            onChange={colorChange as (value: ColorValue) => void}
            showText={true}
          />
        }
        {
          mode === 'multi' &&
          <div className="multi-color-container">
            {Array.isArray(finalValue) && finalValue.map((color, index) => (
              <div key={index} className="multi-color-item">
                <EnhancedColorPicker
                  value={color}
                  onChange={(newColor) => {
                    const newColors = [...(finalValue as ColorValue[])];
                    newColors[index] = newColor;
                    colorChange(newColors);
                  }}
                />
                {finalValue.length > 1 && (
                  <button
                    className="delete-color-btn"
                    onClick={() => {
                      const newColors = [...(finalValue as ColorValue[])];
                      newColors.splice(index, 1);
                      colorChange(newColors);
                    }}
                  >
                    删除
                  </button>
                )}
              </div>
            ))}
            <button
              className="add-color-btn"
              onClick={() => {
                const newColors = [...(Array.isArray(finalValue) ? finalValue : [])];
                newColors.push('#252525');
                colorChange(newColors);
              }}
              disabled={Array.isArray(finalValue) && finalValue.length >= 5}
            >
              添加颜色
            </button>
          </div>
        }
      </div>
    </UIContainer>
  )
}

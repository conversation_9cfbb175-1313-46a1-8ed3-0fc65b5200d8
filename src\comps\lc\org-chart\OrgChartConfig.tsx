/**
 * 组织架构图组件配置面板
 */

import React from 'react';
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI";
import {Control} from "../../../json-schema/SchemaTypes";
import {OrgChartController} from "./OrgChartController";
import {ConfigType} from "../../../designer/right/ConfigContent";

export const OrgChartConfig: React.FC<ConfigType<OrgChartController>> = ({controller}) => {

    const config = controller.getConfig();

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {dataFragment} = fieldChangeData;
        controller.update(dataFragment);
    }

    const {style} = config || {};

    const schema: Control = {
        key: 'style',
        type: 'grid',
        children: [
            {
                type: 'accordion',
                label: 'ReactFlow配置',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                key: 'zoomOnScroll',
                                type: 'switch',
                                label: '滚轮缩放',
                                value: style?.zoomOnScroll,
                            },
                            {
                                key: 'panOnDrag',
                                type: 'switch',
                                label: '拖拽平移',
                                value: style?.panOnDrag,
                            },
                            {
                                key: 'nodeDraggable',
                                type: 'switch',
                                label: '节点可拖拽',
                                value: style?.nodeDraggable,
                            }
                        ]
                    }
                ]
            }
        ]
    };

    return (
        <div style={{padding: 10}}>
            <LCGUI schema={schema} onFieldChange={onFieldChange}/>
        </div>
    );
};
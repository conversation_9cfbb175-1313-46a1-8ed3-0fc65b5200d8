/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Button, Card, Select, Input, Switch, Space, Typography, Divider, message, FloatButton } from 'antd';
import globalVariableManager from '../manager/GlobalVariableManager';
import EnvironmentUtil from '../../utils/EnvironmentUtil';
import './GlobalVariableDebugger.less';
import { getDebugConfig } from '../../config';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface DebugLog {
    timestamp: string;
    type: 'info' | 'success' | 'warning' | 'error';
    message: string;
}

export const GlobalVariableDebugger = observer(() => {
    const [selectedVariableId, setSelectedVariableId] = useState<string>('');
    const [newValue, setNewValue] = useState<string>('');
    const [debugLogs, setDebugLogs] = useState<DebugLog[]>([]);
    const [isVisible, setIsVisible] = useState<boolean>(false);

    // 检查调试器是否应该显示
    const debugConfig = getDebugConfig();
    const isDebugEnabled = EnvironmentUtil.isDebugEnabled() && debugConfig.enableGlobalVariableDebugger;

    // 获取所有全局变量
    const variables = globalVariableManager.getAllVariableDefinitions();
    const variableOptions = variables.map(variable => ({
        label: `${variable.name} (${variable.sourceType})`,
        value: variable.id
    }));

    // 添加调试日志
    const addLog = (type: DebugLog['type'], message: string) => {
        const log: DebugLog = {
            timestamp: new Date().toLocaleTimeString(),
            type,
            message
        };
        setDebugLogs(prev => [log, ...prev.slice(0, 49)]); // 保留最新50条
    };

    // 获取选中变量的当前信息
    const selectedVariable = selectedVariableId ? 
        globalVariableManager.getVariableDefinition(selectedVariableId) : null;
    const currentValue = selectedVariableId ? 
        globalVariableManager.getVariableValue(selectedVariableId) : null;
    const isLoading = selectedVariableId ? 
        globalVariableManager.isLoading.get(selectedVariableId) : false;
    const errorState = selectedVariableId ? 
        globalVariableManager.errorStates.get(selectedVariableId) : null;
    const referenceCount = selectedVariableId ? 
        globalVariableManager.referenceCounts.get(selectedVariableId) || 0 : 0;

    // 监听全局变量变化
    useEffect(() => {
        if (!selectedVariableId || !selectedVariable?.isActiveRendering) return;

        const callback = (newValue: any) => {
            addLog('success', `变量 "${selectedVariable.name}" 值已更新: ${JSON.stringify(newValue)}`);
        };

        // 订阅变量变化
        globalVariableManager.subscribe(selectedVariableId, 'debugger', callback);
        addLog('info', `已订阅变量 "${selectedVariable.name}" 的变化通知`);

        return () => {
            globalVariableManager.unsubscribe(selectedVariableId, 'debugger');
            addLog('info', `已取消订阅变量 "${selectedVariable.name}" 的变化通知`);
        };
    }, [selectedVariableId, selectedVariable?.isActiveRendering, selectedVariable?.name]);

    // 手动设置变量值
    const handleSetValue = () => {
        if (!selectedVariableId || !selectedVariable) {
            message.error('请先选择一个全局变量');
            return;
        }

        try {
            let parsedValue: any;

            // 尝试解析JSON
            try {
                parsedValue = JSON.parse(newValue);
            } catch {
                // 如果不是JSON，当作字符串处理
                parsedValue = newValue;
            }

            addLog('info', `尝试设置变量 "${selectedVariable.name}" 的值为: ${JSON.stringify(parsedValue)}`);

            // 对于静态数据类型的变量，直接修改定义以确保持久化
            if (selectedVariable.sourceType === 'static') {
                const updatedDefinition = {
                    ...selectedVariable,
                    sourceConfig: {
                        ...selectedVariable.sourceConfig,
                        staticData: parsedValue
                    }
                };

                // 更新变量定义（这会触发值的重新计算和通知）
                globalVariableManager.updateDefinition(updatedDefinition);
                addLog('success', `成功更新变量 "${selectedVariable.name}" 的定义和值`);
                message.success('变量值已设置并保存到定义中');
            } else {
                // 对于API和数据库类型的变量，只设置临时值
                globalVariableManager.setVariableValue(selectedVariableId, parsedValue);
                addLog('success', `成功设置变量 "${selectedVariable.name}" 的临时值`);
                addLog('warning', `注意：API/数据库类型的变量值为临时设置，项目重新加载后会恢复原始值`);
                message.success('变量值已设置（临时）');
            }
        } catch (error) {
            const errorMsg = `设置变量值失败: ${(error as Error).message}`;
            addLog('error', errorMsg);
            message.error(errorMsg);
        }
    };

    // 刷新变量值
    const handleRefreshValue = async () => {
        if (!selectedVariableId || !selectedVariable) {
            message.error('请先选择一个全局变量');
            return;
        }

        try {
            addLog('info', `开始刷新变量 "${selectedVariable.name}" 的值...`);
            await globalVariableManager.refreshVariableValue(selectedVariableId);
            addLog('success', `变量 "${selectedVariable.name}" 值刷新完成`);
            message.success('变量值已刷新');
        } catch (error) {
            const errorMsg = `刷新变量值失败: ${(error as Error).message}`;
            addLog('error', errorMsg);
            message.error(errorMsg);
        }
    };

    // 重新计算引用计数
    const handleRecalculateReferences = () => {
        addLog('info', '开始重新计算所有变量的引用计数...');
        globalVariableManager.recalculateAllReferenceCounts();
        addLog('success', '引用计数重新计算完成');
        message.success('引用计数已重新计算');
    };

    // 调试引用计数
    const handleDebugReferences = () => {
        addLog('info', '开始调试引用计数...');

        // 打印所有全局变量定义
        globalVariableManager.debugPrintDefinitions();

        // 检查组件控制器
        const layerManager = (window as any).layerManager;
        if (layerManager && layerManager.compController) {
            const controllers = Object.values(layerManager.compController);
            addLog('info', `找到 ${controllers.length} 个组件控制器`);

            controllers.forEach((controller: any, index: number) => {
                const config = controller.getConfig();
                if (config?.data?.sourceType === 'globalVariable') {
                    addLog('info', `组件 ${index}: 使用全局变量 ${config.data.selectedGlobalVariableId}`);
                }
            });
        } else {
            addLog('warning', 'layerManager 或 compController 不存在');
        }

        addLog('success', '引用计数调试完成，请查看控制台输出');
        message.success('调试信息已输出到控制台');
    };

    // 清空日志
    const clearLogs = () => {
        setDebugLogs([]);
        addLog('info', '调试日志已清空');
    };

    return (
        <div style={{ display: isDebugEnabled ? 'block' : 'none' }}>
            {!isVisible ? (
                <FloatButton
                    className="gv-debugger-float-btn"
                    tooltip="全局变量调试器"
                    type="primary"
                    onClick={() => setIsVisible(true)}
                    style={{
                        position: 'fixed',
                        bottom: '24px',
                        right: '24px',
                        zIndex: 9999
                    }}
                />
            ) : (
                <div className="gv-debugger-panel">
            <Card 
                title={
                    <Space>
                        <span>全局变量调试器</span>
                        <Button
                            size="small"
                            onClick={() => setIsVisible(false)}
                        >
                            关闭
                        </Button>
                    </Space>
                }
                style={{ 
                    position: 'fixed', 
                    top: '10px', 
                    right: '10px', 
                    width: '500px',
                    maxHeight: '80vh',
                    overflow: 'auto',
                    zIndex: 9999,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                }}
            >
                {/* 变量选择 */}
                <div className="gv-debugger-section">
                    <Title level={5}>选择变量</Title>
                    <Select
                        style={{ width: '100%' }}
                        placeholder="选择要调试的全局变量"
                        value={selectedVariableId}
                        onChange={setSelectedVariableId}
                        options={variableOptions}
                        getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
                        dropdownStyle={{ zIndex: 10000 }}
                    />
                </div>

                {selectedVariable && (
                    <>
                        <Divider />
                        
                        {/* 变量信息 */}
                        <div className="gv-debugger-section">
                            <Title level={5}>变量信息</Title>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Text><strong>名称:</strong> {selectedVariable.name}</Text>
                                <Text><strong>数据源:</strong> {selectedVariable.sourceType}</Text>
                                <Text><strong>主动渲染:</strong> 
                                    <Switch 
                                        size="small" 
                                        checked={selectedVariable.isActiveRendering} 
                                        disabled 
                                        style={{ marginLeft: 8 }}
                                    />
                                </Text>
                                <Text><strong>引用次数:</strong> {referenceCount}</Text>
                                <Text><strong>加载状态:</strong> {isLoading ? '加载中...' : '已完成'}</Text>
                                {errorState && (
                                    <Text type="danger"><strong>错误:</strong> {errorState}</Text>
                                )}
                            </Space>
                        </div>

                        <Divider />

                        {/* 当前值显示 */}
                        <div className="gv-debugger-section">
                            <Title level={5}>当前值</Title>
                            <TextArea
                                value={JSON.stringify(currentValue, null, 2)}
                                readOnly
                                rows={4}
                                style={{ fontFamily: 'monospace' }}
                            />
                        </div>

                        <Divider />

                        {/* 设置新值 */}
                        <div className="gv-debugger-section">
                            <Title level={5}>设置新值</Title>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                {selectedVariable.sourceType === 'static' && (
                                    <div style={{
                                        padding: '8px 12px',
                                        backgroundColor: '#f6ffed',
                                        border: '1px solid #b7eb8f',
                                        borderRadius: '6px',
                                        fontSize: '12px',
                                        color: '#52c41a'
                                    }}>
                                        💾 静态数据变量：设置的值会永久保存到变量定义中
                                    </div>
                                )}
                                {selectedVariable.sourceType !== 'static' && (
                                    <div style={{
                                        padding: '8px 12px',
                                        backgroundColor: '#fff7e6',
                                        border: '1px solid #ffd591',
                                        borderRadius: '6px',
                                        fontSize: '12px',
                                        color: '#fa8c16'
                                    }}>
                                        ⚠️ {selectedVariable.sourceType === 'api' ? 'API' : '数据库'}变量：设置的值为临时值，项目重新加载后会恢复原始值
                                    </div>
                                )}
                                <TextArea
                                    placeholder="输入新的值 (支持JSON格式)"
                                    value={newValue}
                                    onChange={(e) => setNewValue(e.target.value)}
                                    rows={3}
                                />
                                <Space>
                                    <Button
                                        type="primary"
                                        onClick={handleSetValue}
                                    >
                                        {selectedVariable.sourceType === 'static' ? '设置并保存' : '设置临时值'}
                                    </Button>
                                    <Button
                                        onClick={handleRefreshValue}
                                    >
                                        刷新值
                                    </Button>
                                </Space>
                            </Space>
                        </div>
                    </>
                )}

                <Divider />

                {/* 工具按钮 */}
                <div className="gv-debugger-section">
                    <Title level={5}>调试工具</Title>
                    <Space wrap>
                        <Button onClick={handleRecalculateReferences}>
                            重新计算引用
                        </Button>
                        <Button onClick={handleDebugReferences}>
                            调试引用计数
                        </Button>
                        <Button onClick={clearLogs}>
                            清空日志
                        </Button>
                    </Space>
                </div>

                <Divider />

                {/* 调试日志 */}
                <div className="gv-debugger-section">
                    <Title level={5}>调试日志</Title>
                    <div className="gv-debugger-logs" style={{ maxHeight: '200px', overflow: 'auto' }}>
                        {debugLogs.map((log, index) => (
                            <div key={index} className={`gv-debugger-log gv-debugger-log-${log.type}`}>
                                <Text code style={{ fontSize: '12px' }}>{log.timestamp}</Text>
                                <Text 
                                    type={log.type === 'error' ? 'danger' : 
                                         log.type === 'warning' ? 'warning' : 
                                         log.type === 'success' ? 'success' : undefined}
                                    style={{ marginLeft: 8 }}
                                >
                                    {log.message}
                                </Text>
                            </div>
                        ))}
                        {debugLogs.length === 0 && (
                            <Text type="secondary">暂无调试日志</Text>
                        )}
                    </div>
                </div>
            </Card>
                </div>
            )}
        </div>
    );
});

export default GlobalVariableDebugger;

/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

html {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;

  .lc-background {
    position: relative;
    overflow: hidden;
  }

  //大屏预览模式下，数据加载异常时样式
  .view-error-message {
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(255, 0, 0, 0.24);
    width: 100%;
    height: 100%;
    pointer-events: none;
    color: #ff1e1e;
    font-weight: 500;
    border: 2px dashed red;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center
  }
}
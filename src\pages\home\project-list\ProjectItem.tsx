/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import './ProjectItem.less';
import {Copy, Delete, Edit, PreviewOpen} from "@icon-park/react";
import {Input, Popover} from "antd";
import {useState} from "react";
import {IProjectInfo, SaveType} from "../../../designer/DesignerType.ts";
import operatorMap from "../../../framework/operate";

export interface ProjectItemProps {
    id: string;
    name: string;
    cover?: string;
    saveType: SaveType;
    doOperate: (id: string, type: string) => void;
}

export default function ProjectItem(props: ProjectItemProps) {
    const {cover, id, saveType, doOperate} = props;
    const [rename, setRename] = useState(false);
    const [name, setName] = useState(props.name);

    const updateName = () => {
        if (name !== props.name) {
            const data: IProjectInfo = {id, name};
            operatorMap[saveType].updateProject(data);
        }
    }

    return (
        <div className="project-list-item">
            <div className="project-item-cover" style={{backgroundImage: `url(${cover})`}}>
                <div className="operate-icon-list">
                    <div className="operate-icon"><Popover content={'编辑项目'}><Edit
                        onClick={() => doOperate(id, "edit")}/></Popover></div>
                    <div className="operate-icon"><Popover content={'预览项目'}><PreviewOpen
                        onClick={() => doOperate(id, "show")}/></Popover></div>
                    <div className="operate-icon"><Popover content={'删除项目'}><Delete
                        onClick={() => doOperate(id, "del")}/></Popover></div>
                    <div className="operate-icon"><Popover content={'复制项目'}><Copy
                        onClick={() => doOperate(id, "clone")}/></Popover></div>
                </div>
            </div>
            <div className="project-item-content">
                <div className="project-name">
                    {rename ?
                        <div className="project-rename-input">
                            <Input autoFocus={true}
                                   size={"small"}
                                   value={name}
                               onBlur={() => {
                                       updateName();
                                   setRename(false);
                               }}
                               onKeyDown={(event) => {
                                   if (event.key === 'Enter') {
                                           updateName();
                                       setRename(false);
                                   }
                               }}
                                   onChange={(e) => setName(e.target.value)}/>
                        </div> :
                        <div className="project-name-content">{name}</div>
                    }
                </div>
                <div className="rename-icon" onClick={() => setRename(true)}>
                    {rename || <Popover content={'重命名'}><Edit/></Popover>}
                </div>
            </div>
        </div>
    )
}
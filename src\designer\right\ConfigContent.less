/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

@import "../../designer/style/DesignerStyle.less";

.lc-config-panel {
  z-index: 1;
  animation: lcFadeInUp 0.7s forwards cubic-bezier(0.175, 0.885, 0.32, 1.275);
  width: 330px;
  top: 50px;
  right: 35px;
  position: absolute;
  height: calc(100% - 90px);
  background-color: @rightConfigPanelBgColor;
  border-left: 1px solid #2e2e2e;

  .lc-panel-top {
    width: 100%;
    height: 30px;
    display: flex;
    color: @rightConfigPanelTitleColor;
    padding: 5px 10px;
    align-items: center;
    background-color: @rightConfigPanelTitleBgColor;
    justify-content: space-between;

    .panel-operate {
      padding: 4px 8px;
    }

    .panel-operate:hover {
      cursor: pointer;
    }
  }

  .lc-panel-content {
    height: calc(100% - 30px);
    overflow-y: auto;
    padding: 10px;
  }

  .resize-handle {
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    cursor: col-resize;
    background-color: #64646400;
    transition: background-color 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .resize-handle:hover {
    background-color: #1645a4;
  }

}


@keyframes lcFadeInUp {
  0% {
    opacity: 0;
    transform: translateX(35px);
  }
  50% {
    opacity: 1;
    transform: translateX(35px);
  }
  55% {
    opacity: 1;
    transform: translateX(35px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }

}
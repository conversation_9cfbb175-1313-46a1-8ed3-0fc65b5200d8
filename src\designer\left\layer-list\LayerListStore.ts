/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {action, makeObservable, observable} from "mobx";
import eventOperateStore from "../../operate-provider/EventOperateStore";
import {Component, MouseEvent} from "react";
import {setControlPointLineColor} from "../../operate-provider/movable/DesignerSelectable";
import historyRecordOperateProxy from "../../operate-provider/undo-redo/HistoryRecordOperateProxy";
import LayerUtil from "./util/LayerUtil";
import layerManager from "../../manager/LayerManager.ts";
import {ILayerItem} from "../../DesignerType.ts";
import { LayerDragItem } from "./constants/DragTypes";
import layerBuilder, { LayerOrder } from "./LayerBuilder";

class LayerListStore {
    constructor() {
        makeObservable(this, {
            searchLayer: observable,
            setSearchLayer: action,
        });
    }

    layerInstances: Record<string, Component> = {};

    searchLayer: boolean = false;

    setSearchLayer = (searchlayer: boolean) => this.searchLayer = searchlayer;


    /**
     * 更新图层的锁定状态，包括普通图层和分组图层
     * 普通图层直接更新即可，分组图层要更新分组图层中的所有子图层
     */
    lockChange = (id: string, lock: boolean) => {
        const updData = [];
        const {layerConfigs} = layerManager;
        const {type} = layerConfigs[id];
        if (type === 'group') {
            //分组图层
            LayerUtil.findAllChildLayer([id]).forEach(id => updData.push({id, lock}));
        } else {
            //普通图层
            updData.push({id, lock});
        }
        historyRecordOperateProxy.doLockUpd(updData);
    }

    /**
     * 通过图层选中组件
     * 直接点击图层为单选：
     *  1. 普通图层可以直接选中
     *  2. 分组图层选中时要同时选中分组图层中的所有子图层
     * 按照ctrl键多选：
     *  1. 多选普通图层为增量选中，比如选中A，按照ctrl键选中B，则A和B都会被选中。按住ctrl多次选中同一个图层，则这个图层会被取消选中
     *  2. 多选分组图层时同样为增量选中，基本规则与普通图层相同，但是多选分组图层时要同时选中它的所有子图层
     */
    selectedChange = (id: string, event: MouseEvent) => {
        const {targetIds, setTargetIds} = eventOperateStore;
        const {layerConfigs} = layerManager;
        const {type, lock} = layerConfigs[id];
        if (!type) return;
        const groupLayer = type === 'group';
        let selectedLayerIds: string[] = [];
        //处理单选多选
        if (event.ctrlKey) {
            //多选
            if (targetIds.includes(id)) {
                //多选模式下同一图层再次点击视为取消选中
                if (groupLayer) {
                    //分组图层
                    const toBeCancelled = LayerUtil.findAllChildLayer([id]);
                    selectedLayerIds = targetIds.filter(_id => !toBeCancelled.includes(_id));
                } else
                    selectedLayerIds = targetIds.filter(_id => _id !== id);
            } else {
                /**
                 * 多选时，不能同时选中锁定状态不一致的组件，若存在状态不一致的场景，只选中未锁定的组件
                 */
                if (targetIds.length === 0) {
                    //多选模式首次选中
                    if (groupLayer)
                        selectedLayerIds = LayerUtil.findAllChildLayer([id]);
                    else
                        selectedLayerIds = [id];
                } else {
                    const firstLock = layerConfigs[targetIds[0]].lock;
                    if (lock !== firstLock) {
                        //只选中未锁定的组件
                        if (groupLayer)
                            selectedLayerIds = [...targetIds, ...LayerUtil.findAllChildLayer([id])].filter(id => !layerConfigs[id].lock);
                        else
                            selectedLayerIds = [...targetIds, id].filter(id => !layerConfigs[id].lock);
                    } else {
                        //直接在已选择的组件上增量本次选中的图层
                        if (groupLayer)
                            selectedLayerIds = [...targetIds, ...LayerUtil.findAllChildLayer([id])];
                        else
                            selectedLayerIds = [...targetIds, id];
                    }
                    //多选模式下需要去重，（避免在图层列表选中先选中子图层，再多选选中分组图层时候，分组图层被重复计算的问题）
                    selectedLayerIds = [...new Set(selectedLayerIds)];
                }
            }
        } else if (event.shiftKey) {
            if (targetIds.length === 1) {
                const startLayer = layerConfigs[targetIds[0]];
                const endLayer = layerConfigs[id];
                let maxId, minId;
                if (startLayer.order! > endLayer.order!) {
                    maxId = targetIds[0];
                    minId = id;
                } else {
                    maxId = id;
                    minId = targetIds[0];
                }
                //按住shift后点击的是原图层，则不处理
                if (maxId === minId)
                    return;

                let finished = false;
                const calculateLayerIds = (selectedIds: string[], nextLayer: ILayerItem, targetId: string) => {
                    if (!nextLayer || finished)
                        return;
                    if (nextLayer.type === 'group') {
                        selectedIds.push(nextLayer.id!)
                        const childHeaderLayer = layerConfigs[nextLayer.childHeader!];
                        if (!childHeaderLayer)
                            return;
                        calculateLayerIds(selectedLayerIds, childHeaderLayer, targetId);
                    } else {
                        selectedIds.push(nextLayer.id!);
                    }
                    if (nextLayer.id === targetId) {
                        finished = true;
                        return;
                    }
                    if (nextLayer.pid && !nextLayer.next) {
                        //查找到分组图层的最后一个元素依然没有匹配到目标图层，则跳转到父级图层往下继续匹配
                        let safeLock = 0; //防止死循环
                        let parentLayer = layerConfigs[nextLayer.pid];
                        let tempNext = layerConfigs[parentLayer.next!];
                        while (!tempNext && parentLayer && safeLock < 1000) {
                            parentLayer = layerConfigs[parentLayer.pid!];
                            tempNext = layerConfigs[parentLayer?.next ?? ''];
                            safeLock++;
                        }
                        //向下查找之前再次判断parentLayer是否已经是要查找的目标节点
                        if (parentLayer && parentLayer.id === targetId) {
                            finished = true;
                            return;
                        }
                        calculateLayerIds(selectedIds, tempNext, targetId);
                    } else
                        calculateLayerIds(selectedIds, layerConfigs[nextLayer.next!], targetId);
                }

                const maxLayer = layerConfigs[maxId];
                selectedLayerIds.push(maxId);
                if (maxLayer.type === 'group') {
                    calculateLayerIds(selectedLayerIds, layerConfigs[maxLayer.childHeader!], minId);
                } else if (maxLayer.pid && !maxLayer.next) {
                    //第一个图层是分组图层下的最后一个子图层，则跳转到父级图层往下继续匹配
                    let safeLock = 0; //防止死循环
                    let parentLayer = layerConfigs[maxLayer.pid];
                    let tempNext = layerConfigs[parentLayer.next!];
                    while (!tempNext && safeLock < 1000) {
                        parentLayer = layerConfigs[parentLayer.pid!];
                        tempNext = layerConfigs[parentLayer.next!];
                        safeLock++;
                    }
                    calculateLayerIds(selectedLayerIds, tempNext, minId);
                } else {
                    calculateLayerIds(selectedLayerIds, layerConfigs[maxLayer.next!], minId);
                }
            }
        } else {
            //单选
            if (groupLayer)
                selectedLayerIds = LayerUtil.findAllChildLayer([id]);
            else
                selectedLayerIds = [id];
        }
        const targetTimer = setTimeout(() => {
            setTargetIds(selectedLayerIds);
            clearTimeout(targetTimer);
        }, 0)

        //更新选中组件的边框颜色（锁定状态组件为红色，非锁定状态组件为蓝色）
        const finalLock = layerConfigs[selectedLayerIds[0]]?.lock;
        const tempTimer = setTimeout(() => {
            setControlPointLineColor(finalLock!);
            clearTimeout(tempTimer);
        }, 0)

    }

    hideChange = (id: string, hide: boolean) => {
        const updData = [];
        const {layerConfigs} = layerManager;
        const {type} = layerConfigs[id];
        if (type === 'group') {
            //分组图层
            LayerUtil.findAllChildLayer([id]).forEach(id => updData.push({id, hide}));
        } else {
            //普通图层
            updData.push({id, hide});
        }
        historyRecordOperateProxy.doHideUpd(updData);
    }

    /**
     * 移动图层位置
     * @param dragItem 被拖拽的图层
     * @param hoverItem 放置的目标图层
     * @param dropPosition 放置位置（top: 目标图层上方, bottom: 目标图层下方）
     */
    moveLayer = (dragItem: LayerDragItem, hoverItem: LayerDragItem, dropPosition: 'top' | 'bottom' | null = null) => {
        console.log('moveLayer', dragItem, hoverItem);

        // 如果是同一个图层，不处理
        if (dragItem.id === hoverItem.id) return;

        const { layerConfigs } = layerManager;
        const dragLayer = layerConfigs[dragItem.id];
        const hoverLayer = layerConfigs[hoverItem.id];

        if (!dragLayer || !hoverLayer) {
            console.error('图层不存在', dragItem.id, hoverItem.id);
            return;
        }

        // 如果拖拽的是分组图层，且目标图层是该分组的子图层，不处理
        if (dragLayer.type === 'group' && LayerUtil.findAllChildLayer([dragItem.id], false).includes(hoverItem.id)) {
            console.log('不能将分组拖到其子图层中');
            return;
        }

        // 如果拖拽的图层和目标图层不在同一个父级下，不处理
        if (dragLayer.pid !== hoverLayer.pid) {
            console.log('不在同一个父级下');
            return;
        }

        // 更新链表关系
        const dragPrev = dragLayer.prev;
        const dragNext = dragLayer.next;
        const hoverPrev = hoverLayer.prev;
        const hoverNext = hoverLayer.next;

        // 检查是否为相邻图层的无效拖拽
        // 如果拖拽图层在目标图层上方，且要放置到目标图层上方，则无效
        if (dragNext === hoverItem.id && dropPosition === 'top') {
            console.log('拖拽图层已在目标图层上方');
            return;
        }
        // 如果拖拽图层在目标图层下方，且要放置到目标图层下方，则无效
        if (hoverNext === dragItem.id && dropPosition === 'bottom') {
            console.log('拖拽图层已在目标图层下方');
            return;
        }

        console.log('开始移动图层', {
            dragPrev, dragNext, hoverPrev, hoverNext, dropPosition
        });

        // 先从原位置移除拖拽图层
        if (dragPrev) {
            layerConfigs[dragPrev].next = dragNext;
        }
        if (dragNext) {
            layerConfigs[dragNext].prev = dragPrev;
        }

        // 根据 dropPosition 决定插入位置
        if (dropPosition === 'top') {
            // 插入到目标图层上方
            dragLayer.prev = hoverPrev;
            dragLayer.next = hoverItem.id;

            if (hoverPrev) {
                layerConfigs[hoverPrev].next = dragItem.id;
            }
            hoverLayer.prev = dragItem.id;
        } else {
            // 插入到目标图层下方（默认行为）
            dragLayer.prev = hoverItem.id;
            dragLayer.next = hoverNext;

            if (hoverNext) {
                layerConfigs[hoverNext].prev = dragItem.id;
            }
            hoverLayer.next = dragItem.id;
        }

        // 更新头部和尾部图层的指针
        // 如果拖拽的图层原来是头部图层，需要更新父图层的childHeader
        if (!dragPrev && dragLayer.pid) {
            layerConfigs[dragLayer.pid].childHeader = dragNext;
        }
        // 如果拖拽的图层原来是尾部图层，需要更新父图层的childTail
        if (!dragNext && dragLayer.pid) {
            layerConfigs[dragLayer.pid].childTail = dragPrev;
        }
        // 如果拖拽的图层原来是根级头部图层，需要更新layerHeader
        if (!dragLayer.pid && !dragPrev) {
            layerManager.layerHeader = dragNext;
        }

        // 根据插入位置更新头部和尾部指针
        if (dropPosition === 'top') {
            // 插入到目标图层上方
            if (!hoverPrev && hoverLayer.pid) {
                // 目标图层原来是头部图层，现在拖拽图层成为新的头部图层
                layerConfigs[hoverLayer.pid].childHeader = dragItem.id;
            }
            if (!hoverLayer.pid && !hoverPrev) {
                // 目标图层原来是根级头部图层，现在拖拽图层成为新的根级头部图层
                layerManager.layerHeader = dragItem.id;
            }
        } else {
            // 插入到目标图层下方
            if (!hoverNext && hoverLayer.pid) {
                // 目标图层原来是尾部图层，现在拖拽图层成为新的尾部图层
                layerConfigs[hoverLayer.pid].childTail = dragItem.id;
            }
        }

        console.log('图层移动完成');

        // 重新渲染图层
        layerManager.reRenderLayer();

        // 更新图层顺序
        this.updateLayerOrder();
    }

    /**
     * 更新图层顺序
     */
    updateLayerOrder = () => {
        const { layerConfigs } = layerManager;
        let order = 1;

        console.log('开始更新图层顺序');

        // 使用LayerBuilder的parser方法获取排序后的图层数组
        const layerArr = layerBuilder.parser(layerConfigs, LayerOrder.ASC);

        // 更新每个图层的order属性
        layerArr.forEach(layer => {
            if (layer && layer.id) {
                const targetLayer = layerConfigs[layer.id];
                if (targetLayer) {
                    targetLayer.order = order++;
                    console.log(`图层 ${targetLayer.name}(${targetLayer.id}) 的顺序更新为 ${targetLayer.order}`);
                }
            }
        });

        console.log('图层顺序更新完成');
    }
}

const layerListStore = new LayerListStore();
export default layerListStore;
# 基础表格组件自定义代码功能

## 功能概述

基础表格组件现在支持自定义代码功能，允许用户为每个列编写自定义的渲染逻辑，支持使用Antd组件创建丰富的表格内容。

## 使用方法

### 1. 启用自定义代码

1. 选择基础表格组件
2. 在右侧配置面板中，找到"样式"选项卡
3. 展开"表格列"配置
4. 选择要自定义的列
5. 开启"自定义渲染"开关

### 2. 编写自定义代码

开启自定义渲染后，会出现代码编辑器。您可以编写一个函数来自定义该列的渲染内容：

```javascript
function(data, rowData, rowIndex) {
    // data: 所有表格数据
    // rowData: 当前行数据
    // rowIndex: 当前行索引
    
    const status = rowData.status;
    
    if (status === 'active') {
        return <antd.Tag color="green">激活</antd.Tag>;
    } else if (status === 'pending') {
        return <antd.Tag color="orange">待审核</antd.Tag>;
    } else {
        return <antd.Tag color="red">禁用</antd.Tag>;
    }
}
```

## 可用组件

通过 `antd` 命名空间，您可以访问以下Antd组件：

### 基础组件
- `antd.Button` - 按钮
- `antd.Tag` - 标签
- `antd.Badge` - 徽标
- `antd.Avatar` - 头像
- `antd.Image` - 图片

### 反馈组件
- `antd.Progress` - 进度条
- `antd.Spin` - 加载中
- `antd.Alert` - 警告提示
- `antd.Result` - 结果页
- `antd.Empty` - 空状态

### 数据展示
- `antd.Statistic` - 统计数值
- `antd.Card` - 卡片
- `antd.List` - 列表
- `antd.Descriptions` - 描述列表
- `antd.Timeline` - 时间轴

### 导航组件
- `antd.Breadcrumb` - 面包屑
- `antd.Dropdown` - 下拉菜单
- `antd.Menu` - 导航菜单
- `antd.Pagination` - 分页

### 表单组件
- `antd.Input` - 输入框
- `antd.Select` - 选择器
- `antd.Checkbox` - 多选框
- `antd.Radio` - 单选框
- `antd.Switch` - 开关
- `antd.Slider` - 滑动输入条

### 其他工具
- `React` - React核心库
- `globalVars.get(name)` - 获取全局变量

## 使用示例

### 1. 状态标签

```javascript
function(data, rowData, rowIndex) {
    const statusMap = {
        'active': { color: 'green', text: '激活' },
        'inactive': { color: 'red', text: '禁用' },
        'pending': { color: 'orange', text: '待审核' }
    };
    
    const status = statusMap[rowData.status] || { color: 'default', text: rowData.status };
    return <antd.Tag color={status.color}>{status.text}</antd.Tag>;
}
```

### 2. 进度条

```javascript
function(data, rowData, rowIndex) {
    const progress = rowData.progress || 0;
    
    return (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <antd.Progress 
                percent={progress} 
                size="small" 
                status={progress === 100 ? 'success' : 'active'}
                style={{ width: '100px' }}
            />
            <span>{progress}%</span>
        </div>
    );
}
```

### 3. 操作按钮

```javascript
function(data, rowData, rowIndex) {
    const handleEdit = () => {
        console.log('编辑', rowData);
        // 可以通过全局变量传递数据
        // globalVars.get('selectedRow') 
    };
    
    const handleDelete = () => {
        console.log('删除', rowData);
    };
    
    return (
        <antd.Space>
            <antd.Button type="primary" size="small" onClick={handleEdit}>
                编辑
            </antd.Button>
            <antd.Button type="link" size="small" danger onClick={handleDelete}>
                删除
            </antd.Button>
        </antd.Space>
    );
}
```

### 4. 用户头像

```javascript
function(data, rowData, rowIndex) {
    return (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <antd.Avatar src={rowData.avatar} size="small">
                {rowData.name?.charAt(0)}
            </antd.Avatar>
            <span>{rowData.name}</span>
        </div>
    );
}
```

### 5. 使用全局变量

```javascript
function(data, rowData, rowIndex) {
    // 获取全局变量
    const theme = globalVars.get('theme') || 'light';
    const userRole = globalVars.get('userRole') || 'guest';
    
    // 根据用户角色显示不同内容
    if (userRole === 'admin') {
        return <antd.Tag color="gold">管理员</antd.Tag>;
    } else {
        return <antd.Tag color="blue">普通用户</antd.Tag>;
    }
}
```

## 注意事项

1. **语法支持**：支持完整的JSX语法，代码会在运行时通过Babel转换
2. **错误处理**：如果代码执行出错，会显示错误信息并回退到默认渲染
3. **性能考虑**：自定义代码会在每次渲染时执行，避免在代码中进行复杂计算
4. **作用域**：用户定义的变量会覆盖注入的组件，建议使用 `antd.` 前缀访问组件
5. **事件处理**：可以在自定义代码中添加事件处理函数，但要注意避免内存泄漏

## 故障排除

### 常见错误

1. **JSX语法错误**：检查标签是否正确闭合
2. **组件不存在**：确保使用 `antd.` 前缀访问组件
3. **运行时错误**：检查数据字段是否存在，添加默认值处理

### 调试技巧

1. 使用 `console.log()` 输出调试信息
2. 在浏览器开发者工具中查看错误信息
3. 先用简单的代码测试，再逐步增加复杂度

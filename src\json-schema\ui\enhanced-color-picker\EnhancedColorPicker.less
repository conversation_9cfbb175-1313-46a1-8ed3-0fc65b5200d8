/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.enhanced-color-picker {
  display: flex;
  align-items: center;

  .color-preview-box {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: rgba(255, 255, 255, 0.3);
    }
  }

  .color-text {
    margin-left: 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.85);
  }
}

.enhanced-color-picker-popover {
  .ant-popover-inner {
    background-color: #1f1f1f;
    border: 1px solid #434343;
  }

  .ant-popover-arrow::before {
    background-color: #1f1f1f;
  }

  .color-picker-panel {
    width: 280px;

    .ant-tabs-nav {
      margin-bottom: 8px;
    }

    .gradient-preview {
      width: 100%;
      height: 24px;
      border-radius: 4px;
      margin-bottom: 12px;
    }

    .gradient-controls {
      .angle-control, .position-control {
        margin-bottom: 12px;

        span {
          margin-right: 8px;
        }
      }

      .color-stops {
        .color-stop-item {
          margin-bottom: 12px;
          padding: 8px;
          border: 1px solid #434343;
          border-radius: 4px;

          .color-stop-controls {
            display: flex;
            align-items: flex-start;
            gap: 8px;

            .slider-group {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 8px;

              .slider-item {
                display: flex;
                align-items: center;
                gap: 8px;

                span {
                  min-width: 60px;
                  font-size: 12px;
                  color: rgba(255, 255, 255, 0.85);
                }

                .offset-slider, .opacity-slider {
                  flex: 1;
                }
              }
            }
          }
        }
      }
    }
  }
}

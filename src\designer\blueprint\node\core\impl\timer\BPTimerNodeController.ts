/*
 * @Author: <EMAIL>
 * @Date: 2024-05-26 12:00:00
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-05-26 12:00:00
 * @Description: 定时器蓝图节点控制器
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {AbstractBPNodeController, AnchorPointType, ExecuteInfoType, NodeInfoType} from "../../AbstractBPNodeController";
import ComponentUtil from "../../../../../../utils/ComponentUtil";
import BPNode, {NodeProps} from "../../../BPNode";
import React from "react";
import ObjectUtil from "../../../../../../utils/ObjectUtil";
import BPExecutor from "../../../../core/BPExecutor";
import {TimerNodeConfig} from "./TimerNodeConfig.tsx";

export interface TimerNodeConfigType extends NodeProps {
    isLoop?: boolean;        // 是否循环
    interval?: number;       // 时间间隔（毫秒）
    autoStart?: boolean;     // 是否自动启动
    isRunning?: boolean;     // 是否正在运行（内部状态，不在配置界面显示）
}

export default class BPTimerNodeController extends AbstractBPNodeController<TimerNodeConfigType> {

    private timerId: NodeJS.Timeout | null = null;
    private executor: BPExecutor | null = null;

    constructor(config?: TimerNodeConfigType) {
        super(config);

        // 在预览模式下，如果配置了自动启动，则立即启动定时器
        if (config?.autoStart && this.isViewMode()) {
            // 延迟启动，确保所有节点都已初始化
            setTimeout(() => {
                const executor = new BPExecutor();
                this.startTimer(config.id || '', executor);
            }, 100);
        }
    }

    /**
     * 检查是否为预览模式
     */
    private isViewMode(): boolean {
        // 通过URL参数或全局环境变量判断当前模式
        const urlParams = new URLSearchParams(window.location.search);
        const mode = urlParams.get('mode') || window.LC_ENV?.mode;
        return mode === '1'; // DesignerMode.VIEW = '1'
    }

    async create(container: HTMLElement, config: TimerNodeConfigType): Promise<void> {
        // 如果传入的config是NodeInfoType（从getNodeInfo返回的），则只使用其中的基本信息
        const defaultConfig = {
            isLoop: true,
            interval: 1000,
            autoStart: false,
            isRunning: false
        };

        // 合并配置，确保有完整的定时器配置
        if (config && 'input' in config && 'output' in config && !('autoStart' in config)) {
            // 这是从getNodeInfo返回的NodeInfoType，只提取基本信息
            this.config = {
                ...defaultConfig,
                id: config.id,
                name: config.name,
                type: config.type,
                titleBgColor: config.titleBgColor,
                icon: config.icon,
                input: config.input,
                output: config.output
            };
        } else {
            // 这是正常的TimerNodeConfigType配置或包含定时器配置的混合对象
            this.config = {
                ...defaultConfig,
                ...config
            };
        }

        this.container = container;
        this.instance = await ComponentUtil.createAndRender(container, BPNode, this.config);

        // 只在预览模式下启动自动定时器，设计模式下不启动
        if (this.config.autoStart && this.isViewMode()) {
            // 创建一个临时的executor来启动定时器
            const executor = new BPExecutor();
            this.startTimer(this.config.id || '', executor);
        }
    }

    execute(executeInfo: ExecuteInfoType, executor: BPExecutor): void {
        const {nodeId, apId, anchorType} = executeInfo;
        
        // 输出类型锚点不执行
        if (anchorType === AnchorPointType.OUTPUT) {
            return;
        }

        // 处理启动定时器
        if (apId === 'start') {
            this.startTimer(nodeId, executor);
        }
        // 处理停止定时器
        else if (apId === 'stop') {
            this.stopTimer();
        }
    }

    /**
     * 启动定时器
     */
    private startTimer(nodeId: string, executor: BPExecutor): void {
        if (this.timerId) {
            this.stopTimer(); // 先停止之前的定时器
        }

        this.executor = executor;
        this.config!.isRunning = true;

        const executeTimer = () => {
            // 触发定时执行输出锚点
            const timerOutputAnchorId = `${nodeId}:timerExecute:${AnchorPointType.OUTPUT}`;
            const timerInfo = {
                nodeId: nodeId,
                timestamp: Date.now(),
                interval: this.config!.interval
            };
            executor.execute(timerOutputAnchorId, executor, timerInfo);

            // 如果是循环模式，继续设置下一次定时器
            if (this.config!.isLoop && this.config!.isRunning) {
                this.timerId = setTimeout(executeTimer, this.config!.interval || 1000);
            } else {
                // 非循环模式，执行一次后停止
                this.config!.isRunning = false;
                this.timerId = null;
            }
        };

        // 首次执行
        if (this.config!.isLoop) {
            this.timerId = setTimeout(executeTimer, this.config!.interval || 1000);
        } else {
            executeTimer();
        }
    }

    /**
     * 停止定时器
     */
    private stopTimer(): void {
        if (this.timerId) {
            clearTimeout(this.timerId);
            this.timerId = null;
        }
        if (this.config) {
            this.config.isRunning = false;
        }
    }

    getConfig(): TimerNodeConfigType | null {
        return this.config;
    }

    update(config: TimerNodeConfigType): void {
        const oldConfig = this.config;
        this.config = ObjectUtil.merge(this.config, config);

        // 如果定时器正在运行且配置发生变化，重启定时器
        if (oldConfig?.isRunning && this.executor &&
            (oldConfig.interval !== this.config?.interval || oldConfig.isLoop !== this.config?.isLoop)) {
            this.stopTimer();
            this.startTimer(this.config?.id || '', this.executor);
        }

        // 处理自动启动配置的变化
        if (oldConfig?.autoStart !== this.config?.autoStart) {
            if (this.config?.autoStart && !this.config?.isRunning) {
                // 开启自动启动且当前未运行，则启动定时器
                const executor = new BPExecutor();
                this.startTimer(this.config?.id || '', executor);
            } else if (!this.config?.autoStart && this.config?.isRunning) {
                // 关闭自动启动且当前正在运行，则停止定时器
                this.stopTimer();
            }
        }
    }

    getNodeInfo(nodeId: string): NodeInfoType | null {
        return {
            id: nodeId,
            name: "Timer",
            titleBgColor: "#f39c12", // 橙色主题
            type: "timer-node",
            icon: "FunctionOutlined",
            input: [
                {
                    id: nodeId + ':start:' + AnchorPointType.INPUT,
                    name: "启动",
                    type: AnchorPointType.INPUT
                },
                {
                    id: nodeId + ':stop:' + AnchorPointType.INPUT,
                    name: "停止",
                    type: AnchorPointType.INPUT
                }
            ],
            output: [
                {
                    id: nodeId + ":timerExecute:" + AnchorPointType.OUTPUT,
                    name: "定时执行",
                    type: AnchorPointType.OUTPUT
                }
            ]
        };
    }

    public getConfigComponent(): React.ComponentType | null {
        return TimerNodeConfig;
    }

    /**
     * 销毁时清理定时器
     */
    public destroy(): void {
        this.stopTimer();
        super.destroy && super.destroy();
    }
}

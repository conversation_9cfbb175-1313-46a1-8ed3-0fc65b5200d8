/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.base-text-component {
  --stroke-color: #000000;
  --stroke-width: 0;

  height: 100%;
  display: flex;
  -webkit-text-stroke-color: var(--stroke-color);
  -webkit-text-stroke-width: var(--stroke-width);
  stroke-color: var(--stroke-color);
  stroke-width: var(--stroke-width);

  input {
    outline: none;
    width: 100%;
    background-color: rgba(255, 222, 173, 0);
    text-align: center;
    border: none;
  }
}
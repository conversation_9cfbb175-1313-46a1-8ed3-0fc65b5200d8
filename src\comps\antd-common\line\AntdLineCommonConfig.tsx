/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React from 'react';
import {AntdCartesianCoordinateSys} from "../config/AntdFragment";
import {ColorAttr, LineOptions, ShapeStyle} from "@antv/g2plot";
import AntdCommonLineController from "./AntdCommonLineController";
import AntdCommonUtil from "../AntdCommonUtil";
import {Control} from "../../../json-schema/SchemaTypes";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI";
import {ShapeAttrs} from "@antv/g-base";
import {ConfigType} from "../../../designer/right/ConfigContent";
import {AntdBaseDesignerController} from "../AntdBaseDesignerController.ts";
import ColorUtil from "../../../utils/ColorUtil";


export function AntdLineCommonStyleConfig(props: ConfigType<AntdCommonLineController>) {
    const {controller} = props;
    const config = controller.getConfig()!.style!;

    const lineCoordinateSysChange = (config: LineOptions) => {
        controller.update({style: config});
    }

    return (
        <>
            <AntdLineCommonGraphics controller={controller}/>
            <AntdCartesianCoordinateSys onChange={lineCoordinateSysChange} config={config}/>
        </>
    );
}

export const AntdLineCommonFieldMapping: React.FC<ConfigType<AntdBaseDesignerController>> = ({controller}) => {
    const options = AntdCommonUtil.getDataFieldOptions(controller);
    const config = controller.getConfig()?.style;
    const schema: Control = {
        type: 'grid',
        key: 'style',
        children: [
            {
                type: 'select',
                label: 'X字段',
                key: 'xField',
                value: config?.xField,
                config: {
                    options,
                }
            },
            {
                type: 'select',
                label: 'Y字段',
                key: 'yField',
                value: config?.yField,
                config: {
                    options,
                }
            },
        ]
    }

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        controller.update(fieldChangeData.dataFragment);
    }

    return <div style={{padding: 10}}><LCGUI schema={schema} onFieldChange={onFieldChange}/></div>
}

export function AntdLineCommonGraphics(props: ConfigType<AntdCommonLineController>) {

    const {controller} = props;
    const config = controller.getConfig()?.style;

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataFragment} = fieldChangeData;

        // 处理线条颜色
        if (id === 'lineColor') {
            if (data && Array.isArray(data)) {
                // 多色模式
                // 检查数组中是否有渐变对象
                const hasGradient = data.some(color =>
                    typeof color === 'object' && color !== null && 'type' in color &&
                    (color.type === 'linear-gradient' || color.type === 'radial-gradient')
                );

                if (hasGradient) {
                    // 如果数组中有渐变对象，我们需要将它们转换为渐变字符串
                    const processedColors = data.map(color => {
                        if (typeof color === 'object' && color !== null && 'type' in color &&
                            (color.type === 'linear-gradient' || color.type === 'radial-gradient')) {
                            return ColorUtil.gradientToCss(color);
                        }
                        return color;
                    });
                    controller.update({style: {color: processedColors as ColorAttr, lineStyle: {stroke: undefined}}});
                } else {
                    // 如果数组中没有渐变对象，直接使用原始数组
                    controller.update({style: {color: data as ColorAttr, lineStyle: {stroke: undefined}}});
                }
            } else if (data && typeof data === 'object' && data !== null && 'type' in data &&
                      (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {lineStyle: {stroke: gradientCss}}});
            } else if (data && typeof data === 'string' && data.indexOf('gradient') !== -1) {
                // 处理渐变字符串
                controller.update({style: {lineStyle: {stroke: data as string}}});
            } else {
                controller.update({style: {lineStyle: {stroke: data as string}}});
            }
        }
        // 处理数据点颜色
        else if (id === 'pointColor') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {point: {...config?.point, color: gradientCss}}});
            } else {
                controller.update({style: {point: {...config?.point, color: data as string}}});
            }
        }
        // 处理数据点描边颜色
        else if (id === 'pointStroke') {
            if (data && typeof data === 'object' && data !== null && 'type' in data &&
                (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                // 处理单个渐变对象
                const gradientCss = ColorUtil.gradientToCss(data);
                controller.update({style: {point: {...config?.point, style: {...config?.point?.style, stroke: gradientCss}}}});
            } else {
                controller.update({style: {point: {...config?.point, style: {...config?.point?.style, stroke: data as string}}}});
            }
        }
        else {
            controller.update(dataFragment);
        }
    }

    const schema: Control = {
        type: 'accordion',
        label: '图形',
        key: 'style',
        config: {bodyStyle: {marginTop: 10}},
        children: [
            {
                type: 'sub-accordion',
                label: '线条',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                key: 'smooth',
                                type: 'switch',
                                label: '平滑',
                                tip: '对阶梯图无效',
                                value: config?.smooth,
                            },
                            {
                                key: 'lineStyle',
                                children: [
                                    {
                                        key: 'lineWidth',
                                        type: 'number-input',
                                        label: '宽度',
                                        value: (config?.lineStyle as ShapeAttrs)?.lineWidth,
                                        config: {
                                            min: 0,
                                            max: 10,
                                        }
                                    }
                                ]
                            },
                            {
                                id: 'lineColor',
                                type: 'enhanced-color-mode',
                                label: '颜色',
                                value: config?.color,
                            }
                        ]
                    }
                ]
            },
            {
                type: 'sub-accordion',
                label: '数据点',
                children: [
                    {
                        key: 'point',
                        type: 'grid',
                        children: [
                            {
                                key: 'size',
                                type: 'number-input',
                                label: '尺寸',
                                value: config?.point?.size,
                                config: {
                                    min: 0,
                                    max: 10,
                                }
                            },
                            {
                                id: 'pointColor',
                                type: 'enhanced-color-mode',
                                label: '颜色',
                                value: (config?.point as ShapeAttrs)?.color,
                            },
                            {
                                key: 'style',
                                children: [
                                    {
                                        key: 'lineWidth',
                                        type: 'number-input',
                                        label: '描边宽',
                                        value: (config?.point?.style as ShapeStyle)?.lineWidth,
                                        config: {
                                            min: 0
                                        }
                                    },
                                    {
                                        id: 'pointStroke',
                                        type: 'enhanced-color-mode',
                                        label: '描边色',
                                        value: (config?.point?.style as ShapeStyle)?.stroke,
                                    },
                                ]
                            },
                            {
                                key: 'shape',
                                type: 'select',
                                label: '形状',
                                value: config?.point?.shape,
                                config: {
                                    options: [
                                        {value: 'circle', label: '圈形'},
                                        {value: 'square', label: '方形'},
                                        {value: 'bowtie', label: '领结'},
                                        {value: 'diamond', label: '钻石'},
                                        {value: 'hexagon', label: '六角形'},
                                        {value: 'triangle', label: '三角形'}]
                                }
                            },
                        ]
                    }
                ]
            },
        ]
    }


    return (
        <LCGUI schema={schema} onFieldChange={onFieldChange}/>
    )
}

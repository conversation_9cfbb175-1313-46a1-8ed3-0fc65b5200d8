/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by <PERSON><PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {Axis} from "@antv/g2plot";
import cloneDeep from "lodash/cloneDeep";
import isEqual from "lodash/isEqual";
import {Control} from "../../../../json-schema/SchemaTypes";
import {FieldChangeData, LCGUI} from "../../../../json-schema/LCGUI";
import {ShapeAttrs} from "@antv/g-base";
import {useRef, useState} from "react";
import ColorUtil from "../../../../utils/ColorUtil";


interface AxisConfigProps {
    config?: Axis;
    onChange: (data?: Axis) => void;
    title?: string;
}


export const AxisConfig = (props: AxisConfigProps) => {
    const {config, onChange, title} = props;
    const {grid, line, label, title: _title, tickLine, subTickLine} = config || {};
    const enableRef = useRef<boolean>(!!(grid || line || label || _title || tickLine || subTickLine));
    const defaultData: Axis = {
        label: {
            style: {
                fill: "#00FFEAFF"
            }
        },
        line: {
            style: {
                stroke: "#00dbffff",
                lineWidth: 1
            }
        },
    }
    const oldData: Axis = cloneDeep(config!);
    const emptyData: Axis = {
        grid: null,
        line: null,
        title: null,
        label: null,
        tickLine: null,
        subTickLine: null,
    }
    const [count, setCount] = useState(0);

    const labelData = (config as any).label;
    const titleData = (config as any).title;
    const lineData = (config as any).line;
    const gridLineData = (config as any).grid;
    const tickLineData = (config as any).tickLine;
    const subTickLineData = (config as any).subTickLine;

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {id, data, dataFragment, reRender} = fieldChangeData;
        switch (id) {
            case 'showAxis':
                onChange(data ? (isEqual(oldData, emptyData) ? defaultData : oldData) : emptyData);
                enableRef.current = !!data;
                break;
            case 'titleSwitch':
                if (data) onChange({title: {text: '标题', offset: 0, style: {fill: '#d2d2d2', fontSize: 12}}});
                else onChange({title: null});
                break;
            case 'lineSwitch':
                if (data) onChange({line: {style: {stroke: '#595959', lineWidth: 1}}});
                else onChange({line: null});
                break;
            case 'gridLineSwitch':
                if (data) {
                    const defaultConfig = {
                        alignTick: true,
                        line: {style: {stroke: '#5c5c5c', lineWidth: 1} as ShapeAttrs, type: 'line'}
                    }
                    onChange({grid: defaultConfig});
                } else {
                    onChange({grid: null});
                }
                break;
            case 'subTickLineSwitch':
                if (data) {
                    const defaultConfig = {
                        count: 5,
                        length: 1,
                        style: {stroke: '#6e6e6e', lineWidth: 1} as ShapeAttrs
                    };
                    onChange({subTickLine: defaultConfig});
                } else {
                    onChange({subTickLine: null});
                }
                break;
            case 'tickLineSwitch':
                if (data) {
                    const defaultConfig = {
                        alignTick: true,
                        length: 1,
                        style: {stroke: '#6e6e6e', lineWidth: 1} as ShapeAttrs
                    };
                    onChange({tickLine: defaultConfig});
                } else {
                    onChange({tickLine: null});
                }
                break;
            // 处理标签文本颜色
            case 'labelFill':
                if (data && typeof data === 'object' && data !== null && 'type' in data &&
                    (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                    // 处理渐变对象
                    const gradientCss = ColorUtil.gradientToCss(data);
                    onChange({label: {...config?.label, style: {...config?.label?.style, fill: gradientCss}}});
                } else {
                    onChange({label: {...config?.label, style: {...config?.label?.style, fill: data as string}}});
                }
                break;
            // 处理标题颜色
            case 'titleFill':
                if (data && typeof data === 'object' && data !== null && 'type' in data &&
                    (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                    // 处理渐变对象
                    const gradientCss = ColorUtil.gradientToCss(data);
                    onChange({title: {...config?.title, style: {...config?.title?.style, fill: gradientCss}}});
                } else {
                    onChange({title: {...config?.title, style: {...config?.title?.style, fill: data as string}}});
                }
                break;
            // 处理轴线颜色
            case 'lineStroke':
                if (data && typeof data === 'object' && data !== null && 'type' in data &&
                    (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                    // 处理渐变对象
                    const gradientCss = ColorUtil.gradientToCss(data);
                    onChange({line: {...config?.line, style: {...config?.line?.style, stroke: gradientCss}}});
                } else {
                    onChange({line: {...config?.line, style: {...config?.line?.style, stroke: data as string}}});
                }
                break;
            // 处理网格线颜色
            case 'gridStroke':
                if (data && typeof data === 'object' && data !== null && 'type' in data &&
                    (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                    // 处理渐变对象
                    const gradientCss = ColorUtil.gradientToCss(data);
                    onChange({grid: {...config?.grid, line: {...config?.grid?.line, style: {...config?.grid?.line?.style, stroke: gradientCss}}}});
                } else {
                    onChange({grid: {...config?.grid, line: {...config?.grid?.line, style: {...config?.grid?.line?.style, stroke: data as string}}}});
                }
                break;
            // 处理刻度线颜色
            case 'tickStroke':
                if (data && typeof data === 'object' && data !== null && 'type' in data &&
                    (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                    // 处理渐变对象
                    const gradientCss = ColorUtil.gradientToCss(data);
                    onChange({tickLine: {...config?.tickLine, style: {...config?.tickLine?.style, stroke: gradientCss}}});
                } else {
                    onChange({tickLine: {...config?.tickLine, style: {...config?.tickLine?.style, stroke: data as string}}});
                }
                break;
            // 处理子刻度线颜色
            case 'subTickStroke':
                if (data && typeof data === 'object' && data !== null && 'type' in data &&
                    (data.type === 'linear-gradient' || data.type === 'radial-gradient')) {
                    // 处理渐变对象
                    const gradientCss = ColorUtil.gradientToCss(data);
                    onChange({subTickLine: {...config?.subTickLine, style: {...config?.subTickLine?.style, stroke: gradientCss}}});
                } else {
                    onChange({subTickLine: {...config?.subTickLine, style: {...config?.subTickLine?.style, stroke: data as string}}});
                }
                break;
            default:
                onChange(dataFragment);
        }
        if (reRender)
            setCount(count + 1);
    }


    const schema: Control[] = [
        {
            type: 'accordion',
            label: title,
            children: [
                {
                    type: 'sub-accordion',
                    label: '基础',
                    children: [
                        {
                            type: 'grid',
                            children: [
                                {
                                    id: 'showAxis',
                                    key: 'showAxis',
                                    type: 'switch',
                                    label: '显示',
                                    reRender:true,
                                    value: enableRef.current,
                                },
                                {
                                    rules:  "{showAxis} === 'true'",
                                    key: 'position',
                                    type: 'radio',
                                    label: '位置',
                                    value: (config as any).position,
                                    config: {
                                        options: [
                                            {label: '左', value: 'left'},
                                            {label: '右', value: 'right'},
                                            {label: '上', value: 'top'},
                                            {label: '下', value: 'bottom'},
                                        ]
                                    }
                                }
                            ]
                        }
                    ]
                },
                {
                    rules:`${enableRef.current}`,
                    children:[
                        {
                            key: 'label',
                            type: 'sub-accordion',
                            label: '文本',
                            children: [
                                {
                                    type: 'grid',
                                    children: [
                                        {
                                            key: 'rotate',
                                            type: 'number-input',
                                            label: '角度',
                                            value: labelData?.rotate || 0,
                                            config: {
                                                min: 0,
                                                step: 0.1,
                                                max: 360,
                                            }
                                        },
                                        {
                                            key: 'offset',
                                            type: 'number-input',
                                            label: '偏移',
                                            value: labelData?.offset || 0,
                                        },
                                        {
                                            key: 'style',
                                            children: [
                                                {
                                                    key: 'fontSize',
                                                    type: 'number-input',
                                                    label: '字号',
                                                    value: (labelData?.style as ShapeAttrs)?.fontSize || 12,
                                                    config: {
                                                        min: 1,
                                                        max: 50,
                                                    }
                                                },
                                                {
                                                    id: 'labelFill',
                                                    key: 'fill',
                                                    type: 'enhanced-color-mode',
                                                    label: '颜色',
                                                    value: (labelData?.style as ShapeAttrs)?.fill || '#1c1c1c',
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            key: 'title',
                            type: 'sub-accordion',
                            label: '标题',
                            children: [
                                {
                                    type: 'grid',
                                    children: [
                                        {
                                            key: 'titleSwitch',
                                            id: 'titleSwitch',
                                            type: 'switch',
                                            label: '开启',
                                            reRender: true,
                                            value: !!titleData,
                                        },
                                        {
                                            rules: "{titleSwitch} === 'true'",
                                            children: [
                                                {
                                                    key: 'position',
                                                    type: 'select',
                                                    label: '位置',
                                                    value: titleData?.position || 'center',
                                                    config: {
                                                        options: [
                                                            {value: 'start', label: '前'},
                                                            {value: 'center', label: '中'},
                                                            {value: 'end', label: '后'}]
                                                    }
                                                },
                                                {
                                                    key: 'text',
                                                    type: 'input',
                                                    label: '内容',
                                                    value: titleData?.text || '标题',
                                                },
                                                {
                                                    key: 'offset',
                                                    type: 'number-input',
                                                    label: '偏移',
                                                    value: titleData?.offset || 0,
                                                },
                                                {
                                                    key: 'style',
                                                    children: [
                                                        {
                                                            key: 'fontSize',
                                                            type: 'number-input',
                                                            label: '字号',
                                                            value: 12,
                                                            config: {
                                                                min: 1,
                                                                max: 50,
                                                            }
                                                        },
                                                        {
                                                            id: 'titleFill',
                                                            key: 'fill',
                                                            type: 'enhanced-color-mode',
                                                            label: '颜色',
                                                            value: '#1c1c1c',
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            key: 'line',
                            type: 'sub-accordion',
                            label: '轴线',
                            children: [
                                {
                                    type: 'grid',
                                    children: [
                                        {
                                            key: 'lineSwitch',
                                            id: 'lineSwitch',
                                            type: 'switch',
                                            label: '开启',
                                            reRender: true,
                                            value: !!lineData,
                                        },
                                        {
                                            key: 'style',
                                            rules: "{lineSwitch} === 'true'",
                                            children: [
                                                {
                                                    key: 'lineWidth',
                                                    type: 'number-input',
                                                    label: '宽度',
                                                    value: lineData?.style?.lineWidth || 1,
                                                    config: {
                                                        min: 0,
                                                        max: 10,
                                                    }
                                                },
                                                {
                                                    id: 'lineStroke',
                                                    key: 'stroke',
                                                    type: 'enhanced-color-mode',
                                                    label: '颜色',
                                                    value: lineData?.style?.stroke || '#595959',
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            key: 'grid',
                            type: 'sub-accordion',
                            label: '网格线',
                            children: [
                                {
                                    type: 'grid',
                                    children: [
                                        {
                                            key: 'gridLineSwitch',
                                            id: 'gridLineSwitch',
                                            type: 'switch',
                                            label: '开启',
                                            reRender: true,
                                            value: !!gridLineData,
                                        },
                                        {
                                            rules: "{gridLineSwitch} === 'true'",
                                            key: 'alignTick',
                                            type: 'switch',
                                            label: '对齐',
                                            value: (gridLineData as any)?.alignTick,
                                        },
                                        {
                                            rules: "{gridLineSwitch} === 'true'",
                                            key: 'line',
                                            children: [
                                                {
                                                    key: 'style',
                                                    children: [
                                                        {
                                                            key: 'lineWidth',
                                                            type: 'number-input',
                                                            label: '宽度',
                                                            value: (gridLineData?.line?.style as ShapeAttrs)?.lineWidth,
                                                            config: {
                                                                min: 0,
                                                                max: 10,
                                                            }
                                                        },
                                                        {
                                                            id: 'gridStroke',
                                                            key: 'stroke',
                                                            type: 'enhanced-color-mode',
                                                            label: '颜色',
                                                            value: (gridLineData?.line?.style as ShapeAttrs)?.stroke,
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            key: 'tickLine',
                            type: 'sub-accordion',
                            label: '刻度线',
                            children: [
                                {
                                    type: 'grid',
                                    children: [
                                        {
                                            key: 'tickLineSwitch',
                                            id: 'tickLineSwitch',
                                            type: 'switch',
                                            label: '开启',
                                            reRender: true,
                                            value: !!tickLineData,
                                        },
                                        {
                                            key: 'alignTick',
                                            rules: "{tickLineSwitch}==='true'",
                                            type: 'switch',
                                            label: '对齐',
                                            value: (tickLineData as any)?.alignTick,
                                        },
                                        {
                                            key: 'length',
                                            rules: "{tickLineSwitch}==='true'",
                                            type: 'number-input',
                                            label: '长度',
                                            value: (tickLineData as any)?.length,
                                            config: {
                                                min: 0,
                                                max: 10,
                                            }
                                        },
                                        {
                                            key: 'style',
                                            rules: "{tickLineSwitch}==='true'",
                                            children: [
                                                {
                                                    key: 'lineWidth',
                                                    type: 'number-input',
                                                    label: '宽度',
                                                    value: (tickLineData?.style as ShapeAttrs)?.lineWidth,
                                                    config: {
                                                        min: 0,
                                                        max: 10,
                                                    }
                                                },
                                                {
                                                    id: 'tickStroke',
                                                    key: 'stroke',
                                                    type: 'enhanced-color-mode',
                                                    label: '颜色',
                                                    value: (tickLineData?.style as ShapeAttrs)?.stroke,
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            key: 'subTickLine',
                            type: 'sub-accordion',
                            label: '子刻度',
                            children: [
                                {
                                    type: 'grid',
                                    children: [
                                        {
                                            key: 'subTickLineSwitch',
                                            id: 'subTickLineSwitch',
                                            type: 'switch',
                                            reRender: true,
                                            label: '开启',
                                            value: !!subTickLineData,
                                        },
                                        {
                                            rules: "{subTickLineSwitch}==='true'",
                                            key: 'count',
                                            type: 'number-input',
                                            label: '数量',
                                            value: subTickLineData?.count,
                                            config: {
                                                min: 0,
                                                max: 100,
                                            }
                                        },
                                        {
                                            rules: "{subTickLineSwitch}==='true'",
                                            key: 'length',
                                            type: 'number-input',
                                            label: '长度',
                                            value: subTickLineData?.length,
                                            config: {
                                                min: 0,
                                                max: 10,
                                            }
                                        },
                                        {
                                            rules: "{subTickLineSwitch}==='true'",
                                            key: 'style',
                                            children: [
                                                {
                                                    key: 'lineWidth',
                                                    type: 'number-input',
                                                    label: '宽度',
                                                    value: (subTickLineData?.style as ShapeAttrs)?.lineWidth,
                                                    config: {
                                                        min: 0,
                                                        max: 10,
                                                    }
                                                },
                                                {
                                                    id: 'subTickStroke',
                                                    key: 'stroke',
                                                    type: 'enhanced-color-mode',
                                                    label: '颜色',
                                                    value: (subTickLineData?.style as ShapeAttrs)?.stroke,
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]

    return (
        <LCGUI schema={schema} onFieldChange={onFieldChange}/>
    )
}

export default AxisConfig;
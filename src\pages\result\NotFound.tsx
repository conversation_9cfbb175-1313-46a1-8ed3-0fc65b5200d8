/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {useNavigate} from "react-router-dom";
import {Button, Result} from "antd";

export default function NotFound() {
    const navigate = useNavigate();
    return (
        <Result
            status="404"
            subTitle="找不到页面"
            extra={<Button type="primary" onClick={() => navigate('/home/<USER>')}>返回首页</Button>}
        />
    );
}
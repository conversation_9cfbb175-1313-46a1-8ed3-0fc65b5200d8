/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import React from 'react';
import {ConfigType} from "../../../designer/right/ConfigContent";
import {Control} from "../../../json-schema/SchemaTypes";
import {FieldChangeData, LCGUI} from "../../../json-schema/LCGUI";
import {BaseTableController} from "./BaseTableController";
import JSXTransformer from "../../../utils/JSXTransformer.ts";
import {Modal, Select} from "antd";

export const BaseTableStyleConfig: React.FC<ConfigType<BaseTableController>> = ({controller}) => {

    const {columns, header, body} = controller.getConfig()?.style || {};
    const [, setCount] = React.useState(0);

    // 列管理相关状态
    const [deleteModalVisible, setDeleteModalVisible] = React.useState(false);
    const [insertModalVisible, setInsertModalVisible] = React.useState(false);
    const [selectedIndex, setSelectedIndex] = React.useState<number>(0);

    // 列管理业务逻辑
    const handleAddColumn = () => {
        if (!columns || columns.length === 0) {
            // 如果没有列，直接添加
            const newColumn = {
                key: 'newColumn',
                label: '新建字段',
                width: 100,
                textAlign: 'center' as const,
                enableCustomCode: false
            };
            controller.update({style: {columns: [newColumn]}});
            setCount(prev => prev + 1);
        } else {
            // 如果有列，显示插入位置选择
            setInsertModalVisible(true);
        }
    };

    const handleDeleteColumn = () => {
        if (!columns || columns.length === 0) return;

        if (columns.length === 1) {
            // 如果只有一列，直接删除
            controller.update({style: {columns: []}});
            setCount(prev => prev + 1);
        } else {
            // 如果有多列，显示删除选择
            setDeleteModalVisible(true);
        }
    };

    const confirmDeleteColumn = () => {
        if (!columns || selectedIndex < 0 || selectedIndex >= columns.length) return;

        const newColumns = [...columns];
        newColumns.splice(selectedIndex, 1);
        controller.update({style: {columns: newColumns}});
        setCount(prev => prev + 1);
        setDeleteModalVisible(false);
    };

    const confirmInsertColumn = () => {
        if (!columns) return;

        const newColumn = {
            key: 'newColumn',
            label: '新建字段',
            width: 100,
            textAlign: 'center' as const,
            enableCustomCode: false
        };

        const newColumns = [...columns];
        newColumns.splice(selectedIndex + 1, 0, newColumn);
        controller.update({style: {columns: newColumns}});
        setCount(prev => prev + 1);
        setInsertModalVisible(false);
    };

    // 生成列选项
    const getColumnOptions = () => {
        if (!columns) return [];
        return columns.map((column, index) => ({
            value: index,
            label: `${column.label || '列'} ${index + 1}`
        }));
    };

    const schema: Control = {
        key: 'style',
        children: [
            {
                key: 'header',
                type: 'accordion',
                label: '表头',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                key: 'height',
                                type: 'number-input',
                                label: '高度',
                                value: header?.height,
                                config: {
                                    min: 0,
                                }
                            },
                            {
                                key: 'background',
                                type: 'color-picker',
                                label: '背景',
                                value: header?.background,
                                config: {
                                    showText: true,
                                }
                            },
                            {
                                key: 'color',
                                type: 'color-picker',
                                label: '字色',
                                value: header?.color,
                                config: {
                                    showText: true,
                                }
                            },
                            {
                                key: 'fontSize',
                                type: 'number-input',
                                label: '字号',
                                value: header?.fontSize,
                                config: {
                                    min: 0,
                                }
                            },
                            {
                                key: 'fontWeight',
                                type: 'number-input',
                                label: '加粗',
                                value: header?.fontWeight,
                                config: {
                                    min: 0,
                                    max: 900,
                                    step: 100
                                }
                            }
                        ]
                    }
                ]
            },
            {
                key: 'body',
                type: 'accordion',
                label: '表体',
                children: [
                    {
                        type: 'grid',
                        children: [
                            {
                                key: 'background',
                                type: 'color-picker',
                                label: '背景',
                                value: body?.background,
                                config: {
                                    showText: true,
                                }
                            },
                            {
                                key: 'color',
                                type: 'color-picker',
                                label: '字色',
                                value: body?.color,
                                config: {
                                    showText: true,
                                }
                            },
                            {
                                type: 'switch',
                                label: '奇偶行',
                                key: 'enableZebra',
                                reRender: true,
                                value: body?.enableZebra,
                            },
                            {
                                rules: "{enableZebra}==='true'",
                                key: 'oddRowColor',
                                type: 'color-picker',
                                label: '奇数行',
                                value: body?.oddRowColor,
                                config: {
                                    showText: true,
                                }
                            },
                            {
                                rules: "{enableZebra}==='true'",
                                key: 'evenRowColor',
                                type: 'color-picker',
                                label: '偶数行',
                                value: body?.evenRowColor,
                                config: {
                                    showText: true,
                                }
                            },
                            {
                                key: 'fontSize',
                                type: 'number-input',
                                label: '字号',
                                value: body?.fontSize,
                                config: {
                                    min: 0,
                                }
                            },
                            {
                                key: 'fontWeight',
                                type: 'number-input',
                                label: '加粗',
                                value: body?.fontWeight,
                                config: {
                                    min: 0,
                                    max: 900,
                                    step: 100
                                }
                            },
                            {
                                type: 'switch',
                                label: '轮播',
                                key: 'enableCarousel',
                                reRender: true,
                                value: body?.enableCarousel,
                            },
                            {
                                rules: "{enableCarousel}==='true'",
                                type: 'number-input',
                                label: '速度',
                                key: 'carouselSpeed',
                                value: body?.carouselSpeed ?? 3,
                                config: {
                                    min: 0.1,
                                    max: 100,
                                    step: 0.1
                                }
                            },
                            {
                                rules: "{enableCarousel}==='true'",
                                type: 'switch',
                                label: '悬停暂停',
                                key: 'pauseOnHover',
                                value: body?.pauseOnHover ?? true,
                            },
                            {
                                rules: "{enableCarousel}==='true'",
                                type: 'select',
                                label: '轮播模式',
                                key: 'carouselMode',
                                reRender: true,
                                value: body?.carouselMode ?? 'step',
                                config: {
                                    options: [
                                        {label: '连续滚动', value: 'continuous'},
                                        {label: '分步滚动', value: 'step'},
                                    ]
                                }
                            },
                            {
                                rules: "{enableCarousel}==='true' && {carouselMode}==='step'",
                                type: 'number-input',
                                label: '每次滚动行数',
                                key: 'slidesToScroll',
                                value: body?.slidesToScroll ?? 1,
                                config: {
                                    min: 1,
                                    max: 10
                                }
                            },
                            {
                                type: 'number-input',
                                label: '页行数',
                                key: 'pageSize',
                                value: body?.pageSize,
                                config: {
                                    min: 0,
                                    max: 100
                                }
                            },
                        ]
                    }
                ]
            },
            {
                key: 'columns',
                type: 'control-group',
                label: '表格列',
                value: columns,
                reRender: true,  // 启用受控模式，确保删除操作后能正确同步
                config: {
                    itemName: '列',
                    onAdd: handleAddColumn,
                    onDelete: handleDeleteColumn,
                    template: {
                        type: 'grid',
                        children: [
                            {
                                key: 'key',
                                type: 'input',
                                label: '列字段名',
                                value: 'newColumn',
                            },
                            {
                                key: 'width',
                                type: 'number-input',
                                label: '列宽',
                                config: {
                                    min: 0,
                                }
                            },
                            {
                                key: 'label',
                                type: 'input',
                                label: '列显示名',
                                value: '新建字段',
                            },
                            {
                                key: 'textAlign',
                                type: 'select',
                                label: '对齐',
                                value: 'center',
                                config: {
                                    options: [
                                        {label: '左对齐', value: 'left'},
                                        {label: '居中', value: 'center'},
                                        {label: '右对齐', value: 'right'},
                                    ]
                                }
                            },
                            {
                                key: 'enableCustomCode',
                                type: 'switch',
                                label: '自定义渲染',
                                value: false,
                                reRender: true,
                            },
                            {
                                key: 'customCode',
                                type: 'code-editor',
                                label: '自定义代码',
                                value: JSXTransformer.getDefaultCustomCode(),
                                rules: '{enableCustomCode} === "true"',
                                config: {
                                    language: 'javascript',
                                    height: 300,
                                    fullScreen: true,
                                    format: true
                                }
                            }
                        ]
                    }
                }
            }
        ]
    };

    const onFieldChange = (fieldChangeData: FieldChangeData) => {
        const {dataFragment, reRender} = fieldChangeData;
        controller.update(dataFragment);
        if (reRender) setCount(count => count + 1);
    }

    return (
        <>
            <LCGUI schema={schema} onFieldChange={onFieldChange}/>

            {/* 删除列选择Modal */}
            <Modal
                title="选择要删除的列"
                open={deleteModalVisible}
                onOk={confirmDeleteColumn}
                onCancel={() => setDeleteModalVisible(false)}
                okText="删除"
                cancelText="取消"
                okButtonProps={{ danger: true }}
            >
                <div style={{ marginBottom: 16 }}>
                    <Select
                        style={{ width: '100%' }}
                        placeholder="请选择要删除的列"
                        value={selectedIndex}
                        onChange={setSelectedIndex}
                        options={getColumnOptions()}
                    />
                </div>
            </Modal>

            {/* 插入列位置选择Modal */}
            <Modal
                title="选择插入位置"
                open={insertModalVisible}
                onOk={confirmInsertColumn}
                onCancel={() => setInsertModalVisible(false)}
                okText="插入"
                cancelText="取消"
            >
                <div style={{ marginBottom: 16 }}>
                    <p>新列将插入到所选列的后面：</p>
                    <Select
                        style={{ width: '100%' }}
                        placeholder="请选择插入位置"
                        value={selectedIndex}
                        onChange={setSelectedIndex}
                        options={getColumnOptions()}
                    />
                </div>
            </Modal>
        </>
    )
}

/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.text-scroller-container {
  --speed: 10s;
  --container-width: 320px;

  width: 100%;
  height: 100%;
  overflow: hidden;
  white-space: nowrap;
  position: relative;

  .text-scroller {
    height: inherit;
    position: absolute;
    color: white;
    animation: scrollText var(--speed) linear infinite;
    display: flex;
    align-items: center;
  }

  @keyframes scrollText {
    0% {
      transform: translateX(var(--container-width));
    }
    100% {
      transform: translateX(-100%);
    }
  }
}



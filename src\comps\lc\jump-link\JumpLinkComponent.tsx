/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {
    CSSProperties,
    ForwardedRef,
    forwardRef,
    useImperativeHandle,
    useRef,
    useState
} from 'react';
import {ComponentBaseProps} from "../../common-component/CommonTypes.ts";
import './JumpLinkComponent.less';
import globalVariableManager from "../../../designer/manager/GlobalVariableManager.ts";
import GlobalVariableParser from "../../../utils/GlobalVariableParser.ts";
import URLUtil from "../../../utils/URLUtil.ts";
import {DesignerMode} from "../../../designer/DesignerType";

export interface JumpLinkComponentStyle {
    color?: string;
    fontSize?: number;
    fontWeight?: number;
    fontFamily?: string;
    alignItems?: string;
    justifyContent?: string;
    textDecoration?: string;
    cursor?: string;
    letterSpacing?: number;
    lineHeight?: number;
}

export interface JumpLinkData {
    text: string;
    url: string;
    target: '_blank' | '_self';
}

export interface JumpLinkComponentProps extends ComponentBaseProps {
    style?: JumpLinkComponentStyle;
}

export interface JumpLinkComponentRef {
    updateConfig: (newConfig: JumpLinkComponentProps) => void;
    setEventHandler: (eventMap: Record<string, () => void>) => void;
}

export const JumpLinkComponent = forwardRef((props: JumpLinkComponentProps, ref: ForwardedRef<JumpLinkComponentRef>) => {
    const [config, setConfig] = useState<JumpLinkComponentProps>({...props});
    const {style, data} = config;
    const eventHandlerMap = useRef<Record<string, () => void>>({});

    useImperativeHandle(ref, () => ({
        updateConfig: (newConfig) => setConfig({...newConfig}),
        setEventHandler: (eventMap) => eventHandlerMap.current = eventMap,
    }));

    const handleClick = async (e: React.MouseEvent) => {
        e.preventDefault();

        // 触发点击事件
        if ('click' in eventHandlerMap.current) {
            eventHandlerMap.current['click']();
        }

        // 检查当前模式，只在预览模式下执行跳转
        const {mode} = URLUtil.parseUrlParams();
        if (mode !== DesignerMode.VIEW) {
            // 在编辑模式下不执行跳转，只触发事件
            return;
        }

        // 处理跳转逻辑
        if (data?.staticData?.url) {
            try {
                // 解析URL中的全局变量
                const parsedUrl = await GlobalVariableParser.parseAndReplace(
                    data.staticData.url,
                    globalVariableManager
                );

                // 执行跳转
                if (data.staticData.target === '_blank') {
                    window.open(parsedUrl, '_blank');
                } else {
                    window.location.href = parsedUrl;
                }
            } catch (error) {
                console.error('跳转链接解析失败:', error);
                // 如果解析失败，使用原始URL
                if (data.staticData.target === '_blank') {
                    window.open(data.staticData.url, '_blank');
                } else {
                    window.location.href = data.staticData.url;
                }
            }
        }
    };

    const linkStyle: CSSProperties = {
        ...style,
        cursor: 'pointer',
        textDecoration: style?.textDecoration || 'underline',
        display: 'flex',
        alignItems: style?.alignItems || 'center',
        justifyContent: style?.justifyContent || 'center',
        height: '100%',
        width: '100%',
        letterSpacing: style?.letterSpacing,
        lineHeight: style?.lineHeight,
    };

    return (
        <div 
            className={'jump-link-component'}
            style={linkStyle}
            onClick={handleClick}
            title={data?.staticData?.url}
        >
            {data?.staticData?.text || '跳转链接'}
        </div>
    );
});

export default JumpLinkComponent;

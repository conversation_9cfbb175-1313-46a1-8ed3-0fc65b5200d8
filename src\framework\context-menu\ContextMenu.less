/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.context-menu {
  background-color: #1b1e23;
  padding: 5px;
  width: 130px;
  min-height: 150px;
  transition: opacity 250ms ease !important;
  z-index: 2;

  .menu-item {
    display: flex;
    color: rgba(206, 206, 206, 0.86);
    font-size: 13px;
    padding: 9px 8px;
    cursor: pointer;

    label {
      margin-right: 10px;
    }

    label, span {
      pointer-events: none;
    }
  }

  .menu-item:hover {
    transition: background-color 0.3s ease;
    background-color: rgba(107, 107, 107, 0.44);
  }
}
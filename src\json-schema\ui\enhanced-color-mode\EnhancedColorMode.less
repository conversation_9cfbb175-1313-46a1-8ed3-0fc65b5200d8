/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by pu<PERSON><PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

.lc-enhanced-color-mode {
  display: flex;
  align-items: flex-start;

  .mode-select {
    margin-right: 10px;
  }

  .multi-color-container {
    flex: 1;

    .multi-color-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      // 不再需要单独的颜色预览样式

      .delete-color-btn {
        margin-left: 8px;
        padding: 2px 6px;
        background-color: #1f1f1f;
        border: 1px solid #434343;
        border-radius: 2px;
        color: #ff4d4f;
        cursor: pointer;
        font-size: 12px;

        &:hover {
          background-color: #2a2a2a;
          border-color: #ff4d4f;
        }
      }
    }

    .add-color-btn {
      margin-top: 8px;
      padding: 4px 8px;
      background-color: #1f1f1f;
      border: 1px solid #434343;
      border-radius: 2px;
      color: #fff;
      cursor: pointer;

      &:hover {
        background-color: #2a2a2a;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

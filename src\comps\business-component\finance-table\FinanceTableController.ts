/*
 * Copyright © 2023-2025 puyinzhen
 * All rights reserved.
 *
 * The copyright of this work (or idea/project/document) is owned by puyi<PERSON><PERSON>. Without explicit written permission, no part of this work may be reproduced, distributed, or modified in any form for commercial purposes.
 *
 * This copyright statement applies to, but is not limited to: concept descriptions, design documents, source code, images, presentation files, and any related content.
 *
 * For permission to use this work or any part of it, <NAME_EMAIL> to obtain written authorization.
 */

import {UpdateOptions} from "../../../framework/core/AbstractController";
import AbstractDesignerController from "../../../framework/core/AbstractDesignerController";
import ComponentUtil from "../../../utils/ComponentUtil";
import FinanceTableComponent, {FinanceTableComponentProps, FinanceTableComponentRef, FinanceTableData} from "./FinanceTableComponent";
import ObjectUtil from "../../../utils/ObjectUtil";

export class FinanceTableController extends AbstractDesignerController<FinanceTableComponentRef, FinanceTableComponentProps> {

    async create(container: HTMLElement, config: FinanceTableComponentProps): Promise<void> {
        this.config = config;
        this.container = container;
        this.instance = await ComponentUtil.createAndRender<FinanceTableComponentRef>(container, FinanceTableComponent, config);
    }

    destroy(): void {
        this.instance?.destroy();
        this.instance = null;
        this.config = null;
    }

    getConfig(): FinanceTableComponentProps | null {
        return this.config;
    }

    changeData(data: FinanceTableData) {
        // 按照标准做法，直接更新 staticData 字段
        if (this.config && this.config.data) {
            this.config.data.staticData = data;
        }
        this.instance?.updateConfig(this.config!);
    }

    update(config: FinanceTableComponentProps, upOp?: UpdateOptions | undefined): void {
        this.config = ObjectUtil.merge(this.config, config);
        upOp = upOp || {reRender: true};
        if (upOp.reRender)
            this.instance?.updateConfig(this.config!);
    }

}